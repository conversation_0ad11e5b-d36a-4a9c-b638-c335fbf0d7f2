"""
Test script for ErgoMotion Lab implementation
Tests core functionality without requiring camera or GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ergonomics.rula_calculator import RULACalculator
from src.ergonomics.reba_calculator import REBACalculator
from src.utils.pose_utils import test_pose_utils
from src.camera.pose import PoseDetector
from src.camera.camera_handler import MockCameraHandler
import numpy as np


def test_rula_calculator():
    """Test RULA calculator functionality"""
    from src.ergonomics.rula_calculator import test_rula_calculator as rula_test_func
    result = rula_test_func()
    assert result is not None, "RULA calculator should return a result"


def test_reba_calculator():
    """Test REBA calculator functionality"""
    from src.ergonomics.reba_calculator import test_reba_calculator as reba_test_func
    result = reba_test_func()
    assert result is not None, "REBA calculator should return a result"


def test_ergonomics_calculations():
    """Test RULA and REBA calculations with various scenarios"""
    print("=" * 50)
    print("TESTING ERGONOMICS CALCULATIONS")
    print("=" * 50)
    
    # Test RULA Calculator
    print("\n1. Testing RULA Calculator:")
    print("-" * 30)
    from src.ergonomics.rula_calculator import test_rula_calculator as rula_test_func
    rula_test_func()
    
    # Test REBA Calculator
    print("\n2. Testing REBA Calculator:")
    print("-" * 30)
    from src.ergonomics.reba_calculator import test_reba_calculator as reba_test_func
    reba_test_func()
    
    # Test with different angle scenarios
    print("\n3. Testing Different Posture Scenarios:")
    print("-" * 40)
    
    rula_calc = RULACalculator()
    reba_calc = REBACalculator()
    
    scenarios = [
        {
            'name': 'Good Posture',
            'angles': {'left_elbow': 90, 'right_elbow': 90, 'neck': 5, 'trunk': 2}
        },
        {
            'name': 'Poor Posture - Forward Head',
            'angles': {'left_elbow': 120, 'right_elbow': 120, 'neck': 25, 'trunk': 15}
        },
        {
            'name': 'Poor Posture - Bent Forward',
            'angles': {'left_elbow': 60, 'right_elbow': 60, 'neck': 30, 'trunk': 45}
        },
        {
            'name': 'Extreme Poor Posture',
            'angles': {'left_elbow': 45, 'right_elbow': 45, 'neck': 40, 'trunk': 70}
        }
    ]
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        rula_score = rula_calc.calculate_score(scenario['angles'])
        reba_score = reba_calc.calculate_score(scenario['angles'])
        
        print(f"  RULA: {rula_score['total_score']} ({rula_score['risk_level']})")
        print(f"  REBA: {reba_score['total_score']} ({reba_score['risk_level']})")
    
    assert True, "Ergonomics calculations completed successfully"


def test_pose_detection():
    """Test pose detection functionality"""
    print("\n" + "=" * 50)
    print("TESTING POSE DETECTION")
    print("=" * 50)
    
    # Test pose detector initialization
    print("\n1. Testing Pose Detector Initialization:")
    print("-" * 40)
    
    pose_detector = PoseDetector()
    model_loaded = pose_detector.load_model()
    print(f"Model loaded: {model_loaded}")
    
    if not model_loaded:
        print("Using mock pose detection (model file not found)")
    
    # Test with mock frame
    print("\n2. Testing Pose Detection on Mock Frame:")
    print("-" * 42)
    
    # Create a mock frame
    mock_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Detect pose
    pose_data = pose_detector.detect_pose(mock_frame)
    
    if pose_data:
        print(f"Keypoints detected: {len(pose_data['keypoints'])}")
        print(f"Average confidence: {sum(pose_data['confidence_scores']) / len(pose_data['confidence_scores']):.2f}")
        
        # Test angle calculation
        angles = pose_detector.get_joint_angles(pose_data)
        print(f"Calculated angles: {list(angles.keys())}")
        
        for angle_name, angle_value in angles.items():
            print(f"  {angle_name}: {angle_value:.1f}°")
    else:
        print("No pose data detected")
    
    assert pose_data is not None, "Pose detection should return data"


def test_camera_handler():
    """Test camera handler functionality"""
    print("\n" + "=" * 50)
    print("TESTING CAMERA HANDLER")
    print("=" * 50)
    
    print("\n1. Testing Mock Camera Handler:")
    print("-" * 35)
    
    # Test mock camera handler
    import asyncio
    
    async def test_mock_camera():
        mock_camera = MockCameraHandler()
        
        # Test initialization
        init_success = await mock_camera.initialize()
        print(f"Mock camera initialized: {init_success}")
        
        if init_success:
            # Test frame capture
            frame_received = False
            
            def frame_callback(frame):
                nonlocal frame_received
                frame_received = True
                print(f"Frame received: {frame.shape}")
            
            # Start capture briefly
            mock_camera.start_capture(frame_callback)
            
            # Wait a bit for frames
            await asyncio.sleep(0.5)
            
            # Stop capture
            mock_camera.stop_capture()
            
            print(f"Frame callback triggered: {frame_received}")
            
            # Test current frame retrieval
            current_frame = mock_camera.get_current_frame()
            if current_frame is not None:
                print(f"Current frame shape: {current_frame.shape}")
            
            # Cleanup
            mock_camera.release()
        
        return init_success
    
    # Run async test
    result = asyncio.run(test_mock_camera())
    assert result, "Camera handler should initialize successfully"


def test_utils():
    """Test utility functions"""
    print("\n" + "=" * 50)
    print("TESTING UTILITY FUNCTIONS")
    print("=" * 50)
    
    test_pose_utils()
    assert True, "Utility functions test completed successfully"


def run_all_tests():
    """Run all tests and report results"""
    print("ERGOMOTION LAB - IMPLEMENTATION TEST")
    print("=" * 60)
    
    test_results = {}
    
    try:
        test_ergonomics_calculations()
        test_results['ergonomics'] = True
    except Exception as e:
        print(f"Ergonomics test failed: {e}")
        test_results['ergonomics'] = False

    try:
        test_pose_detection()
        test_results['pose_detection'] = True
    except Exception as e:
        print(f"Pose detection test failed: {e}")
        test_results['pose_detection'] = False

    try:
        test_camera_handler()
        test_results['camera_handler'] = True
    except Exception as e:
        print(f"Camera handler test failed: {e}")
        test_results['camera_handler'] = False

    try:
        test_utils()
        test_results['utils'] = True
    except Exception as e:
        print(f"Utils test failed: {e}")
        test_results['utils'] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name.upper():<20}: {status}")
    
    all_passed = all(test_results.values())
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    
    print(f"\nOVERALL STATUS: {overall_status}")
    
    return all_passed


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)