"""
Live Camera Interface for ErgoMotion Lab
Real-time camera feed with pose overlay and RULA/REBA status
"""

import flet as ft
import cv2
import numpy as np
import asyncio
import base64
from io import BytesIO
from PIL import Image
from typing import Optional
from src.camera.pose import PoseDetector
from src.ergonomics.rula_calculator import RULACalculator
from src.ergonomics.reba_calculator import REBACalculator


class LiveCameraView:
    """Live camera view with pose detection and ergonomic assessment"""
    
    def __init__(self, page: ft.Page, camera_handler):
        self.page = page
        self.camera_handler = camera_handler
        self.pose_detector = PoseDetector()
        self.rula_calculator = RULACalculator()
        self.reba_calculator = REBACalculator()
        
        # UI components
        self.camera_image: Optional[ft.Image] = None
        self.status_text: Optional[ft.Text] = None
        self.rula_score_text: Optional[ft.Text] = None
        self.reba_score_text: Optional[ft.Text] = None
        
        # State
        self.is_assessing = True  # Start assessment by default
        self.current_pose_data = None
        self.frame_count = 0
        
        # Initialize pose detector
        self.pose_detector.load_model()

    def _create_placeholder_image(self) -> str:
        """Create a placeholder image as base64"""
        try:
            # Create a simple placeholder image
            placeholder = Image.new('RGB', (640, 480), color=(50, 50, 50))
            buffer = BytesIO()
            placeholder.save(buffer, format='JPEG')
            return base64.b64encode(buffer.getvalue()).decode()
        except Exception as e:
            print(f"Failed to create placeholder: {e}")
            return ""

    def build(self) -> ft.Container:
        """Build the live camera view UI"""
        # Create placeholder image
        placeholder_image = self._create_placeholder_image()

        # Camera display
        self.camera_image = ft.Image(
            src_base64=placeholder_image,
            width=640,
            height=480,
            fit=ft.ImageFit.CONTAIN,
            border_radius=ft.border_radius.all(10)
        )
        
        # Status indicators
        self.status_text = ft.Text(
            "Camera Ready - Assessment Active",
            size=16,
            color=ft.Colors.ORANGE,
            weight=ft.FontWeight.W_500
        )

        self.rula_score_text = ft.Text(
            "RULA: --",
            size=14,
            color=ft.Colors.BLUE_800
        )

        self.reba_score_text = ft.Text(
            "REBA: --",
            size=14,
            color=ft.Colors.BLUE_800
        )

        self.debug_text = ft.Text(
            "Pose: Detecting...",
            size=12,
            color=ft.Colors.GREY_600
        )
        
        # Build layout
        return ft.Container(
            content=ft.Column([
                # Camera feed
                ft.Container(
                    content=self.camera_image,
                    alignment=ft.alignment.center,
                    bgcolor=ft.Colors.BLACK12,
                    border_radius=ft.border_radius.all(10),
                    padding=10
                ),
                
                # Status row
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.VIDEOCAM, color=ft.Colors.GREEN, size=20),
                            self.status_text,
                            ft.VerticalDivider(width=20),
                            self.rula_score_text,
                            ft.VerticalDivider(width=20),
                            self.reba_score_text
                        ], alignment=ft.MainAxisAlignment.CENTER),
                        ft.Row([
                            self.debug_text
                        ], alignment=ft.MainAxisAlignment.CENTER)
                    ]),
                    padding=ft.padding.only(top=10)
                )
            ], alignment=ft.MainAxisAlignment.CENTER),
            expand=True
        )
    
    async def start_camera_feed(self):
        """Start the camera feed with pose detection"""
        try:
            self.camera_handler.start_capture(self._process_frame)
            self.status_text.value = "Camera Active"
            self.status_text.color = ft.Colors.GREEN
            self.page.update()
        except Exception as e:
            print(f"Failed to start camera feed: {e}")
            self.status_text.value = "Camera Error"
            self.status_text.color = ft.Colors.RED
            self.page.update()
    
    def _process_frame(self, frame: np.ndarray):
        """Process each camera frame"""
        try:
            self.frame_count += 1

            # Detect pose every few frames for performance
            if self.frame_count % 3 == 0:  # Process every 3rd frame
                self.current_pose_data = self.pose_detector.detect_pose(frame)

                # Update debug info
                if self.current_pose_data:
                    keypoints_count = len(self.current_pose_data['keypoints'])
                    avg_confidence = sum(self.current_pose_data['confidence_scores']) / len(self.current_pose_data['confidence_scores'])
                    self.debug_text.value = f"Pose: {keypoints_count} points, conf: {avg_confidence:.2f}"
                else:
                    self.debug_text.value = "Pose: No detection"

            # Draw pose overlay if available
            display_frame = frame.copy()
            if self.current_pose_data:
                display_frame = self.pose_detector.draw_pose(display_frame, self.current_pose_data)

                # Add frame info overlay
                cv2.putText(display_frame, f"Frame: {self.frame_count}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
                cv2.putText(display_frame, "POSE DETECTED", (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            else:
                cv2.putText(display_frame, f"Frame: {self.frame_count}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
                cv2.putText(display_frame, "NO POSE", (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

            # Perform ergonomic assessment if active
            if self.is_assessing and self.current_pose_data:
                self._perform_assessment()

            # Convert frame to base64 for display
            self._update_camera_display(display_frame)

        except Exception as e:
            print(f"Frame processing error: {e}")
    
    def _perform_assessment(self):
        """Perform RULA/REBA assessment on current pose"""
        try:
            if not self.current_pose_data:
                return
            
            # Get joint angles
            angles = self.pose_detector.get_joint_angles(self.current_pose_data)
            
            # Calculate RULA score
            rula_score = self.rula_calculator.calculate_score(angles)
            
            # Calculate REBA score
            reba_score = self.reba_calculator.calculate_score(angles)
            
            # Update UI (schedule for main thread)
            self._update_scores(rula_score, reba_score)
            
        except Exception as e:
            print(f"Assessment error: {e}")
    
    def _update_scores(self, rula_score: dict, reba_score: dict):
        """Update RULA/REBA scores in UI"""
        try:
            # Update RULA score
            if rula_score and 'total_score' in rula_score:
                score = rula_score['total_score']
                risk_level = rula_score.get('risk_level', 'Unknown')
                self.rula_score_text.value = f"RULA: {score} ({risk_level})"
                
                # Color based on risk level
                if risk_level == 'Low':
                    self.rula_score_text.color = ft.Colors.GREEN
                elif risk_level == 'Medium':
                    self.rula_score_text.color = ft.Colors.ORANGE
                else:
                    self.rula_score_text.color = ft.Colors.RED
            
            # Update REBA score
            if reba_score and 'total_score' in reba_score:
                score = reba_score['total_score']
                risk_level = reba_score.get('risk_level', 'Unknown')
                self.reba_score_text.value = f"REBA: {score} ({risk_level})"
                
                # Color based on risk level
                if risk_level == 'Low':
                    self.reba_score_text.color = ft.Colors.GREEN
                elif risk_level == 'Medium':
                    self.reba_score_text.color = ft.Colors.ORANGE
                else:
                    self.reba_score_text.color = ft.Colors.RED
            
            self.page.update()
            
        except Exception as e:
            print(f"Score update error: {e}")
    
    def _update_camera_display(self, frame: np.ndarray):
        """Update camera display with processed frame"""
        try:
            # Resize frame for display
            display_frame = cv2.resize(frame, (640, 480))
            
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(rgb_frame)
            
            # Convert to base64
            buffer = BytesIO()
            pil_image.save(buffer, format='JPEG', quality=85)
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Update image source
            self.camera_image.src_base64 = img_base64
            
            # Schedule UI update
            self._update_display()
            
        except Exception as e:
            print(f"Display update error: {e}")
    
    def _update_display(self):
        """Update the display in main thread"""
        try:
            self.page.update()
        except Exception as e:
            print(f"UI update error: {e}")
    
    async def start_assessment(self):
        """Start ergonomic assessment"""
        self.is_assessing = True
        self.status_text.value = "Assessing..."
        self.status_text.color = ft.Colors.ORANGE
        self.page.update()
    
    async def stop_assessment(self):
        """Stop ergonomic assessment"""
        self.is_assessing = False
        self.status_text.value = "Camera Active"
        self.status_text.color = ft.Colors.GREEN
        self.rula_score_text.value = "RULA: --"
        self.reba_score_text.value = "REBA: --"
        self.rula_score_text.color = ft.Colors.BLUE_800
        self.reba_score_text.color = ft.Colors.BLUE_800
        self.page.update()
    
    def cleanup(self):
        """Cleanup resources"""
        self.camera_handler.stop_capture()