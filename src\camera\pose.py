"""
Pose Detection using MoveNet for ErgoMotion Lab
Handles offline TFLite model loading and pose keypoint extraction
"""

import numpy as np
import cv2
from typing import Optional, Dict
import os

# Handle TensorFlow import gracefully for Python 3.13 compatibility
try:
    import tensorflow as tf
    TF_AVAILABLE = True
except ImportError:
    print("TensorFlow not available - using mock pose detection")
    TF_AVAILABLE = False
    tf = None


class PoseDetector:
    """MoveNet pose detector for ergonomic assessment"""
    
    # MoveNet keypoint indices (17 keypoints)
    KEYPOINT_NAMES = [
        'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
        'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
        'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
        'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
    ]
    
    # Keypoint connections for visualization
    CONNECTIONS = [
        (0, 1), (0, 2), (1, 3), (2, 4),  # Head
        (5, 6), (5, 7), (7, 9), (6, 8), (8, 10),  # Arms
        (5, 11), (6, 12), (11, 12),  # Torso
        (11, 13), (13, 15), (12, 14), (14, 16)  # Legs
    ]
    
    def __init__(self, model_path: str = "assets/models/movenet_thunder.tflite"):
        self.model_path = model_path
        self.interpreter: Optional[tf.lite.Interpreter] = None
        self.input_details = None
        self.output_details = None
        self.input_size = 256  # MoveNet input size
        self.confidence_threshold = 0.3
        
    def load_model(self) -> bool:
        """Load MoveNet TFLite model"""
        try:
            if not TF_AVAILABLE:
                print("TensorFlow not available - using mock pose detection")
                return False
                
            if not os.path.exists(self.model_path):
                print(f"Model file not found: {self.model_path}")
                print("Using mock pose detection")
                return False
            
            # Load TFLite model
            self.interpreter = tf.lite.Interpreter(model_path=self.model_path)
            self.interpreter.allocate_tensors()
            
            # Get input and output details
            self.input_details = self.interpreter.get_input_details()
            self.output_details = self.interpreter.get_output_details()
            
            print("MoveNet model loaded successfully")
            print(f"Input shape: {self.input_details[0]['shape']}")
            print(f"Output shape: {self.output_details[0]['shape']}")
            
            return True
            
        except Exception as e:
            print(f"Failed to load MoveNet model: {e}")
            return False
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for MoveNet input"""
        # Resize to model input size
        resized = cv2.resize(frame, (self.input_size, self.input_size))
        
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1] and add batch dimension
        input_tensor = np.expand_dims(rgb_frame.astype(np.float32) / 255.0, axis=0)
        
        return input_tensor
    
    def detect_pose(self, frame: np.ndarray) -> Optional[Dict]:
        """Detect pose keypoints in frame"""
        if self.interpreter is None:
            return self._mock_pose_detection(frame)
        
        try:
            # Preprocess frame
            input_tensor = self.preprocess_frame(frame)
            
            # Run inference
            self.interpreter.set_tensor(self.input_details[0]['index'], input_tensor)
            self.interpreter.invoke()
            
            # Get output
            keypoints_with_scores = self.interpreter.get_tensor(self.output_details[0]['index'])
            
            # Extract keypoints (shape: [1, 1, 17, 3] -> [17, 3])
            keypoints = keypoints_with_scores[0, 0, :, :]
            
            # Convert to frame coordinates
            h, w = frame.shape[:2]
            pose_data = {
                'keypoints': [],
                'confidence_scores': [],
                'frame_shape': (h, w)
            }
            
            for i, (y, x, confidence) in enumerate(keypoints):
                # Convert normalized coordinates to pixel coordinates
                pixel_x = int(x * w)
                pixel_y = int(y * h)
                
                pose_data['keypoints'].append((pixel_x, pixel_y))
                pose_data['confidence_scores'].append(float(confidence))
            
            return pose_data
            
        except Exception as e:
            print(f"Pose detection error: {e}")
            return None
    
    def _mock_pose_detection(self, frame: np.ndarray) -> Dict:
        """Generate pose data based on motion detection in the frame"""
        h, w = frame.shape[:2]

        # Simple motion detection to find center of movement
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Use frame center as fallback
        center_x, center_y = w//2, h//2

        # Try to detect motion/contours for more realistic positioning
        try:
            # Apply blur and threshold to find motion areas
            blurred = cv2.GaussianBlur(gray, (21, 21), 0)

            # Find contours (areas of interest)
            edges = cv2.Canny(blurred, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Find largest contour (likely to be person)
                largest_contour = max(contours, key=cv2.contourArea)
                if cv2.contourArea(largest_contour) > 1000:  # Minimum size threshold
                    # Get bounding box of largest contour
                    x, y, cw, ch = cv2.boundingRect(largest_contour)
                    center_x = x + cw//2
                    center_y = y + ch//2

        except Exception as e:
            print(f"Motion detection error: {e}")

        # Generate realistic pose keypoints based on detected center
        # Scale based on detected area size
        scale = min(w, h) // 8  # Adaptive scaling

        mock_keypoints = [
            (center_x, center_y - scale*2),           # nose
            (center_x-scale//3, center_y - scale*2-10), # left_eye
            (center_x+scale//3, center_y - scale*2-10), # right_eye
            (center_x-scale//2, center_y - scale*2-5),  # left_ear
            (center_x+scale//2, center_y - scale*2-5),  # right_ear
            (center_x-scale, center_y - scale),         # left_shoulder
            (center_x+scale, center_y - scale),         # right_shoulder
            (center_x-scale*2, center_y),               # left_elbow
            (center_x+scale*2, center_y),               # right_elbow
            (center_x-scale*2-20, center_y+scale//2),   # left_wrist
            (center_x+scale*2+20, center_y+scale//2),   # right_wrist
            (center_x-scale//2, center_y+scale//2),     # left_hip
            (center_x+scale//2, center_y+scale//2),     # right_hip
            (center_x-scale//2-10, center_y+scale*2),   # left_knee
            (center_x+scale//2+10, center_y+scale*2),   # right_knee
            (center_x-scale//2-20, center_y+scale*3),   # left_ankle
            (center_x+scale//2+20, center_y+scale*3),   # right_ankle
        ]

        # Ensure keypoints are within frame bounds
        mock_keypoints = [(max(10, min(w-10, x)), max(10, min(h-10, y))) for x, y in mock_keypoints]

        return {
            'keypoints': mock_keypoints,
            'confidence_scores': [0.8] * 17,  # High confidence for all points
            'frame_shape': (h, w),
            'detection_center': (center_x, center_y)
        }
    
    def draw_pose(self, frame: np.ndarray, pose_data: Dict) -> np.ndarray:
        """Draw pose keypoints and connections on frame"""
        if not pose_data:
            return frame

        keypoints = pose_data['keypoints']
        confidences = pose_data['confidence_scores']

        # Draw detection center if available
        if 'detection_center' in pose_data:
            center = pose_data['detection_center']
            cv2.circle(frame, center, 20, (255, 255, 0), 3)  # Yellow circle for detection center
            cv2.putText(frame, "MOTION CENTER", (center[0]-50, center[1]-30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        # Draw connections with thicker lines
        for start_idx, end_idx in self.CONNECTIONS:
            if (confidences[start_idx] > self.confidence_threshold and
                confidences[end_idx] > self.confidence_threshold):

                start_point = keypoints[start_idx]
                end_point = keypoints[end_idx]

                # Thicker, more visible lines with different colors
                if start_idx < 5 or end_idx < 5:  # Head connections
                    color = (255, 100, 100)  # Light blue
                elif start_idx < 11 or end_idx < 11:  # Arm connections
                    color = (100, 255, 100)  # Light green
                else:  # Leg connections
                    color = (100, 100, 255)  # Light red

                cv2.line(frame, start_point, end_point, color, 5)

        # Draw keypoints with larger circles
        for i, (x, y) in enumerate(keypoints):
            if confidences[i] > self.confidence_threshold:
                # Color based on keypoint type
                if i < 5:  # Head keypoints
                    color = (255, 0, 0)  # Blue
                    label_color = (255, 255, 255)
                elif i < 11:  # Arm keypoints
                    color = (0, 255, 0)  # Green
                    label_color = (0, 0, 0)
                else:  # Leg keypoints
                    color = (0, 0, 255)  # Red
                    label_color = (255, 255, 255)

                # Larger, more visible circles
                cv2.circle(frame, (x, y), 10, color, -1)
                cv2.circle(frame, (x, y), 15, (255, 255, 255), 3)

                # Add keypoint labels
                cv2.putText(frame, str(i), (x+20, y-20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, label_color, 2)

        # Add pose info
        cv2.putText(frame, f"KEYPOINTS: {len(keypoints)}", (10, frame.shape[0]-60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
        cv2.putText(frame, f"AVG CONF: {sum(confidences)/len(confidences):.2f}", (10, frame.shape[0]-30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)

        return frame
    
    def get_joint_angles(self, pose_data: Dict) -> Dict[str, float]:
        """Calculate joint angles from pose keypoints for RULA/REBA"""
        if not pose_data:
            return {}
        
        keypoints = pose_data['keypoints']
        angles = {}
        
        try:
            # Helper function to calculate angle between three points
            def calculate_angle(p1, p2, p3):
                """Calculate angle at p2 formed by p1-p2-p3"""
                v1 = np.array(p1) - np.array(p2)
                v2 = np.array(p3) - np.array(p2)
                
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                cos_angle = np.clip(cos_angle, -1.0, 1.0)
                angle = np.arccos(cos_angle)
                return np.degrees(angle)
            
            # Calculate key angles for RULA/REBA
            # Left arm angles
            if all(i < len(keypoints) for i in [5, 7, 9]):  # shoulder, elbow, wrist
                angles['left_elbow'] = calculate_angle(keypoints[5], keypoints[7], keypoints[9])
            
            # Right arm angles
            if all(i < len(keypoints) for i in [6, 8, 10]):
                angles['right_elbow'] = calculate_angle(keypoints[6], keypoints[8], keypoints[10])
            
            # Neck angle (simplified)
            if all(i < len(keypoints) for i in [0, 5, 6]):  # nose, shoulders
                neck_center = ((keypoints[5][0] + keypoints[6][0]) // 2,
                              (keypoints[5][1] + keypoints[6][1]) // 2)
                angles['neck'] = calculate_angle(keypoints[0], neck_center, (neck_center[0], neck_center[1] + 100))
            
            # Trunk angle (simplified)
            if all(i < len(keypoints) for i in [5, 6, 11, 12]):  # shoulders, hips
                shoulder_center = ((keypoints[5][0] + keypoints[6][0]) // 2,
                                 (keypoints[5][1] + keypoints[6][1]) // 2)
                hip_center = ((keypoints[11][0] + keypoints[12][0]) // 2,
                             (keypoints[11][1] + keypoints[12][1]) // 2)
                angles['trunk'] = calculate_angle(shoulder_center, hip_center, 
                                                (hip_center[0], hip_center[1] + 100))
            
        except Exception as e:
            print(f"Angle calculation error: {e}")
        
        return angles