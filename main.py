"""
ErgoMotion Lab - Main Entry Point
Flet app for ergonomic assessments using camera and pose detection
"""

import flet as ft
import asyncio
from src.ui.ui_live import LiveCameraView
from src.camera.camera_handler import CameraHandler


class ErgoMotionApp:
    def __init__(self):
        self.camera_handler = CameraHandler()
        self.live_view = None
        
    async def main(self, page: ft.Page):
        """Main app entry point"""
        # Configure page
        page.title = "ErgoMotion Lab"
        page.theme_mode = ft.ThemeMode.LIGHT
        page.vertical_alignment = ft.MainAxisAlignment.CENTER
        page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
        page.padding = 20
        
        # Initialize live camera view
        self.live_view = LiveCameraView(page, self.camera_handler)
        
        # Create main layout
        main_container = ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Text(
                        "ErgoMotion Lab",
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_800
                    ),
                    alignment=ft.alignment.center,
                    padding=ft.padding.only(bottom=20)
                ),
                
                # Status indicator
                ft.Container(
                    content=ft.Row([
                        ft.Icon(
                            ft.Icons.CAMERA_ALT,
                            color=ft.Colors.GREEN,
                            size=24
                        ),
                        ft.Text(
                            "RULA Ready",
                            size=18,
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.GREEN
                        )
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    padding=ft.padding.only(bottom=20)
                ),
                
                # Camera view container
                self.live_view.build(),
                
                # Control buttons
                ft.Container(
                    content=ft.Row([
                        ft.ElevatedButton(
                            "Start Assessment",
                            icon=ft.Icons.PLAY_ARROW,
                            on_click=self.start_assessment,
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            "Stop",
                            icon=ft.Icons.STOP,
                            on_click=self.stop_assessment,
                            bgcolor=ft.Colors.RED_600,
                            color=ft.Colors.WHITE
                        )
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    padding=ft.padding.only(top=20)
                )
            ], alignment=ft.MainAxisAlignment.CENTER),
            expand=True
        )
        
        # Add to page
        page.add(main_container)
        
        # Initialize camera
        await self.initialize_camera()
    
    async def initialize_camera(self):
        """Initialize camera and start feed"""
        try:
            success = await self.camera_handler.initialize()
            if success:
                await self.live_view.start_camera_feed()
            else:
                print("Failed to initialize camera")
        except Exception as e:
            print(f"Camera initialization error: {e}")
    
    async def start_assessment(self, e):
        """Start RULA/REBA assessment"""
        await self.live_view.start_assessment()
    
    async def stop_assessment(self, e):
        """Stop assessment"""
        await self.live_view.stop_assessment()


async def main(page: ft.Page):
    """Flet app entry point"""
    app = ErgoMotionApp()
    await app.main(page)


if __name__ == "__main__":
    ft.app(target=main)