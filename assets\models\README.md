# MoveNet Model Directory

This directory should contain the MoveNet TFLite model for pose detection.

## Required Model
- **File**: `movenet_thunder.tflite`
- **Size**: ~7MB
- **Source**: TensorFlow Hub - MoveNet Thunder
- **URL**: https://tfhub.dev/google/movenet/singlepose/thunder/4

## Download Instructions

1. Download the MoveNet Thunder model from TensorFlow Hub
2. Convert to TFLite format if needed
3. Place the `.tflite` file in this directory
4. Ensure the filename matches `movenet_thunder.tflite`

## Model Specifications
- **Input**: 256x256x3 RGB image
- **Output**: 17 keypoints with confidence scores
- **Keypoints**: Standard COCO pose keypoints
- **Performance**: Optimized for accuracy over speed

## Fallback Behavior
If the model file is not found, the application will use mock pose detection for testing purposes.