"""
Flet Material Icons

To generate/update these values run:

sh ci/generate_material_icons_python.sh
"""

import random
from enum import Enum
from typing import Dict, List, Optional


class Icons(str, Enum):
    @staticmethod
    def random(
        exclude: Optional[List["Icons"]] = None,
        weights: Optional[Dict["Icons", int]] = None,
    ) -> Optional["Icons"]:
        """
        Selects a random icon, with optional exclusions and weights.

        Args:
            exclude: A list of icons members to exclude from the selection.
            weights: A dictionary mapping icon members to their respective weights for weighted random selection.

        Returns:
            A randomly selected icon, or None if all members are excluded.
        """
        choices = list(Icons)
        if exclude:
            choices = [member for member in choices if member not in exclude]
            if not choices:
                return None
        if weights:
            weights_list = [weights.get(c, 1) for c in choices]
            return random.choices(choices, weights=weights_list)[0]
        return random.choice(choices)

    TEN_K = "ten_k"
    TEN_K_SHARP = "ten_k_sharp"
    TEN_K_ROUNDED = "ten_k_rounded"
    TEN_K_OUTLINED = "ten_k_outlined"
    TEN_MP = "ten_mp"
    TEN_MP_SHARP = "ten_mp_sharp"
    TEN_MP_ROUNDED = "ten_mp_rounded"
    TEN_MP_OUTLINED = "ten_mp_outlined"
    ELEVEN_MP = "eleven_mp"
    ELEVEN_MP_SHARP = "eleven_mp_sharp"
    ELEVEN_MP_ROUNDED = "eleven_mp_rounded"
    ELEVEN_MP_OUTLINED = "eleven_mp_outlined"
    ONETWOTHREE = "onetwothree"
    ONETWOTHREE_SHARP = "onetwothree_sharp"
    ONETWOTHREE_ROUNDED = "onetwothree_rounded"
    ONETWOTHREE_OUTLINED = "onetwothree_outlined"
    TWELVE_MP = "twelve_mp"
    TWELVE_MP_SHARP = "twelve_mp_sharp"
    TWELVE_MP_ROUNDED = "twelve_mp_rounded"
    TWELVE_MP_OUTLINED = "twelve_mp_outlined"
    THIRTEEN_MP = "thirteen_mp"
    THIRTEEN_MP_SHARP = "thirteen_mp_sharp"
    THIRTEEN_MP_ROUNDED = "thirteen_mp_rounded"
    THIRTEEN_MP_OUTLINED = "thirteen_mp_outlined"
    FOURTEEN_MP = "fourteen_mp"
    FOURTEEN_MP_SHARP = "fourteen_mp_sharp"
    FOURTEEN_MP_ROUNDED = "fourteen_mp_rounded"
    FOURTEEN_MP_OUTLINED = "fourteen_mp_outlined"
    FIFTEEN_MP = "fifteen_mp"
    FIFTEEN_MP_SHARP = "fifteen_mp_sharp"
    FIFTEEN_MP_ROUNDED = "fifteen_mp_rounded"
    FIFTEEN_MP_OUTLINED = "fifteen_mp_outlined"
    SIXTEEN_MP = "sixteen_mp"
    SIXTEEN_MP_SHARP = "sixteen_mp_sharp"
    SIXTEEN_MP_ROUNDED = "sixteen_mp_rounded"
    SIXTEEN_MP_OUTLINED = "sixteen_mp_outlined"
    SEVENTEEN_MP = "seventeen_mp"
    SEVENTEEN_MP_SHARP = "seventeen_mp_sharp"
    SEVENTEEN_MP_ROUNDED = "seventeen_mp_rounded"
    SEVENTEEN_MP_OUTLINED = "seventeen_mp_outlined"
    EIGHTEEN_UP_RATING = "eighteen_up_rating"
    EIGHTEEN_UP_RATING_SHARP = "eighteen_up_rating_sharp"
    EIGHTEEN_UP_RATING_ROUNDED = "eighteen_up_rating_rounded"
    EIGHTEEN_UP_RATING_OUTLINED = "eighteen_up_rating_outlined"
    EIGHTEEN_MP = "eighteen_mp"
    EIGHTEEN_MP_SHARP = "eighteen_mp_sharp"
    EIGHTEEN_MP_ROUNDED = "eighteen_mp_rounded"
    EIGHTEEN_MP_OUTLINED = "eighteen_mp_outlined"
    NINETEEN_MP = "nineteen_mp"
    NINETEEN_MP_SHARP = "nineteen_mp_sharp"
    NINETEEN_MP_ROUNDED = "nineteen_mp_rounded"
    NINETEEN_MP_OUTLINED = "nineteen_mp_outlined"
    ONE_K = "one_k"
    ONE_K_SHARP = "one_k_sharp"
    ONE_K_ROUNDED = "one_k_rounded"
    ONE_K_OUTLINED = "one_k_outlined"
    ONE_K_PLUS = "one_k_plus"
    ONE_K_PLUS_SHARP = "one_k_plus_sharp"
    ONE_K_PLUS_ROUNDED = "one_k_plus_rounded"
    ONE_K_PLUS_OUTLINED = "one_k_plus_outlined"
    ONE_X_MOBILEDATA = "one_x_mobiledata"
    ONE_X_MOBILEDATA_SHARP = "one_x_mobiledata_sharp"
    ONE_X_MOBILEDATA_ROUNDED = "one_x_mobiledata_rounded"
    ONE_X_MOBILEDATA_OUTLINED = "one_x_mobiledata_outlined"
    TWENTY_MP = "twenty_mp"
    TWENTY_MP_SHARP = "twenty_mp_sharp"
    TWENTY_MP_ROUNDED = "twenty_mp_rounded"
    TWENTY_MP_OUTLINED = "twenty_mp_outlined"
    TWENTY_ONE_MP = "twenty_one_mp"
    TWENTY_ONE_MP_SHARP = "twenty_one_mp_sharp"
    TWENTY_ONE_MP_ROUNDED = "twenty_one_mp_rounded"
    TWENTY_ONE_MP_OUTLINED = "twenty_one_mp_outlined"
    TWENTY_TWO_MP = "twenty_two_mp"
    TWENTY_TWO_MP_SHARP = "twenty_two_mp_sharp"
    TWENTY_TWO_MP_ROUNDED = "twenty_two_mp_rounded"
    TWENTY_TWO_MP_OUTLINED = "twenty_two_mp_outlined"
    TWENTY_THREE_MP = "twenty_three_mp"
    TWENTY_THREE_MP_SHARP = "twenty_three_mp_sharp"
    TWENTY_THREE_MP_ROUNDED = "twenty_three_mp_rounded"
    TWENTY_THREE_MP_OUTLINED = "twenty_three_mp_outlined"
    TWENTY_FOUR_MP = "twenty_four_mp"
    TWENTY_FOUR_MP_SHARP = "twenty_four_mp_sharp"
    TWENTY_FOUR_MP_ROUNDED = "twenty_four_mp_rounded"
    TWENTY_FOUR_MP_OUTLINED = "twenty_four_mp_outlined"
    TWO_K = "two_k"
    TWO_K_SHARP = "two_k_sharp"
    TWO_K_ROUNDED = "two_k_rounded"
    TWO_K_OUTLINED = "two_k_outlined"
    TWO_K_PLUS = "two_k_plus"
    TWO_K_PLUS_SHARP = "two_k_plus_sharp"
    TWO_K_PLUS_ROUNDED = "two_k_plus_rounded"
    TWO_K_PLUS_OUTLINED = "two_k_plus_outlined"
    TWO_MP = "two_mp"
    TWO_MP_SHARP = "two_mp_sharp"
    TWO_MP_ROUNDED = "two_mp_rounded"
    TWO_MP_OUTLINED = "two_mp_outlined"
    THIRTY_FPS = "thirty_fps"
    THIRTY_FPS_SHARP = "thirty_fps_sharp"
    THIRTY_FPS_ROUNDED = "thirty_fps_rounded"
    THIRTY_FPS_OUTLINED = "thirty_fps_outlined"
    THIRTY_FPS_SELECT = "thirty_fps_select"
    THIRTY_FPS_SELECT_SHARP = "thirty_fps_select_sharp"
    THIRTY_FPS_SELECT_ROUNDED = "thirty_fps_select_rounded"
    THIRTY_FPS_SELECT_OUTLINED = "thirty_fps_select_outlined"
    THREESIXTY = "threesixty"
    THREESIXTY_SHARP = "threesixty_sharp"
    THREESIXTY_ROUNDED = "threesixty_rounded"
    THREESIXTY_OUTLINED = "threesixty_outlined"
    THREED_ROTATION = "threed_rotation"
    THREED_ROTATION_SHARP = "threed_rotation_sharp"
    THREED_ROTATION_ROUNDED = "threed_rotation_rounded"
    THREED_ROTATION_OUTLINED = "threed_rotation_outlined"
    THREE_G_MOBILEDATA = "three_g_mobiledata"
    THREE_G_MOBILEDATA_SHARP = "three_g_mobiledata_sharp"
    THREE_G_MOBILEDATA_ROUNDED = "three_g_mobiledata_rounded"
    THREE_G_MOBILEDATA_OUTLINED = "three_g_mobiledata_outlined"
    THREE_K = "three_k"
    THREE_K_SHARP = "three_k_sharp"
    THREE_K_ROUNDED = "three_k_rounded"
    THREE_K_OUTLINED = "three_k_outlined"
    THREE_K_PLUS = "three_k_plus"
    THREE_K_PLUS_SHARP = "three_k_plus_sharp"
    THREE_K_PLUS_ROUNDED = "three_k_plus_rounded"
    THREE_K_PLUS_OUTLINED = "three_k_plus_outlined"
    THREE_MP = "three_mp"
    THREE_MP_SHARP = "three_mp_sharp"
    THREE_MP_ROUNDED = "three_mp_rounded"
    THREE_MP_OUTLINED = "three_mp_outlined"
    THREE_P = "three_p"
    THREE_P_SHARP = "three_p_sharp"
    THREE_P_ROUNDED = "three_p_rounded"
    THREE_P_OUTLINED = "three_p_outlined"
    FOUR_G_MOBILEDATA = "four_g_mobiledata"
    FOUR_G_MOBILEDATA_SHARP = "four_g_mobiledata_sharp"
    FOUR_G_MOBILEDATA_ROUNDED = "four_g_mobiledata_rounded"
    FOUR_G_MOBILEDATA_OUTLINED = "four_g_mobiledata_outlined"
    FOUR_G_PLUS_MOBILEDATA = "four_g_plus_mobiledata"
    FOUR_G_PLUS_MOBILEDATA_SHARP = "four_g_plus_mobiledata_sharp"
    FOUR_G_PLUS_MOBILEDATA_ROUNDED = "four_g_plus_mobiledata_rounded"
    FOUR_G_PLUS_MOBILEDATA_OUTLINED = "four_g_plus_mobiledata_outlined"
    FOUR_K = "four_k"
    FOUR_K_SHARP = "four_k_sharp"
    FOUR_K_ROUNDED = "four_k_rounded"
    FOUR_K_OUTLINED = "four_k_outlined"
    FOUR_K_PLUS = "four_k_plus"
    FOUR_K_PLUS_SHARP = "four_k_plus_sharp"
    FOUR_K_PLUS_ROUNDED = "four_k_plus_rounded"
    FOUR_K_PLUS_OUTLINED = "four_k_plus_outlined"
    FOUR_MP = "four_mp"
    FOUR_MP_SHARP = "four_mp_sharp"
    FOUR_MP_ROUNDED = "four_mp_rounded"
    FOUR_MP_OUTLINED = "four_mp_outlined"
    FIVE_G = "five_g"
    FIVE_G_SHARP = "five_g_sharp"
    FIVE_G_ROUNDED = "five_g_rounded"
    FIVE_G_OUTLINED = "five_g_outlined"
    FIVE_K = "five_k"
    FIVE_K_SHARP = "five_k_sharp"
    FIVE_K_ROUNDED = "five_k_rounded"
    FIVE_K_OUTLINED = "five_k_outlined"
    FIVE_K_PLUS = "five_k_plus"
    FIVE_K_PLUS_SHARP = "five_k_plus_sharp"
    FIVE_K_PLUS_ROUNDED = "five_k_plus_rounded"
    FIVE_K_PLUS_OUTLINED = "five_k_plus_outlined"
    FIVE_MP = "five_mp"
    FIVE_MP_SHARP = "five_mp_sharp"
    FIVE_MP_ROUNDED = "five_mp_rounded"
    FIVE_MP_OUTLINED = "five_mp_outlined"
    SIXTY_FPS = "sixty_fps"
    SIXTY_FPS_SHARP = "sixty_fps_sharp"
    SIXTY_FPS_ROUNDED = "sixty_fps_rounded"
    SIXTY_FPS_OUTLINED = "sixty_fps_outlined"
    SIXTY_FPS_SELECT = "sixty_fps_select"
    SIXTY_FPS_SELECT_SHARP = "sixty_fps_select_sharp"
    SIXTY_FPS_SELECT_ROUNDED = "sixty_fps_select_rounded"
    SIXTY_FPS_SELECT_OUTLINED = "sixty_fps_select_outlined"
    SIX_FT_APART = "six_ft_apart"
    SIX_FT_APART_SHARP = "six_ft_apart_sharp"
    SIX_FT_APART_ROUNDED = "six_ft_apart_rounded"
    SIX_FT_APART_OUTLINED = "six_ft_apart_outlined"
    SIX_K = "six_k"
    SIX_K_SHARP = "six_k_sharp"
    SIX_K_ROUNDED = "six_k_rounded"
    SIX_K_OUTLINED = "six_k_outlined"
    SIX_K_PLUS = "six_k_plus"
    SIX_K_PLUS_SHARP = "six_k_plus_sharp"
    SIX_K_PLUS_ROUNDED = "six_k_plus_rounded"
    SIX_K_PLUS_OUTLINED = "six_k_plus_outlined"
    SIX_MP = "six_mp"
    SIX_MP_SHARP = "six_mp_sharp"
    SIX_MP_ROUNDED = "six_mp_rounded"
    SIX_MP_OUTLINED = "six_mp_outlined"
    SEVEN_K = "seven_k"
    SEVEN_K_SHARP = "seven_k_sharp"
    SEVEN_K_ROUNDED = "seven_k_rounded"
    SEVEN_K_OUTLINED = "seven_k_outlined"
    SEVEN_K_PLUS = "seven_k_plus"
    SEVEN_K_PLUS_SHARP = "seven_k_plus_sharp"
    SEVEN_K_PLUS_ROUNDED = "seven_k_plus_rounded"
    SEVEN_K_PLUS_OUTLINED = "seven_k_plus_outlined"
    SEVEN_MP = "seven_mp"
    SEVEN_MP_SHARP = "seven_mp_sharp"
    SEVEN_MP_ROUNDED = "seven_mp_rounded"
    SEVEN_MP_OUTLINED = "seven_mp_outlined"
    EIGHT_K = "eight_k"
    EIGHT_K_SHARP = "eight_k_sharp"
    EIGHT_K_ROUNDED = "eight_k_rounded"
    EIGHT_K_OUTLINED = "eight_k_outlined"
    EIGHT_K_PLUS = "eight_k_plus"
    EIGHT_K_PLUS_SHARP = "eight_k_plus_sharp"
    EIGHT_K_PLUS_ROUNDED = "eight_k_plus_rounded"
    EIGHT_K_PLUS_OUTLINED = "eight_k_plus_outlined"
    EIGHT_MP = "eight_mp"
    EIGHT_MP_SHARP = "eight_mp_sharp"
    EIGHT_MP_ROUNDED = "eight_mp_rounded"
    EIGHT_MP_OUTLINED = "eight_mp_outlined"
    NINE_K = "nine_k"
    NINE_K_SHARP = "nine_k_sharp"
    NINE_K_ROUNDED = "nine_k_rounded"
    NINE_K_OUTLINED = "nine_k_outlined"
    NINE_K_PLUS = "nine_k_plus"
    NINE_K_PLUS_SHARP = "nine_k_plus_sharp"
    NINE_K_PLUS_ROUNDED = "nine_k_plus_rounded"
    NINE_K_PLUS_OUTLINED = "nine_k_plus_outlined"
    NINE_MP = "nine_mp"
    NINE_MP_SHARP = "nine_mp_sharp"
    NINE_MP_ROUNDED = "nine_mp_rounded"
    NINE_MP_OUTLINED = "nine_mp_outlined"
    ABC = "abc"
    ABC_SHARP = "abc_sharp"
    ABC_ROUNDED = "abc_rounded"
    ABC_OUTLINED = "abc_outlined"
    AC_UNIT = "ac_unit"
    AC_UNIT_SHARP = "ac_unit_sharp"
    AC_UNIT_ROUNDED = "ac_unit_rounded"
    AC_UNIT_OUTLINED = "ac_unit_outlined"
    ACCESS_ALARM = "access_alarm"
    ACCESS_ALARM_SHARP = "access_alarm_sharp"
    ACCESS_ALARM_ROUNDED = "access_alarm_rounded"
    ACCESS_ALARM_OUTLINED = "access_alarm_outlined"
    ACCESS_ALARMS = "access_alarms"
    ACCESS_ALARMS_SHARP = "access_alarms_sharp"
    ACCESS_ALARMS_ROUNDED = "access_alarms_rounded"
    ACCESS_ALARMS_OUTLINED = "access_alarms_outlined"
    ACCESS_TIME = "access_time"
    ACCESS_TIME_SHARP = "access_time_sharp"
    ACCESS_TIME_ROUNDED = "access_time_rounded"
    ACCESS_TIME_OUTLINED = "access_time_outlined"
    ACCESS_TIME_FILLED = "access_time_filled"
    ACCESS_TIME_FILLED_SHARP = "access_time_filled_sharp"
    ACCESS_TIME_FILLED_ROUNDED = "access_time_filled_rounded"
    ACCESS_TIME_FILLED_OUTLINED = "access_time_filled_outlined"
    ACCESSIBILITY = "accessibility"
    ACCESSIBILITY_SHARP = "accessibility_sharp"
    ACCESSIBILITY_ROUNDED = "accessibility_rounded"
    ACCESSIBILITY_OUTLINED = "accessibility_outlined"
    ACCESSIBILITY_NEW = "accessibility_new"
    ACCESSIBILITY_NEW_SHARP = "accessibility_new_sharp"
    ACCESSIBILITY_NEW_ROUNDED = "accessibility_new_rounded"
    ACCESSIBILITY_NEW_OUTLINED = "accessibility_new_outlined"
    ACCESSIBLE = "accessible"
    ACCESSIBLE_SHARP = "accessible_sharp"
    ACCESSIBLE_ROUNDED = "accessible_rounded"
    ACCESSIBLE_OUTLINED = "accessible_outlined"
    ACCESSIBLE_FORWARD = "accessible_forward"
    ACCESSIBLE_FORWARD_SHARP = "accessible_forward_sharp"
    ACCESSIBLE_FORWARD_ROUNDED = "accessible_forward_rounded"
    ACCESSIBLE_FORWARD_OUTLINED = "accessible_forward_outlined"
    ACCOUNT_BALANCE = "account_balance"
    ACCOUNT_BALANCE_SHARP = "account_balance_sharp"
    ACCOUNT_BALANCE_ROUNDED = "account_balance_rounded"
    ACCOUNT_BALANCE_OUTLINED = "account_balance_outlined"
    ACCOUNT_BALANCE_WALLET = "account_balance_wallet"
    ACCOUNT_BALANCE_WALLET_SHARP = "account_balance_wallet_sharp"
    ACCOUNT_BALANCE_WALLET_ROUNDED = "account_balance_wallet_rounded"
    ACCOUNT_BALANCE_WALLET_OUTLINED = "account_balance_wallet_outlined"
    ACCOUNT_BOX = "account_box"
    ACCOUNT_BOX_SHARP = "account_box_sharp"
    ACCOUNT_BOX_ROUNDED = "account_box_rounded"
    ACCOUNT_BOX_OUTLINED = "account_box_outlined"
    ACCOUNT_CIRCLE = "account_circle"
    ACCOUNT_CIRCLE_SHARP = "account_circle_sharp"
    ACCOUNT_CIRCLE_ROUNDED = "account_circle_rounded"
    ACCOUNT_CIRCLE_OUTLINED = "account_circle_outlined"
    ACCOUNT_TREE = "account_tree"
    ACCOUNT_TREE_SHARP = "account_tree_sharp"
    ACCOUNT_TREE_ROUNDED = "account_tree_rounded"
    ACCOUNT_TREE_OUTLINED = "account_tree_outlined"
    AD_UNITS = "ad_units"
    AD_UNITS_SHARP = "ad_units_sharp"
    AD_UNITS_ROUNDED = "ad_units_rounded"
    AD_UNITS_OUTLINED = "ad_units_outlined"
    ADB = "adb"
    ADB_SHARP = "adb_sharp"
    ADB_ROUNDED = "adb_rounded"
    ADB_OUTLINED = "adb_outlined"
    ADD = "add"
    ADD_SHARP = "add_sharp"
    ADD_ROUNDED = "add_rounded"
    ADD_OUTLINED = "add_outlined"
    ADD_A_PHOTO = "add_a_photo"
    ADD_A_PHOTO_SHARP = "add_a_photo_sharp"
    ADD_A_PHOTO_ROUNDED = "add_a_photo_rounded"
    ADD_A_PHOTO_OUTLINED = "add_a_photo_outlined"
    ADD_ALARM = "add_alarm"
    ADD_ALARM_SHARP = "add_alarm_sharp"
    ADD_ALARM_ROUNDED = "add_alarm_rounded"
    ADD_ALARM_OUTLINED = "add_alarm_outlined"
    ADD_ALERT = "add_alert"
    ADD_ALERT_SHARP = "add_alert_sharp"
    ADD_ALERT_ROUNDED = "add_alert_rounded"
    ADD_ALERT_OUTLINED = "add_alert_outlined"
    ADD_BOX = "add_box"
    ADD_BOX_SHARP = "add_box_sharp"
    ADD_BOX_ROUNDED = "add_box_rounded"
    ADD_BOX_OUTLINED = "add_box_outlined"
    ADD_BUSINESS = "add_business"
    ADD_BUSINESS_SHARP = "add_business_sharp"
    ADD_BUSINESS_ROUNDED = "add_business_rounded"
    ADD_BUSINESS_OUTLINED = "add_business_outlined"
    ADD_CALL = "add_call"
    ADD_CARD = "add_card"
    ADD_CARD_SHARP = "add_card_sharp"
    ADD_CARD_ROUNDED = "add_card_rounded"
    ADD_CARD_OUTLINED = "add_card_outlined"
    ADD_CHART = "add_chart"
    ADD_CHART_SHARP = "add_chart_sharp"
    ADD_CHART_ROUNDED = "add_chart_rounded"
    ADD_CHART_OUTLINED = "add_chart_outlined"
    ADD_CIRCLE = "add_circle"
    ADD_CIRCLE_SHARP = "add_circle_sharp"
    ADD_CIRCLE_ROUNDED = "add_circle_rounded"
    ADD_CIRCLE_OUTLINED = "add_circle_outlined"
    ADD_CIRCLE_OUTLINE = "add_circle_outline"
    ADD_CIRCLE_OUTLINE_SHARP = "add_circle_outline_sharp"
    ADD_CIRCLE_OUTLINE_ROUNDED = "add_circle_outline_rounded"
    ADD_CIRCLE_OUTLINE_OUTLINED = "add_circle_outline_outlined"
    ADD_COMMENT = "add_comment"
    ADD_COMMENT_SHARP = "add_comment_sharp"
    ADD_COMMENT_ROUNDED = "add_comment_rounded"
    ADD_COMMENT_OUTLINED = "add_comment_outlined"
    ADD_HOME = "add_home"
    ADD_HOME_SHARP = "add_home_sharp"
    ADD_HOME_ROUNDED = "add_home_rounded"
    ADD_HOME_OUTLINED = "add_home_outlined"
    ADD_HOME_WORK = "add_home_work"
    ADD_HOME_WORK_SHARP = "add_home_work_sharp"
    ADD_HOME_WORK_ROUNDED = "add_home_work_rounded"
    ADD_HOME_WORK_OUTLINED = "add_home_work_outlined"
    ADD_IC_CALL = "add_ic_call"
    ADD_IC_CALL_SHARP = "add_ic_call_sharp"
    ADD_IC_CALL_ROUNDED = "add_ic_call_rounded"
    ADD_IC_CALL_OUTLINED = "add_ic_call_outlined"
    ADD_LINK = "add_link"
    ADD_LINK_SHARP = "add_link_sharp"
    ADD_LINK_ROUNDED = "add_link_rounded"
    ADD_LINK_OUTLINED = "add_link_outlined"
    ADD_LOCATION = "add_location"
    ADD_LOCATION_SHARP = "add_location_sharp"
    ADD_LOCATION_ROUNDED = "add_location_rounded"
    ADD_LOCATION_OUTLINED = "add_location_outlined"
    ADD_LOCATION_ALT = "add_location_alt"
    ADD_LOCATION_ALT_SHARP = "add_location_alt_sharp"
    ADD_LOCATION_ALT_ROUNDED = "add_location_alt_rounded"
    ADD_LOCATION_ALT_OUTLINED = "add_location_alt_outlined"
    ADD_MODERATOR = "add_moderator"
    ADD_MODERATOR_SHARP = "add_moderator_sharp"
    ADD_MODERATOR_ROUNDED = "add_moderator_rounded"
    ADD_MODERATOR_OUTLINED = "add_moderator_outlined"
    ADD_PHOTO_ALTERNATE = "add_photo_alternate"
    ADD_PHOTO_ALTERNATE_SHARP = "add_photo_alternate_sharp"
    ADD_PHOTO_ALTERNATE_ROUNDED = "add_photo_alternate_rounded"
    ADD_PHOTO_ALTERNATE_OUTLINED = "add_photo_alternate_outlined"
    ADD_REACTION = "add_reaction"
    ADD_REACTION_SHARP = "add_reaction_sharp"
    ADD_REACTION_ROUNDED = "add_reaction_rounded"
    ADD_REACTION_OUTLINED = "add_reaction_outlined"
    ADD_ROAD = "add_road"
    ADD_ROAD_SHARP = "add_road_sharp"
    ADD_ROAD_ROUNDED = "add_road_rounded"
    ADD_ROAD_OUTLINED = "add_road_outlined"
    ADD_SHOPPING_CART = "add_shopping_cart"
    ADD_SHOPPING_CART_SHARP = "add_shopping_cart_sharp"
    ADD_SHOPPING_CART_ROUNDED = "add_shopping_cart_rounded"
    ADD_SHOPPING_CART_OUTLINED = "add_shopping_cart_outlined"
    ADD_TASK = "add_task"
    ADD_TASK_SHARP = "add_task_sharp"
    ADD_TASK_ROUNDED = "add_task_rounded"
    ADD_TASK_OUTLINED = "add_task_outlined"
    ADD_TO_DRIVE = "add_to_drive"
    ADD_TO_DRIVE_SHARP = "add_to_drive_sharp"
    ADD_TO_DRIVE_ROUNDED = "add_to_drive_rounded"
    ADD_TO_DRIVE_OUTLINED = "add_to_drive_outlined"
    ADD_TO_HOME_SCREEN = "add_to_home_screen"
    ADD_TO_HOME_SCREEN_SHARP = "add_to_home_screen_sharp"
    ADD_TO_HOME_SCREEN_ROUNDED = "add_to_home_screen_rounded"
    ADD_TO_HOME_SCREEN_OUTLINED = "add_to_home_screen_outlined"
    ADD_TO_PHOTOS = "add_to_photos"
    ADD_TO_PHOTOS_SHARP = "add_to_photos_sharp"
    ADD_TO_PHOTOS_ROUNDED = "add_to_photos_rounded"
    ADD_TO_PHOTOS_OUTLINED = "add_to_photos_outlined"
    ADD_TO_QUEUE = "add_to_queue"
    ADD_TO_QUEUE_SHARP = "add_to_queue_sharp"
    ADD_TO_QUEUE_ROUNDED = "add_to_queue_rounded"
    ADD_TO_QUEUE_OUTLINED = "add_to_queue_outlined"
    ADDCHART = "addchart"
    ADDCHART_SHARP = "addchart_sharp"
    ADDCHART_ROUNDED = "addchart_rounded"
    ADDCHART_OUTLINED = "addchart_outlined"
    ADF_SCANNER = "adf_scanner"
    ADF_SCANNER_SHARP = "adf_scanner_sharp"
    ADF_SCANNER_ROUNDED = "adf_scanner_rounded"
    ADF_SCANNER_OUTLINED = "adf_scanner_outlined"
    ADJUST = "adjust"
    ADJUST_SHARP = "adjust_sharp"
    ADJUST_ROUNDED = "adjust_rounded"
    ADJUST_OUTLINED = "adjust_outlined"
    ADMIN_PANEL_SETTINGS = "admin_panel_settings"
    ADMIN_PANEL_SETTINGS_SHARP = "admin_panel_settings_sharp"
    ADMIN_PANEL_SETTINGS_ROUNDED = "admin_panel_settings_rounded"
    ADMIN_PANEL_SETTINGS_OUTLINED = "admin_panel_settings_outlined"
    ADOBE = "adobe"
    ADOBE_SHARP = "adobe_sharp"
    ADOBE_ROUNDED = "adobe_rounded"
    ADOBE_OUTLINED = "adobe_outlined"
    ADS_CLICK = "ads_click"
    ADS_CLICK_SHARP = "ads_click_sharp"
    ADS_CLICK_ROUNDED = "ads_click_rounded"
    ADS_CLICK_OUTLINED = "ads_click_outlined"
    AGRICULTURE = "agriculture"
    AGRICULTURE_SHARP = "agriculture_sharp"
    AGRICULTURE_ROUNDED = "agriculture_rounded"
    AGRICULTURE_OUTLINED = "agriculture_outlined"
    AIR = "air"
    AIR_SHARP = "air_sharp"
    AIR_ROUNDED = "air_rounded"
    AIR_OUTLINED = "air_outlined"
    AIRLINE_SEAT_FLAT = "airline_seat_flat"
    AIRLINE_SEAT_FLAT_SHARP = "airline_seat_flat_sharp"
    AIRLINE_SEAT_FLAT_ROUNDED = "airline_seat_flat_rounded"
    AIRLINE_SEAT_FLAT_OUTLINED = "airline_seat_flat_outlined"
    AIRLINE_SEAT_FLAT_ANGLED = "airline_seat_flat_angled"
    AIRLINE_SEAT_FLAT_ANGLED_SHARP = "airline_seat_flat_angled_sharp"
    AIRLINE_SEAT_FLAT_ANGLED_ROUNDED = "airline_seat_flat_angled_rounded"
    AIRLINE_SEAT_FLAT_ANGLED_OUTLINED = "airline_seat_flat_angled_outlined"
    AIRLINE_SEAT_INDIVIDUAL_SUITE = "airline_seat_individual_suite"
    AIRLINE_SEAT_INDIVIDUAL_SUITE_SHARP = "airline_seat_individual_suite_sharp"
    AIRLINE_SEAT_INDIVIDUAL_SUITE_ROUNDED = "airline_seat_individual_suite_rounded"
    AIRLINE_SEAT_INDIVIDUAL_SUITE_OUTLINED = "airline_seat_individual_suite_outlined"
    AIRLINE_SEAT_LEGROOM_EXTRA = "airline_seat_legroom_extra"
    AIRLINE_SEAT_LEGROOM_EXTRA_SHARP = "airline_seat_legroom_extra_sharp"
    AIRLINE_SEAT_LEGROOM_EXTRA_ROUNDED = "airline_seat_legroom_extra_rounded"
    AIRLINE_SEAT_LEGROOM_EXTRA_OUTLINED = "airline_seat_legroom_extra_outlined"
    AIRLINE_SEAT_LEGROOM_NORMAL = "airline_seat_legroom_normal"
    AIRLINE_SEAT_LEGROOM_NORMAL_SHARP = "airline_seat_legroom_normal_sharp"
    AIRLINE_SEAT_LEGROOM_NORMAL_ROUNDED = "airline_seat_legroom_normal_rounded"
    AIRLINE_SEAT_LEGROOM_NORMAL_OUTLINED = "airline_seat_legroom_normal_outlined"
    AIRLINE_SEAT_LEGROOM_REDUCED = "airline_seat_legroom_reduced"
    AIRLINE_SEAT_LEGROOM_REDUCED_SHARP = "airline_seat_legroom_reduced_sharp"
    AIRLINE_SEAT_LEGROOM_REDUCED_ROUNDED = "airline_seat_legroom_reduced_rounded"
    AIRLINE_SEAT_LEGROOM_REDUCED_OUTLINED = "airline_seat_legroom_reduced_outlined"
    AIRLINE_SEAT_RECLINE_EXTRA = "airline_seat_recline_extra"
    AIRLINE_SEAT_RECLINE_EXTRA_SHARP = "airline_seat_recline_extra_sharp"
    AIRLINE_SEAT_RECLINE_EXTRA_ROUNDED = "airline_seat_recline_extra_rounded"
    AIRLINE_SEAT_RECLINE_EXTRA_OUTLINED = "airline_seat_recline_extra_outlined"
    AIRLINE_SEAT_RECLINE_NORMAL = "airline_seat_recline_normal"
    AIRLINE_SEAT_RECLINE_NORMAL_SHARP = "airline_seat_recline_normal_sharp"
    AIRLINE_SEAT_RECLINE_NORMAL_ROUNDED = "airline_seat_recline_normal_rounded"
    AIRLINE_SEAT_RECLINE_NORMAL_OUTLINED = "airline_seat_recline_normal_outlined"
    AIRLINE_STOPS = "airline_stops"
    AIRLINE_STOPS_SHARP = "airline_stops_sharp"
    AIRLINE_STOPS_ROUNDED = "airline_stops_rounded"
    AIRLINE_STOPS_OUTLINED = "airline_stops_outlined"
    AIRLINES = "airlines"
    AIRLINES_SHARP = "airlines_sharp"
    AIRLINES_ROUNDED = "airlines_rounded"
    AIRLINES_OUTLINED = "airlines_outlined"
    AIRPLANE_TICKET = "airplane_ticket"
    AIRPLANE_TICKET_SHARP = "airplane_ticket_sharp"
    AIRPLANE_TICKET_ROUNDED = "airplane_ticket_rounded"
    AIRPLANE_TICKET_OUTLINED = "airplane_ticket_outlined"
    AIRPLANEMODE_ACTIVE = "airplanemode_active"
    AIRPLANEMODE_ACTIVE_SHARP = "airplanemode_active_sharp"
    AIRPLANEMODE_ACTIVE_ROUNDED = "airplanemode_active_rounded"
    AIRPLANEMODE_ACTIVE_OUTLINED = "airplanemode_active_outlined"
    AIRPLANEMODE_INACTIVE = "airplanemode_inactive"
    AIRPLANEMODE_INACTIVE_SHARP = "airplanemode_inactive_sharp"
    AIRPLANEMODE_INACTIVE_ROUNDED = "airplanemode_inactive_rounded"
    AIRPLANEMODE_INACTIVE_OUTLINED = "airplanemode_inactive_outlined"
    AIRPLANEMODE_OFF = "airplanemode_off"
    AIRPLANEMODE_OFF_SHARP = "airplanemode_off_sharp"
    AIRPLANEMODE_OFF_ROUNDED = "airplanemode_off_rounded"
    AIRPLANEMODE_OFF_OUTLINED = "airplanemode_off_outlined"
    AIRPLANEMODE_ON = "airplanemode_on"
    AIRPLANEMODE_ON_SHARP = "airplanemode_on_sharp"
    AIRPLANEMODE_ON_ROUNDED = "airplanemode_on_rounded"
    AIRPLANEMODE_ON_OUTLINED = "airplanemode_on_outlined"
    AIRPLAY = "airplay"
    AIRPLAY_SHARP = "airplay_sharp"
    AIRPLAY_ROUNDED = "airplay_rounded"
    AIRPLAY_OUTLINED = "airplay_outlined"
    AIRPORT_SHUTTLE = "airport_shuttle"
    AIRPORT_SHUTTLE_SHARP = "airport_shuttle_sharp"
    AIRPORT_SHUTTLE_ROUNDED = "airport_shuttle_rounded"
    AIRPORT_SHUTTLE_OUTLINED = "airport_shuttle_outlined"
    ALARM = "alarm"
    ALARM_SHARP = "alarm_sharp"
    ALARM_ROUNDED = "alarm_rounded"
    ALARM_OUTLINED = "alarm_outlined"
    ALARM_ADD = "alarm_add"
    ALARM_ADD_SHARP = "alarm_add_sharp"
    ALARM_ADD_ROUNDED = "alarm_add_rounded"
    ALARM_ADD_OUTLINED = "alarm_add_outlined"
    ALARM_OFF = "alarm_off"
    ALARM_OFF_SHARP = "alarm_off_sharp"
    ALARM_OFF_ROUNDED = "alarm_off_rounded"
    ALARM_OFF_OUTLINED = "alarm_off_outlined"
    ALARM_ON = "alarm_on"
    ALARM_ON_SHARP = "alarm_on_sharp"
    ALARM_ON_ROUNDED = "alarm_on_rounded"
    ALARM_ON_OUTLINED = "alarm_on_outlined"
    ALBUM = "album"
    ALBUM_SHARP = "album_sharp"
    ALBUM_ROUNDED = "album_rounded"
    ALBUM_OUTLINED = "album_outlined"
    ALIGN_HORIZONTAL_CENTER = "align_horizontal_center"
    ALIGN_HORIZONTAL_CENTER_SHARP = "align_horizontal_center_sharp"
    ALIGN_HORIZONTAL_CENTER_ROUNDED = "align_horizontal_center_rounded"
    ALIGN_HORIZONTAL_CENTER_OUTLINED = "align_horizontal_center_outlined"
    ALIGN_HORIZONTAL_LEFT = "align_horizontal_left"
    ALIGN_HORIZONTAL_LEFT_SHARP = "align_horizontal_left_sharp"
    ALIGN_HORIZONTAL_LEFT_ROUNDED = "align_horizontal_left_rounded"
    ALIGN_HORIZONTAL_LEFT_OUTLINED = "align_horizontal_left_outlined"
    ALIGN_HORIZONTAL_RIGHT = "align_horizontal_right"
    ALIGN_HORIZONTAL_RIGHT_SHARP = "align_horizontal_right_sharp"
    ALIGN_HORIZONTAL_RIGHT_ROUNDED = "align_horizontal_right_rounded"
    ALIGN_HORIZONTAL_RIGHT_OUTLINED = "align_horizontal_right_outlined"
    ALIGN_VERTICAL_BOTTOM = "align_vertical_bottom"
    ALIGN_VERTICAL_BOTTOM_SHARP = "align_vertical_bottom_sharp"
    ALIGN_VERTICAL_BOTTOM_ROUNDED = "align_vertical_bottom_rounded"
    ALIGN_VERTICAL_BOTTOM_OUTLINED = "align_vertical_bottom_outlined"
    ALIGN_VERTICAL_CENTER = "align_vertical_center"
    ALIGN_VERTICAL_CENTER_SHARP = "align_vertical_center_sharp"
    ALIGN_VERTICAL_CENTER_ROUNDED = "align_vertical_center_rounded"
    ALIGN_VERTICAL_CENTER_OUTLINED = "align_vertical_center_outlined"
    ALIGN_VERTICAL_TOP = "align_vertical_top"
    ALIGN_VERTICAL_TOP_SHARP = "align_vertical_top_sharp"
    ALIGN_VERTICAL_TOP_ROUNDED = "align_vertical_top_rounded"
    ALIGN_VERTICAL_TOP_OUTLINED = "align_vertical_top_outlined"
    ALL_INBOX = "all_inbox"
    ALL_INBOX_SHARP = "all_inbox_sharp"
    ALL_INBOX_ROUNDED = "all_inbox_rounded"
    ALL_INBOX_OUTLINED = "all_inbox_outlined"
    ALL_INCLUSIVE = "all_inclusive"
    ALL_INCLUSIVE_SHARP = "all_inclusive_sharp"
    ALL_INCLUSIVE_ROUNDED = "all_inclusive_rounded"
    ALL_INCLUSIVE_OUTLINED = "all_inclusive_outlined"
    ALL_OUT = "all_out"
    ALL_OUT_SHARP = "all_out_sharp"
    ALL_OUT_ROUNDED = "all_out_rounded"
    ALL_OUT_OUTLINED = "all_out_outlined"
    ALT_ROUTE = "alt_route"
    ALT_ROUTE_SHARP = "alt_route_sharp"
    ALT_ROUTE_ROUNDED = "alt_route_rounded"
    ALT_ROUTE_OUTLINED = "alt_route_outlined"
    ALTERNATE_EMAIL = "alternate_email"
    ALTERNATE_EMAIL_SHARP = "alternate_email_sharp"
    ALTERNATE_EMAIL_ROUNDED = "alternate_email_rounded"
    ALTERNATE_EMAIL_OUTLINED = "alternate_email_outlined"
    AMP_STORIES = "amp_stories"
    AMP_STORIES_SHARP = "amp_stories_sharp"
    AMP_STORIES_ROUNDED = "amp_stories_rounded"
    AMP_STORIES_OUTLINED = "amp_stories_outlined"
    ANALYTICS = "analytics"
    ANALYTICS_SHARP = "analytics_sharp"
    ANALYTICS_ROUNDED = "analytics_rounded"
    ANALYTICS_OUTLINED = "analytics_outlined"
    ANCHOR = "anchor"
    ANCHOR_SHARP = "anchor_sharp"
    ANCHOR_ROUNDED = "anchor_rounded"
    ANCHOR_OUTLINED = "anchor_outlined"
    ANDROID = "android"
    ANDROID_SHARP = "android_sharp"
    ANDROID_ROUNDED = "android_rounded"
    ANDROID_OUTLINED = "android_outlined"
    ANIMATION = "animation"
    ANIMATION_SHARP = "animation_sharp"
    ANIMATION_ROUNDED = "animation_rounded"
    ANIMATION_OUTLINED = "animation_outlined"
    ANNOUNCEMENT = "announcement"
    ANNOUNCEMENT_SHARP = "announcement_sharp"
    ANNOUNCEMENT_ROUNDED = "announcement_rounded"
    ANNOUNCEMENT_OUTLINED = "announcement_outlined"
    AOD = "aod"
    AOD_SHARP = "aod_sharp"
    AOD_ROUNDED = "aod_rounded"
    AOD_OUTLINED = "aod_outlined"
    APARTMENT = "apartment"
    APARTMENT_SHARP = "apartment_sharp"
    APARTMENT_ROUNDED = "apartment_rounded"
    APARTMENT_OUTLINED = "apartment_outlined"
    API = "api"
    API_SHARP = "api_sharp"
    API_ROUNDED = "api_rounded"
    API_OUTLINED = "api_outlined"
    APP_BLOCKING = "app_blocking"
    APP_BLOCKING_SHARP = "app_blocking_sharp"
    APP_BLOCKING_ROUNDED = "app_blocking_rounded"
    APP_BLOCKING_OUTLINED = "app_blocking_outlined"
    APP_REGISTRATION = "app_registration"
    APP_REGISTRATION_SHARP = "app_registration_sharp"
    APP_REGISTRATION_ROUNDED = "app_registration_rounded"
    APP_REGISTRATION_OUTLINED = "app_registration_outlined"
    APP_SETTINGS_ALT = "app_settings_alt"
    APP_SETTINGS_ALT_SHARP = "app_settings_alt_sharp"
    APP_SETTINGS_ALT_ROUNDED = "app_settings_alt_rounded"
    APP_SETTINGS_ALT_OUTLINED = "app_settings_alt_outlined"
    APP_SHORTCUT = "app_shortcut"
    APP_SHORTCUT_SHARP = "app_shortcut_sharp"
    APP_SHORTCUT_ROUNDED = "app_shortcut_rounded"
    APP_SHORTCUT_OUTLINED = "app_shortcut_outlined"
    APPLE = "apple"
    APPLE_SHARP = "apple_sharp"
    APPLE_ROUNDED = "apple_rounded"
    APPLE_OUTLINED = "apple_outlined"
    APPROVAL = "approval"
    APPROVAL_SHARP = "approval_sharp"
    APPROVAL_ROUNDED = "approval_rounded"
    APPROVAL_OUTLINED = "approval_outlined"
    APPS = "apps"
    APPS_SHARP = "apps_sharp"
    APPS_ROUNDED = "apps_rounded"
    APPS_OUTLINED = "apps_outlined"
    APPS_OUTAGE = "apps_outage"
    APPS_OUTAGE_SHARP = "apps_outage_sharp"
    APPS_OUTAGE_ROUNDED = "apps_outage_rounded"
    APPS_OUTAGE_OUTLINED = "apps_outage_outlined"
    ARCHITECTURE = "architecture"
    ARCHITECTURE_SHARP = "architecture_sharp"
    ARCHITECTURE_ROUNDED = "architecture_rounded"
    ARCHITECTURE_OUTLINED = "architecture_outlined"
    ARCHIVE = "archive"
    ARCHIVE_SHARP = "archive_sharp"
    ARCHIVE_ROUNDED = "archive_rounded"
    ARCHIVE_OUTLINED = "archive_outlined"
    AREA_CHART = "area_chart"
    AREA_CHART_SHARP = "area_chart_sharp"
    AREA_CHART_ROUNDED = "area_chart_rounded"
    AREA_CHART_OUTLINED = "area_chart_outlined"
    ARROW_BACK = "arrow_back"
    ARROW_BACK_SHARP = "arrow_back_sharp"
    ARROW_BACK_ROUNDED = "arrow_back_rounded"
    ARROW_BACK_OUTLINED = "arrow_back_outlined"
    ARROW_BACK_IOS = "arrow_back_ios"
    ARROW_BACK_IOS_SHARP = "arrow_back_ios_sharp"
    ARROW_BACK_IOS_ROUNDED = "arrow_back_ios_rounded"
    ARROW_BACK_IOS_OUTLINED = "arrow_back_ios_outlined"
    ARROW_BACK_IOS_NEW = "arrow_back_ios_new"
    ARROW_BACK_IOS_NEW_SHARP = "arrow_back_ios_new_sharp"
    ARROW_BACK_IOS_NEW_ROUNDED = "arrow_back_ios_new_rounded"
    ARROW_BACK_IOS_NEW_OUTLINED = "arrow_back_ios_new_outlined"
    ARROW_CIRCLE_DOWN = "arrow_circle_down"
    ARROW_CIRCLE_DOWN_SHARP = "arrow_circle_down_sharp"
    ARROW_CIRCLE_DOWN_ROUNDED = "arrow_circle_down_rounded"
    ARROW_CIRCLE_DOWN_OUTLINED = "arrow_circle_down_outlined"
    ARROW_CIRCLE_LEFT = "arrow_circle_left"
    ARROW_CIRCLE_LEFT_SHARP = "arrow_circle_left_sharp"
    ARROW_CIRCLE_LEFT_ROUNDED = "arrow_circle_left_rounded"
    ARROW_CIRCLE_LEFT_OUTLINED = "arrow_circle_left_outlined"
    ARROW_CIRCLE_RIGHT = "arrow_circle_right"
    ARROW_CIRCLE_RIGHT_SHARP = "arrow_circle_right_sharp"
    ARROW_CIRCLE_RIGHT_ROUNDED = "arrow_circle_right_rounded"
    ARROW_CIRCLE_RIGHT_OUTLINED = "arrow_circle_right_outlined"
    ARROW_CIRCLE_UP = "arrow_circle_up"
    ARROW_CIRCLE_UP_SHARP = "arrow_circle_up_sharp"
    ARROW_CIRCLE_UP_ROUNDED = "arrow_circle_up_rounded"
    ARROW_CIRCLE_UP_OUTLINED = "arrow_circle_up_outlined"
    ARROW_DOWNWARD = "arrow_downward"
    ARROW_DOWNWARD_SHARP = "arrow_downward_sharp"
    ARROW_DOWNWARD_ROUNDED = "arrow_downward_rounded"
    ARROW_DOWNWARD_OUTLINED = "arrow_downward_outlined"
    ARROW_DROP_DOWN = "arrow_drop_down"
    ARROW_DROP_DOWN_SHARP = "arrow_drop_down_sharp"
    ARROW_DROP_DOWN_ROUNDED = "arrow_drop_down_rounded"
    ARROW_DROP_DOWN_OUTLINED = "arrow_drop_down_outlined"
    ARROW_DROP_DOWN_CIRCLE = "arrow_drop_down_circle"
    ARROW_DROP_DOWN_CIRCLE_SHARP = "arrow_drop_down_circle_sharp"
    ARROW_DROP_DOWN_CIRCLE_ROUNDED = "arrow_drop_down_circle_rounded"
    ARROW_DROP_DOWN_CIRCLE_OUTLINED = "arrow_drop_down_circle_outlined"
    ARROW_DROP_UP = "arrow_drop_up"
    ARROW_DROP_UP_SHARP = "arrow_drop_up_sharp"
    ARROW_DROP_UP_ROUNDED = "arrow_drop_up_rounded"
    ARROW_DROP_UP_OUTLINED = "arrow_drop_up_outlined"
    ARROW_FORWARD = "arrow_forward"
    ARROW_FORWARD_SHARP = "arrow_forward_sharp"
    ARROW_FORWARD_ROUNDED = "arrow_forward_rounded"
    ARROW_FORWARD_OUTLINED = "arrow_forward_outlined"
    ARROW_FORWARD_IOS = "arrow_forward_ios"
    ARROW_FORWARD_IOS_SHARP = "arrow_forward_ios_sharp"
    ARROW_FORWARD_IOS_ROUNDED = "arrow_forward_ios_rounded"
    ARROW_FORWARD_IOS_OUTLINED = "arrow_forward_ios_outlined"
    ARROW_LEFT = "arrow_left"
    ARROW_LEFT_SHARP = "arrow_left_sharp"
    ARROW_LEFT_ROUNDED = "arrow_left_rounded"
    ARROW_LEFT_OUTLINED = "arrow_left_outlined"
    ARROW_OUTWARD = "arrow_outward"
    ARROW_OUTWARD_SHARP = "arrow_outward_sharp"
    ARROW_OUTWARD_ROUNDED = "arrow_outward_rounded"
    ARROW_OUTWARD_OUTLINED = "arrow_outward_outlined"
    ARROW_RIGHT = "arrow_right"
    ARROW_RIGHT_SHARP = "arrow_right_sharp"
    ARROW_RIGHT_ROUNDED = "arrow_right_rounded"
    ARROW_RIGHT_OUTLINED = "arrow_right_outlined"
    ARROW_RIGHT_ALT = "arrow_right_alt"
    ARROW_RIGHT_ALT_SHARP = "arrow_right_alt_sharp"
    ARROW_RIGHT_ALT_ROUNDED = "arrow_right_alt_rounded"
    ARROW_RIGHT_ALT_OUTLINED = "arrow_right_alt_outlined"
    ARROW_UPWARD = "arrow_upward"
    ARROW_UPWARD_SHARP = "arrow_upward_sharp"
    ARROW_UPWARD_ROUNDED = "arrow_upward_rounded"
    ARROW_UPWARD_OUTLINED = "arrow_upward_outlined"
    ART_TRACK = "art_track"
    ART_TRACK_SHARP = "art_track_sharp"
    ART_TRACK_ROUNDED = "art_track_rounded"
    ART_TRACK_OUTLINED = "art_track_outlined"
    ARTICLE = "article"
    ARTICLE_SHARP = "article_sharp"
    ARTICLE_ROUNDED = "article_rounded"
    ARTICLE_OUTLINED = "article_outlined"
    ASPECT_RATIO = "aspect_ratio"
    ASPECT_RATIO_SHARP = "aspect_ratio_sharp"
    ASPECT_RATIO_ROUNDED = "aspect_ratio_rounded"
    ASPECT_RATIO_OUTLINED = "aspect_ratio_outlined"
    ASSESSMENT = "assessment"
    ASSESSMENT_SHARP = "assessment_sharp"
    ASSESSMENT_ROUNDED = "assessment_rounded"
    ASSESSMENT_OUTLINED = "assessment_outlined"
    ASSIGNMENT = "assignment"
    ASSIGNMENT_SHARP = "assignment_sharp"
    ASSIGNMENT_ROUNDED = "assignment_rounded"
    ASSIGNMENT_OUTLINED = "assignment_outlined"
    ASSIGNMENT_ADD = "assignment_add"
    ASSIGNMENT_IND = "assignment_ind"
    ASSIGNMENT_IND_SHARP = "assignment_ind_sharp"
    ASSIGNMENT_IND_ROUNDED = "assignment_ind_rounded"
    ASSIGNMENT_IND_OUTLINED = "assignment_ind_outlined"
    ASSIGNMENT_LATE = "assignment_late"
    ASSIGNMENT_LATE_SHARP = "assignment_late_sharp"
    ASSIGNMENT_LATE_ROUNDED = "assignment_late_rounded"
    ASSIGNMENT_LATE_OUTLINED = "assignment_late_outlined"
    ASSIGNMENT_RETURN = "assignment_return"
    ASSIGNMENT_RETURN_SHARP = "assignment_return_sharp"
    ASSIGNMENT_RETURN_ROUNDED = "assignment_return_rounded"
    ASSIGNMENT_RETURN_OUTLINED = "assignment_return_outlined"
    ASSIGNMENT_RETURNED = "assignment_returned"
    ASSIGNMENT_RETURNED_SHARP = "assignment_returned_sharp"
    ASSIGNMENT_RETURNED_ROUNDED = "assignment_returned_rounded"
    ASSIGNMENT_RETURNED_OUTLINED = "assignment_returned_outlined"
    ASSIGNMENT_TURNED_IN = "assignment_turned_in"
    ASSIGNMENT_TURNED_IN_SHARP = "assignment_turned_in_sharp"
    ASSIGNMENT_TURNED_IN_ROUNDED = "assignment_turned_in_rounded"
    ASSIGNMENT_TURNED_IN_OUTLINED = "assignment_turned_in_outlined"
    ASSIST_WALKER = "assist_walker"
    ASSIST_WALKER_SHARP = "assist_walker_sharp"
    ASSIST_WALKER_ROUNDED = "assist_walker_rounded"
    ASSIST_WALKER_OUTLINED = "assist_walker_outlined"
    ASSISTANT = "assistant"
    ASSISTANT_SHARP = "assistant_sharp"
    ASSISTANT_ROUNDED = "assistant_rounded"
    ASSISTANT_OUTLINED = "assistant_outlined"
    ASSISTANT_DIRECTION = "assistant_direction"
    ASSISTANT_DIRECTION_SHARP = "assistant_direction_sharp"
    ASSISTANT_DIRECTION_ROUNDED = "assistant_direction_rounded"
    ASSISTANT_DIRECTION_OUTLINED = "assistant_direction_outlined"
    ASSISTANT_NAVIGATION = "assistant_navigation"
    ASSISTANT_PHOTO = "assistant_photo"
    ASSISTANT_PHOTO_SHARP = "assistant_photo_sharp"
    ASSISTANT_PHOTO_ROUNDED = "assistant_photo_rounded"
    ASSISTANT_PHOTO_OUTLINED = "assistant_photo_outlined"
    ASSURED_WORKLOAD = "assured_workload"
    ASSURED_WORKLOAD_SHARP = "assured_workload_sharp"
    ASSURED_WORKLOAD_ROUNDED = "assured_workload_rounded"
    ASSURED_WORKLOAD_OUTLINED = "assured_workload_outlined"
    ATM = "atm"
    ATM_SHARP = "atm_sharp"
    ATM_ROUNDED = "atm_rounded"
    ATM_OUTLINED = "atm_outlined"
    ATTACH_EMAIL = "attach_email"
    ATTACH_EMAIL_SHARP = "attach_email_sharp"
    ATTACH_EMAIL_ROUNDED = "attach_email_rounded"
    ATTACH_EMAIL_OUTLINED = "attach_email_outlined"
    ATTACH_FILE = "attach_file"
    ATTACH_FILE_SHARP = "attach_file_sharp"
    ATTACH_FILE_ROUNDED = "attach_file_rounded"
    ATTACH_FILE_OUTLINED = "attach_file_outlined"
    ATTACH_MONEY = "attach_money"
    ATTACH_MONEY_SHARP = "attach_money_sharp"
    ATTACH_MONEY_ROUNDED = "attach_money_rounded"
    ATTACH_MONEY_OUTLINED = "attach_money_outlined"
    ATTACHMENT = "attachment"
    ATTACHMENT_SHARP = "attachment_sharp"
    ATTACHMENT_ROUNDED = "attachment_rounded"
    ATTACHMENT_OUTLINED = "attachment_outlined"
    ATTRACTIONS = "attractions"
    ATTRACTIONS_SHARP = "attractions_sharp"
    ATTRACTIONS_ROUNDED = "attractions_rounded"
    ATTRACTIONS_OUTLINED = "attractions_outlined"
    ATTRIBUTION = "attribution"
    ATTRIBUTION_SHARP = "attribution_sharp"
    ATTRIBUTION_ROUNDED = "attribution_rounded"
    ATTRIBUTION_OUTLINED = "attribution_outlined"
    AUDIO_FILE = "audio_file"
    AUDIO_FILE_SHARP = "audio_file_sharp"
    AUDIO_FILE_ROUNDED = "audio_file_rounded"
    AUDIO_FILE_OUTLINED = "audio_file_outlined"
    AUDIOTRACK = "audiotrack"
    AUDIOTRACK_SHARP = "audiotrack_sharp"
    AUDIOTRACK_ROUNDED = "audiotrack_rounded"
    AUDIOTRACK_OUTLINED = "audiotrack_outlined"
    AUTO_AWESOME = "auto_awesome"
    AUTO_AWESOME_SHARP = "auto_awesome_sharp"
    AUTO_AWESOME_ROUNDED = "auto_awesome_rounded"
    AUTO_AWESOME_OUTLINED = "auto_awesome_outlined"
    AUTO_AWESOME_MOSAIC = "auto_awesome_mosaic"
    AUTO_AWESOME_MOSAIC_SHARP = "auto_awesome_mosaic_sharp"
    AUTO_AWESOME_MOSAIC_ROUNDED = "auto_awesome_mosaic_rounded"
    AUTO_AWESOME_MOSAIC_OUTLINED = "auto_awesome_mosaic_outlined"
    AUTO_AWESOME_MOTION = "auto_awesome_motion"
    AUTO_AWESOME_MOTION_SHARP = "auto_awesome_motion_sharp"
    AUTO_AWESOME_MOTION_ROUNDED = "auto_awesome_motion_rounded"
    AUTO_AWESOME_MOTION_OUTLINED = "auto_awesome_motion_outlined"
    AUTO_DELETE = "auto_delete"
    AUTO_DELETE_SHARP = "auto_delete_sharp"
    AUTO_DELETE_ROUNDED = "auto_delete_rounded"
    AUTO_DELETE_OUTLINED = "auto_delete_outlined"
    AUTO_FIX_HIGH = "auto_fix_high"
    AUTO_FIX_HIGH_SHARP = "auto_fix_high_sharp"
    AUTO_FIX_HIGH_ROUNDED = "auto_fix_high_rounded"
    AUTO_FIX_HIGH_OUTLINED = "auto_fix_high_outlined"
    AUTO_FIX_NORMAL = "auto_fix_normal"
    AUTO_FIX_NORMAL_SHARP = "auto_fix_normal_sharp"
    AUTO_FIX_NORMAL_ROUNDED = "auto_fix_normal_rounded"
    AUTO_FIX_NORMAL_OUTLINED = "auto_fix_normal_outlined"
    AUTO_FIX_OFF = "auto_fix_off"
    AUTO_FIX_OFF_SHARP = "auto_fix_off_sharp"
    AUTO_FIX_OFF_ROUNDED = "auto_fix_off_rounded"
    AUTO_FIX_OFF_OUTLINED = "auto_fix_off_outlined"
    AUTO_GRAPH = "auto_graph"
    AUTO_GRAPH_SHARP = "auto_graph_sharp"
    AUTO_GRAPH_ROUNDED = "auto_graph_rounded"
    AUTO_GRAPH_OUTLINED = "auto_graph_outlined"
    AUTO_MODE = "auto_mode"
    AUTO_MODE_SHARP = "auto_mode_sharp"
    AUTO_MODE_ROUNDED = "auto_mode_rounded"
    AUTO_MODE_OUTLINED = "auto_mode_outlined"
    AUTO_STORIES = "auto_stories"
    AUTO_STORIES_SHARP = "auto_stories_sharp"
    AUTO_STORIES_ROUNDED = "auto_stories_rounded"
    AUTO_STORIES_OUTLINED = "auto_stories_outlined"
    AUTOFPS_SELECT = "autofps_select"
    AUTOFPS_SELECT_SHARP = "autofps_select_sharp"
    AUTOFPS_SELECT_ROUNDED = "autofps_select_rounded"
    AUTOFPS_SELECT_OUTLINED = "autofps_select_outlined"
    AUTORENEW = "autorenew"
    AUTORENEW_SHARP = "autorenew_sharp"
    AUTORENEW_ROUNDED = "autorenew_rounded"
    AUTORENEW_OUTLINED = "autorenew_outlined"
    AV_TIMER = "av_timer"
    AV_TIMER_SHARP = "av_timer_sharp"
    AV_TIMER_ROUNDED = "av_timer_rounded"
    AV_TIMER_OUTLINED = "av_timer_outlined"
    BABY_CHANGING_STATION = "baby_changing_station"
    BABY_CHANGING_STATION_SHARP = "baby_changing_station_sharp"
    BABY_CHANGING_STATION_ROUNDED = "baby_changing_station_rounded"
    BABY_CHANGING_STATION_OUTLINED = "baby_changing_station_outlined"
    BACK_HAND = "back_hand"
    BACK_HAND_SHARP = "back_hand_sharp"
    BACK_HAND_ROUNDED = "back_hand_rounded"
    BACK_HAND_OUTLINED = "back_hand_outlined"
    BACKPACK = "backpack"
    BACKPACK_SHARP = "backpack_sharp"
    BACKPACK_ROUNDED = "backpack_rounded"
    BACKPACK_OUTLINED = "backpack_outlined"
    BACKSPACE = "backspace"
    BACKSPACE_SHARP = "backspace_sharp"
    BACKSPACE_ROUNDED = "backspace_rounded"
    BACKSPACE_OUTLINED = "backspace_outlined"
    BACKUP = "backup"
    BACKUP_SHARP = "backup_sharp"
    BACKUP_ROUNDED = "backup_rounded"
    BACKUP_OUTLINED = "backup_outlined"
    BACKUP_TABLE = "backup_table"
    BACKUP_TABLE_SHARP = "backup_table_sharp"
    BACKUP_TABLE_ROUNDED = "backup_table_rounded"
    BACKUP_TABLE_OUTLINED = "backup_table_outlined"
    BADGE = "badge"
    BADGE_SHARP = "badge_sharp"
    BADGE_ROUNDED = "badge_rounded"
    BADGE_OUTLINED = "badge_outlined"
    BAKERY_DINING = "bakery_dining"
    BAKERY_DINING_SHARP = "bakery_dining_sharp"
    BAKERY_DINING_ROUNDED = "bakery_dining_rounded"
    BAKERY_DINING_OUTLINED = "bakery_dining_outlined"
    BALANCE = "balance"
    BALANCE_SHARP = "balance_sharp"
    BALANCE_ROUNDED = "balance_rounded"
    BALANCE_OUTLINED = "balance_outlined"
    BALCONY = "balcony"
    BALCONY_SHARP = "balcony_sharp"
    BALCONY_ROUNDED = "balcony_rounded"
    BALCONY_OUTLINED = "balcony_outlined"
    BALLOT = "ballot"
    BALLOT_SHARP = "ballot_sharp"
    BALLOT_ROUNDED = "ballot_rounded"
    BALLOT_OUTLINED = "ballot_outlined"
    BAR_CHART = "bar_chart"
    BAR_CHART_SHARP = "bar_chart_sharp"
    BAR_CHART_ROUNDED = "bar_chart_rounded"
    BAR_CHART_OUTLINED = "bar_chart_outlined"
    BARCODE_READER = "barcode_reader"
    BATCH_PREDICTION = "batch_prediction"
    BATCH_PREDICTION_SHARP = "batch_prediction_sharp"
    BATCH_PREDICTION_ROUNDED = "batch_prediction_rounded"
    BATCH_PREDICTION_OUTLINED = "batch_prediction_outlined"
    BATHROOM = "bathroom"
    BATHROOM_SHARP = "bathroom_sharp"
    BATHROOM_ROUNDED = "bathroom_rounded"
    BATHROOM_OUTLINED = "bathroom_outlined"
    BATHTUB = "bathtub"
    BATHTUB_SHARP = "bathtub_sharp"
    BATHTUB_ROUNDED = "bathtub_rounded"
    BATHTUB_OUTLINED = "bathtub_outlined"
    BATTERY_0_BAR = "battery_0_bar"
    BATTERY_0_BAR_SHARP = "battery_0_bar_sharp"
    BATTERY_0_BAR_ROUNDED = "battery_0_bar_rounded"
    BATTERY_0_BAR_OUTLINED = "battery_0_bar_outlined"
    BATTERY_1_BAR = "battery_1_bar"
    BATTERY_1_BAR_SHARP = "battery_1_bar_sharp"
    BATTERY_1_BAR_ROUNDED = "battery_1_bar_rounded"
    BATTERY_1_BAR_OUTLINED = "battery_1_bar_outlined"
    BATTERY_2_BAR = "battery_2_bar"
    BATTERY_2_BAR_SHARP = "battery_2_bar_sharp"
    BATTERY_2_BAR_ROUNDED = "battery_2_bar_rounded"
    BATTERY_2_BAR_OUTLINED = "battery_2_bar_outlined"
    BATTERY_3_BAR = "battery_3_bar"
    BATTERY_3_BAR_SHARP = "battery_3_bar_sharp"
    BATTERY_3_BAR_ROUNDED = "battery_3_bar_rounded"
    BATTERY_3_BAR_OUTLINED = "battery_3_bar_outlined"
    BATTERY_4_BAR = "battery_4_bar"
    BATTERY_4_BAR_SHARP = "battery_4_bar_sharp"
    BATTERY_4_BAR_ROUNDED = "battery_4_bar_rounded"
    BATTERY_4_BAR_OUTLINED = "battery_4_bar_outlined"
    BATTERY_5_BAR = "battery_5_bar"
    BATTERY_5_BAR_SHARP = "battery_5_bar_sharp"
    BATTERY_5_BAR_ROUNDED = "battery_5_bar_rounded"
    BATTERY_5_BAR_OUTLINED = "battery_5_bar_outlined"
    BATTERY_6_BAR = "battery_6_bar"
    BATTERY_6_BAR_SHARP = "battery_6_bar_sharp"
    BATTERY_6_BAR_ROUNDED = "battery_6_bar_rounded"
    BATTERY_6_BAR_OUTLINED = "battery_6_bar_outlined"
    BATTERY_ALERT = "battery_alert"
    BATTERY_ALERT_SHARP = "battery_alert_sharp"
    BATTERY_ALERT_ROUNDED = "battery_alert_rounded"
    BATTERY_ALERT_OUTLINED = "battery_alert_outlined"
    BATTERY_CHARGING_FULL = "battery_charging_full"
    BATTERY_CHARGING_FULL_SHARP = "battery_charging_full_sharp"
    BATTERY_CHARGING_FULL_ROUNDED = "battery_charging_full_rounded"
    BATTERY_CHARGING_FULL_OUTLINED = "battery_charging_full_outlined"
    BATTERY_FULL = "battery_full"
    BATTERY_FULL_SHARP = "battery_full_sharp"
    BATTERY_FULL_ROUNDED = "battery_full_rounded"
    BATTERY_FULL_OUTLINED = "battery_full_outlined"
    BATTERY_SAVER = "battery_saver"
    BATTERY_SAVER_SHARP = "battery_saver_sharp"
    BATTERY_SAVER_ROUNDED = "battery_saver_rounded"
    BATTERY_SAVER_OUTLINED = "battery_saver_outlined"
    BATTERY_STD = "battery_std"
    BATTERY_STD_SHARP = "battery_std_sharp"
    BATTERY_STD_ROUNDED = "battery_std_rounded"
    BATTERY_STD_OUTLINED = "battery_std_outlined"
    BATTERY_UNKNOWN = "battery_unknown"
    BATTERY_UNKNOWN_SHARP = "battery_unknown_sharp"
    BATTERY_UNKNOWN_ROUNDED = "battery_unknown_rounded"
    BATTERY_UNKNOWN_OUTLINED = "battery_unknown_outlined"
    BEACH_ACCESS = "beach_access"
    BEACH_ACCESS_SHARP = "beach_access_sharp"
    BEACH_ACCESS_ROUNDED = "beach_access_rounded"
    BEACH_ACCESS_OUTLINED = "beach_access_outlined"
    BED = "bed"
    BED_SHARP = "bed_sharp"
    BED_ROUNDED = "bed_rounded"
    BED_OUTLINED = "bed_outlined"
    BEDROOM_BABY = "bedroom_baby"
    BEDROOM_BABY_SHARP = "bedroom_baby_sharp"
    BEDROOM_BABY_ROUNDED = "bedroom_baby_rounded"
    BEDROOM_BABY_OUTLINED = "bedroom_baby_outlined"
    BEDROOM_CHILD = "bedroom_child"
    BEDROOM_CHILD_SHARP = "bedroom_child_sharp"
    BEDROOM_CHILD_ROUNDED = "bedroom_child_rounded"
    BEDROOM_CHILD_OUTLINED = "bedroom_child_outlined"
    BEDROOM_PARENT = "bedroom_parent"
    BEDROOM_PARENT_SHARP = "bedroom_parent_sharp"
    BEDROOM_PARENT_ROUNDED = "bedroom_parent_rounded"
    BEDROOM_PARENT_OUTLINED = "bedroom_parent_outlined"
    BEDTIME = "bedtime"
    BEDTIME_SHARP = "bedtime_sharp"
    BEDTIME_ROUNDED = "bedtime_rounded"
    BEDTIME_OUTLINED = "bedtime_outlined"
    BEDTIME_OFF = "bedtime_off"
    BEDTIME_OFF_SHARP = "bedtime_off_sharp"
    BEDTIME_OFF_ROUNDED = "bedtime_off_rounded"
    BEDTIME_OFF_OUTLINED = "bedtime_off_outlined"
    BEENHERE = "beenhere"
    BEENHERE_SHARP = "beenhere_sharp"
    BEENHERE_ROUNDED = "beenhere_rounded"
    BEENHERE_OUTLINED = "beenhere_outlined"
    BENTO = "bento"
    BENTO_SHARP = "bento_sharp"
    BENTO_ROUNDED = "bento_rounded"
    BENTO_OUTLINED = "bento_outlined"
    BIKE_SCOOTER = "bike_scooter"
    BIKE_SCOOTER_SHARP = "bike_scooter_sharp"
    BIKE_SCOOTER_ROUNDED = "bike_scooter_rounded"
    BIKE_SCOOTER_OUTLINED = "bike_scooter_outlined"
    BIOTECH = "biotech"
    BIOTECH_SHARP = "biotech_sharp"
    BIOTECH_ROUNDED = "biotech_rounded"
    BIOTECH_OUTLINED = "biotech_outlined"
    BLENDER = "blender"
    BLENDER_SHARP = "blender_sharp"
    BLENDER_ROUNDED = "blender_rounded"
    BLENDER_OUTLINED = "blender_outlined"
    BLIND = "blind"
    BLIND_SHARP = "blind_sharp"
    BLIND_ROUNDED = "blind_rounded"
    BLIND_OUTLINED = "blind_outlined"
    BLINDS = "blinds"
    BLINDS_SHARP = "blinds_sharp"
    BLINDS_ROUNDED = "blinds_rounded"
    BLINDS_OUTLINED = "blinds_outlined"
    BLINDS_CLOSED = "blinds_closed"
    BLINDS_CLOSED_SHARP = "blinds_closed_sharp"
    BLINDS_CLOSED_ROUNDED = "blinds_closed_rounded"
    BLINDS_CLOSED_OUTLINED = "blinds_closed_outlined"
    BLOCK = "block"
    BLOCK_SHARP = "block_sharp"
    BLOCK_ROUNDED = "block_rounded"
    BLOCK_OUTLINED = "block_outlined"
    BLOCK_FLIPPED = "block_flipped"
    BLOODTYPE = "bloodtype"
    BLOODTYPE_SHARP = "bloodtype_sharp"
    BLOODTYPE_ROUNDED = "bloodtype_rounded"
    BLOODTYPE_OUTLINED = "bloodtype_outlined"
    BLUETOOTH = "bluetooth"
    BLUETOOTH_SHARP = "bluetooth_sharp"
    BLUETOOTH_ROUNDED = "bluetooth_rounded"
    BLUETOOTH_OUTLINED = "bluetooth_outlined"
    BLUETOOTH_AUDIO = "bluetooth_audio"
    BLUETOOTH_AUDIO_SHARP = "bluetooth_audio_sharp"
    BLUETOOTH_AUDIO_ROUNDED = "bluetooth_audio_rounded"
    BLUETOOTH_AUDIO_OUTLINED = "bluetooth_audio_outlined"
    BLUETOOTH_CONNECTED = "bluetooth_connected"
    BLUETOOTH_CONNECTED_SHARP = "bluetooth_connected_sharp"
    BLUETOOTH_CONNECTED_ROUNDED = "bluetooth_connected_rounded"
    BLUETOOTH_CONNECTED_OUTLINED = "bluetooth_connected_outlined"
    BLUETOOTH_DISABLED = "bluetooth_disabled"
    BLUETOOTH_DISABLED_SHARP = "bluetooth_disabled_sharp"
    BLUETOOTH_DISABLED_ROUNDED = "bluetooth_disabled_rounded"
    BLUETOOTH_DISABLED_OUTLINED = "bluetooth_disabled_outlined"
    BLUETOOTH_DRIVE = "bluetooth_drive"
    BLUETOOTH_DRIVE_SHARP = "bluetooth_drive_sharp"
    BLUETOOTH_DRIVE_ROUNDED = "bluetooth_drive_rounded"
    BLUETOOTH_DRIVE_OUTLINED = "bluetooth_drive_outlined"
    BLUETOOTH_SEARCHING = "bluetooth_searching"
    BLUETOOTH_SEARCHING_SHARP = "bluetooth_searching_sharp"
    BLUETOOTH_SEARCHING_ROUNDED = "bluetooth_searching_rounded"
    BLUETOOTH_SEARCHING_OUTLINED = "bluetooth_searching_outlined"
    BLUR_CIRCULAR = "blur_circular"
    BLUR_CIRCULAR_SHARP = "blur_circular_sharp"
    BLUR_CIRCULAR_ROUNDED = "blur_circular_rounded"
    BLUR_CIRCULAR_OUTLINED = "blur_circular_outlined"
    BLUR_LINEAR = "blur_linear"
    BLUR_LINEAR_SHARP = "blur_linear_sharp"
    BLUR_LINEAR_ROUNDED = "blur_linear_rounded"
    BLUR_LINEAR_OUTLINED = "blur_linear_outlined"
    BLUR_OFF = "blur_off"
    BLUR_OFF_SHARP = "blur_off_sharp"
    BLUR_OFF_ROUNDED = "blur_off_rounded"
    BLUR_OFF_OUTLINED = "blur_off_outlined"
    BLUR_ON = "blur_on"
    BLUR_ON_SHARP = "blur_on_sharp"
    BLUR_ON_ROUNDED = "blur_on_rounded"
    BLUR_ON_OUTLINED = "blur_on_outlined"
    BOLT = "bolt"
    BOLT_SHARP = "bolt_sharp"
    BOLT_ROUNDED = "bolt_rounded"
    BOLT_OUTLINED = "bolt_outlined"
    BOOK = "book"
    BOOK_SHARP = "book_sharp"
    BOOK_ROUNDED = "book_rounded"
    BOOK_OUTLINED = "book_outlined"
    BOOK_ONLINE = "book_online"
    BOOK_ONLINE_SHARP = "book_online_sharp"
    BOOK_ONLINE_ROUNDED = "book_online_rounded"
    BOOK_ONLINE_OUTLINED = "book_online_outlined"
    BOOKMARK = "bookmark"
    BOOKMARK_SHARP = "bookmark_sharp"
    BOOKMARK_ROUNDED = "bookmark_rounded"
    BOOKMARK_OUTLINED = "bookmark_outlined"
    BOOKMARK_ADD = "bookmark_add"
    BOOKMARK_ADD_SHARP = "bookmark_add_sharp"
    BOOKMARK_ADD_ROUNDED = "bookmark_add_rounded"
    BOOKMARK_ADD_OUTLINED = "bookmark_add_outlined"
    BOOKMARK_ADDED = "bookmark_added"
    BOOKMARK_ADDED_SHARP = "bookmark_added_sharp"
    BOOKMARK_ADDED_ROUNDED = "bookmark_added_rounded"
    BOOKMARK_ADDED_OUTLINED = "bookmark_added_outlined"
    BOOKMARK_BORDER = "bookmark_border"
    BOOKMARK_BORDER_SHARP = "bookmark_border_sharp"
    BOOKMARK_BORDER_ROUNDED = "bookmark_border_rounded"
    BOOKMARK_BORDER_OUTLINED = "bookmark_border_outlined"
    BOOKMARK_OUTLINE = "bookmark_outline"
    BOOKMARK_OUTLINE_SHARP = "bookmark_outline_sharp"
    BOOKMARK_OUTLINE_ROUNDED = "bookmark_outline_rounded"
    BOOKMARK_OUTLINE_OUTLINED = "bookmark_outline_outlined"
    BOOKMARK_REMOVE = "bookmark_remove"
    BOOKMARK_REMOVE_SHARP = "bookmark_remove_sharp"
    BOOKMARK_REMOVE_ROUNDED = "bookmark_remove_rounded"
    BOOKMARK_REMOVE_OUTLINED = "bookmark_remove_outlined"
    BOOKMARKS = "bookmarks"
    BOOKMARKS_SHARP = "bookmarks_sharp"
    BOOKMARKS_ROUNDED = "bookmarks_rounded"
    BOOKMARKS_OUTLINED = "bookmarks_outlined"
    BORDER_ALL = "border_all"
    BORDER_ALL_SHARP = "border_all_sharp"
    BORDER_ALL_ROUNDED = "border_all_rounded"
    BORDER_ALL_OUTLINED = "border_all_outlined"
    BORDER_BOTTOM = "border_bottom"
    BORDER_BOTTOM_SHARP = "border_bottom_sharp"
    BORDER_BOTTOM_ROUNDED = "border_bottom_rounded"
    BORDER_BOTTOM_OUTLINED = "border_bottom_outlined"
    BORDER_CLEAR = "border_clear"
    BORDER_CLEAR_SHARP = "border_clear_sharp"
    BORDER_CLEAR_ROUNDED = "border_clear_rounded"
    BORDER_CLEAR_OUTLINED = "border_clear_outlined"
    BORDER_COLOR = "border_color"
    BORDER_COLOR_SHARP = "border_color_sharp"
    BORDER_COLOR_ROUNDED = "border_color_rounded"
    BORDER_COLOR_OUTLINED = "border_color_outlined"
    BORDER_HORIZONTAL = "border_horizontal"
    BORDER_HORIZONTAL_SHARP = "border_horizontal_sharp"
    BORDER_HORIZONTAL_ROUNDED = "border_horizontal_rounded"
    BORDER_HORIZONTAL_OUTLINED = "border_horizontal_outlined"
    BORDER_INNER = "border_inner"
    BORDER_INNER_SHARP = "border_inner_sharp"
    BORDER_INNER_ROUNDED = "border_inner_rounded"
    BORDER_INNER_OUTLINED = "border_inner_outlined"
    BORDER_LEFT = "border_left"
    BORDER_LEFT_SHARP = "border_left_sharp"
    BORDER_LEFT_ROUNDED = "border_left_rounded"
    BORDER_LEFT_OUTLINED = "border_left_outlined"
    BORDER_OUTER = "border_outer"
    BORDER_OUTER_SHARP = "border_outer_sharp"
    BORDER_OUTER_ROUNDED = "border_outer_rounded"
    BORDER_OUTER_OUTLINED = "border_outer_outlined"
    BORDER_RIGHT = "border_right"
    BORDER_RIGHT_SHARP = "border_right_sharp"
    BORDER_RIGHT_ROUNDED = "border_right_rounded"
    BORDER_RIGHT_OUTLINED = "border_right_outlined"
    BORDER_STYLE = "border_style"
    BORDER_STYLE_SHARP = "border_style_sharp"
    BORDER_STYLE_ROUNDED = "border_style_rounded"
    BORDER_STYLE_OUTLINED = "border_style_outlined"
    BORDER_TOP = "border_top"
    BORDER_TOP_SHARP = "border_top_sharp"
    BORDER_TOP_ROUNDED = "border_top_rounded"
    BORDER_TOP_OUTLINED = "border_top_outlined"
    BORDER_VERTICAL = "border_vertical"
    BORDER_VERTICAL_SHARP = "border_vertical_sharp"
    BORDER_VERTICAL_ROUNDED = "border_vertical_rounded"
    BORDER_VERTICAL_OUTLINED = "border_vertical_outlined"
    BOY = "boy"
    BOY_SHARP = "boy_sharp"
    BOY_ROUNDED = "boy_rounded"
    BOY_OUTLINED = "boy_outlined"
    BRANDING_WATERMARK = "branding_watermark"
    BRANDING_WATERMARK_SHARP = "branding_watermark_sharp"
    BRANDING_WATERMARK_ROUNDED = "branding_watermark_rounded"
    BRANDING_WATERMARK_OUTLINED = "branding_watermark_outlined"
    BREAKFAST_DINING = "breakfast_dining"
    BREAKFAST_DINING_SHARP = "breakfast_dining_sharp"
    BREAKFAST_DINING_ROUNDED = "breakfast_dining_rounded"
    BREAKFAST_DINING_OUTLINED = "breakfast_dining_outlined"
    BRIGHTNESS_1 = "brightness_1"
    BRIGHTNESS_1_SHARP = "brightness_1_sharp"
    BRIGHTNESS_1_ROUNDED = "brightness_1_rounded"
    BRIGHTNESS_1_OUTLINED = "brightness_1_outlined"
    BRIGHTNESS_2 = "brightness_2"
    BRIGHTNESS_2_SHARP = "brightness_2_sharp"
    BRIGHTNESS_2_ROUNDED = "brightness_2_rounded"
    BRIGHTNESS_2_OUTLINED = "brightness_2_outlined"
    BRIGHTNESS_3 = "brightness_3"
    BRIGHTNESS_3_SHARP = "brightness_3_sharp"
    BRIGHTNESS_3_ROUNDED = "brightness_3_rounded"
    BRIGHTNESS_3_OUTLINED = "brightness_3_outlined"
    BRIGHTNESS_4 = "brightness_4"
    BRIGHTNESS_4_SHARP = "brightness_4_sharp"
    BRIGHTNESS_4_ROUNDED = "brightness_4_rounded"
    BRIGHTNESS_4_OUTLINED = "brightness_4_outlined"
    BRIGHTNESS_5 = "brightness_5"
    BRIGHTNESS_5_SHARP = "brightness_5_sharp"
    BRIGHTNESS_5_ROUNDED = "brightness_5_rounded"
    BRIGHTNESS_5_OUTLINED = "brightness_5_outlined"
    BRIGHTNESS_6 = "brightness_6"
    BRIGHTNESS_6_SHARP = "brightness_6_sharp"
    BRIGHTNESS_6_ROUNDED = "brightness_6_rounded"
    BRIGHTNESS_6_OUTLINED = "brightness_6_outlined"
    BRIGHTNESS_7 = "brightness_7"
    BRIGHTNESS_7_SHARP = "brightness_7_sharp"
    BRIGHTNESS_7_ROUNDED = "brightness_7_rounded"
    BRIGHTNESS_7_OUTLINED = "brightness_7_outlined"
    BRIGHTNESS_AUTO = "brightness_auto"
    BRIGHTNESS_AUTO_SHARP = "brightness_auto_sharp"
    BRIGHTNESS_AUTO_ROUNDED = "brightness_auto_rounded"
    BRIGHTNESS_AUTO_OUTLINED = "brightness_auto_outlined"
    BRIGHTNESS_HIGH = "brightness_high"
    BRIGHTNESS_HIGH_SHARP = "brightness_high_sharp"
    BRIGHTNESS_HIGH_ROUNDED = "brightness_high_rounded"
    BRIGHTNESS_HIGH_OUTLINED = "brightness_high_outlined"
    BRIGHTNESS_LOW = "brightness_low"
    BRIGHTNESS_LOW_SHARP = "brightness_low_sharp"
    BRIGHTNESS_LOW_ROUNDED = "brightness_low_rounded"
    BRIGHTNESS_LOW_OUTLINED = "brightness_low_outlined"
    BRIGHTNESS_MEDIUM = "brightness_medium"
    BRIGHTNESS_MEDIUM_SHARP = "brightness_medium_sharp"
    BRIGHTNESS_MEDIUM_ROUNDED = "brightness_medium_rounded"
    BRIGHTNESS_MEDIUM_OUTLINED = "brightness_medium_outlined"
    BROADCAST_ON_HOME = "broadcast_on_home"
    BROADCAST_ON_HOME_SHARP = "broadcast_on_home_sharp"
    BROADCAST_ON_HOME_ROUNDED = "broadcast_on_home_rounded"
    BROADCAST_ON_HOME_OUTLINED = "broadcast_on_home_outlined"
    BROADCAST_ON_PERSONAL = "broadcast_on_personal"
    BROADCAST_ON_PERSONAL_SHARP = "broadcast_on_personal_sharp"
    BROADCAST_ON_PERSONAL_ROUNDED = "broadcast_on_personal_rounded"
    BROADCAST_ON_PERSONAL_OUTLINED = "broadcast_on_personal_outlined"
    BROKEN_IMAGE = "broken_image"
    BROKEN_IMAGE_SHARP = "broken_image_sharp"
    BROKEN_IMAGE_ROUNDED = "broken_image_rounded"
    BROKEN_IMAGE_OUTLINED = "broken_image_outlined"
    BROWSE_GALLERY = "browse_gallery"
    BROWSE_GALLERY_SHARP = "browse_gallery_sharp"
    BROWSE_GALLERY_ROUNDED = "browse_gallery_rounded"
    BROWSE_GALLERY_OUTLINED = "browse_gallery_outlined"
    BROWSER_NOT_SUPPORTED = "browser_not_supported"
    BROWSER_NOT_SUPPORTED_SHARP = "browser_not_supported_sharp"
    BROWSER_NOT_SUPPORTED_ROUNDED = "browser_not_supported_rounded"
    BROWSER_NOT_SUPPORTED_OUTLINED = "browser_not_supported_outlined"
    BROWSER_UPDATED = "browser_updated"
    BROWSER_UPDATED_SHARP = "browser_updated_sharp"
    BROWSER_UPDATED_ROUNDED = "browser_updated_rounded"
    BROWSER_UPDATED_OUTLINED = "browser_updated_outlined"
    BRUNCH_DINING = "brunch_dining"
    BRUNCH_DINING_SHARP = "brunch_dining_sharp"
    BRUNCH_DINING_ROUNDED = "brunch_dining_rounded"
    BRUNCH_DINING_OUTLINED = "brunch_dining_outlined"
    BRUSH = "brush"
    BRUSH_SHARP = "brush_sharp"
    BRUSH_ROUNDED = "brush_rounded"
    BRUSH_OUTLINED = "brush_outlined"
    BUBBLE_CHART = "bubble_chart"
    BUBBLE_CHART_SHARP = "bubble_chart_sharp"
    BUBBLE_CHART_ROUNDED = "bubble_chart_rounded"
    BUBBLE_CHART_OUTLINED = "bubble_chart_outlined"
    BUG_REPORT = "bug_report"
    BUG_REPORT_SHARP = "bug_report_sharp"
    BUG_REPORT_ROUNDED = "bug_report_rounded"
    BUG_REPORT_OUTLINED = "bug_report_outlined"
    BUILD = "build"
    BUILD_SHARP = "build_sharp"
    BUILD_ROUNDED = "build_rounded"
    BUILD_OUTLINED = "build_outlined"
    BUILD_CIRCLE = "build_circle"
    BUILD_CIRCLE_SHARP = "build_circle_sharp"
    BUILD_CIRCLE_ROUNDED = "build_circle_rounded"
    BUILD_CIRCLE_OUTLINED = "build_circle_outlined"
    BUNGALOW = "bungalow"
    BUNGALOW_SHARP = "bungalow_sharp"
    BUNGALOW_ROUNDED = "bungalow_rounded"
    BUNGALOW_OUTLINED = "bungalow_outlined"
    BURST_MODE = "burst_mode"
    BURST_MODE_SHARP = "burst_mode_sharp"
    BURST_MODE_ROUNDED = "burst_mode_rounded"
    BURST_MODE_OUTLINED = "burst_mode_outlined"
    BUS_ALERT = "bus_alert"
    BUS_ALERT_SHARP = "bus_alert_sharp"
    BUS_ALERT_ROUNDED = "bus_alert_rounded"
    BUS_ALERT_OUTLINED = "bus_alert_outlined"
    BUSINESS = "business"
    BUSINESS_SHARP = "business_sharp"
    BUSINESS_ROUNDED = "business_rounded"
    BUSINESS_OUTLINED = "business_outlined"
    BUSINESS_CENTER = "business_center"
    BUSINESS_CENTER_SHARP = "business_center_sharp"
    BUSINESS_CENTER_ROUNDED = "business_center_rounded"
    BUSINESS_CENTER_OUTLINED = "business_center_outlined"
    CABIN = "cabin"
    CABIN_SHARP = "cabin_sharp"
    CABIN_ROUNDED = "cabin_rounded"
    CABIN_OUTLINED = "cabin_outlined"
    CABLE = "cable"
    CABLE_SHARP = "cable_sharp"
    CABLE_ROUNDED = "cable_rounded"
    CABLE_OUTLINED = "cable_outlined"
    CACHED = "cached"
    CACHED_SHARP = "cached_sharp"
    CACHED_ROUNDED = "cached_rounded"
    CACHED_OUTLINED = "cached_outlined"
    CAKE = "cake"
    CAKE_SHARP = "cake_sharp"
    CAKE_ROUNDED = "cake_rounded"
    CAKE_OUTLINED = "cake_outlined"
    CALCULATE = "calculate"
    CALCULATE_SHARP = "calculate_sharp"
    CALCULATE_ROUNDED = "calculate_rounded"
    CALCULATE_OUTLINED = "calculate_outlined"
    CALENDAR_MONTH = "calendar_month"
    CALENDAR_MONTH_SHARP = "calendar_month_sharp"
    CALENDAR_MONTH_ROUNDED = "calendar_month_rounded"
    CALENDAR_MONTH_OUTLINED = "calendar_month_outlined"
    CALENDAR_TODAY = "calendar_today"
    CALENDAR_TODAY_SHARP = "calendar_today_sharp"
    CALENDAR_TODAY_ROUNDED = "calendar_today_rounded"
    CALENDAR_TODAY_OUTLINED = "calendar_today_outlined"
    CALENDAR_VIEW_DAY = "calendar_view_day"
    CALENDAR_VIEW_DAY_SHARP = "calendar_view_day_sharp"
    CALENDAR_VIEW_DAY_ROUNDED = "calendar_view_day_rounded"
    CALENDAR_VIEW_DAY_OUTLINED = "calendar_view_day_outlined"
    CALENDAR_VIEW_MONTH = "calendar_view_month"
    CALENDAR_VIEW_MONTH_SHARP = "calendar_view_month_sharp"
    CALENDAR_VIEW_MONTH_ROUNDED = "calendar_view_month_rounded"
    CALENDAR_VIEW_MONTH_OUTLINED = "calendar_view_month_outlined"
    CALENDAR_VIEW_WEEK = "calendar_view_week"
    CALENDAR_VIEW_WEEK_SHARP = "calendar_view_week_sharp"
    CALENDAR_VIEW_WEEK_ROUNDED = "calendar_view_week_rounded"
    CALENDAR_VIEW_WEEK_OUTLINED = "calendar_view_week_outlined"
    CALL = "call"
    CALL_SHARP = "call_sharp"
    CALL_ROUNDED = "call_rounded"
    CALL_OUTLINED = "call_outlined"
    CALL_END = "call_end"
    CALL_END_SHARP = "call_end_sharp"
    CALL_END_ROUNDED = "call_end_rounded"
    CALL_END_OUTLINED = "call_end_outlined"
    CALL_MADE = "call_made"
    CALL_MADE_SHARP = "call_made_sharp"
    CALL_MADE_ROUNDED = "call_made_rounded"
    CALL_MADE_OUTLINED = "call_made_outlined"
    CALL_MERGE = "call_merge"
    CALL_MERGE_SHARP = "call_merge_sharp"
    CALL_MERGE_ROUNDED = "call_merge_rounded"
    CALL_MERGE_OUTLINED = "call_merge_outlined"
    CALL_MISSED = "call_missed"
    CALL_MISSED_SHARP = "call_missed_sharp"
    CALL_MISSED_ROUNDED = "call_missed_rounded"
    CALL_MISSED_OUTLINED = "call_missed_outlined"
    CALL_MISSED_OUTGOING = "call_missed_outgoing"
    CALL_MISSED_OUTGOING_SHARP = "call_missed_outgoing_sharp"
    CALL_MISSED_OUTGOING_ROUNDED = "call_missed_outgoing_rounded"
    CALL_MISSED_OUTGOING_OUTLINED = "call_missed_outgoing_outlined"
    CALL_RECEIVED = "call_received"
    CALL_RECEIVED_SHARP = "call_received_sharp"
    CALL_RECEIVED_ROUNDED = "call_received_rounded"
    CALL_RECEIVED_OUTLINED = "call_received_outlined"
    CALL_SPLIT = "call_split"
    CALL_SPLIT_SHARP = "call_split_sharp"
    CALL_SPLIT_ROUNDED = "call_split_rounded"
    CALL_SPLIT_OUTLINED = "call_split_outlined"
    CALL_TO_ACTION = "call_to_action"
    CALL_TO_ACTION_SHARP = "call_to_action_sharp"
    CALL_TO_ACTION_ROUNDED = "call_to_action_rounded"
    CALL_TO_ACTION_OUTLINED = "call_to_action_outlined"
    CAMERA = "camera"
    CAMERA_SHARP = "camera_sharp"
    CAMERA_ROUNDED = "camera_rounded"
    CAMERA_OUTLINED = "camera_outlined"
    CAMERA_ALT = "camera_alt"
    CAMERA_ALT_SHARP = "camera_alt_sharp"
    CAMERA_ALT_ROUNDED = "camera_alt_rounded"
    CAMERA_ALT_OUTLINED = "camera_alt_outlined"
    CAMERA_ENHANCE = "camera_enhance"
    CAMERA_ENHANCE_SHARP = "camera_enhance_sharp"
    CAMERA_ENHANCE_ROUNDED = "camera_enhance_rounded"
    CAMERA_ENHANCE_OUTLINED = "camera_enhance_outlined"
    CAMERA_FRONT = "camera_front"
    CAMERA_FRONT_SHARP = "camera_front_sharp"
    CAMERA_FRONT_ROUNDED = "camera_front_rounded"
    CAMERA_FRONT_OUTLINED = "camera_front_outlined"
    CAMERA_INDOOR = "camera_indoor"
    CAMERA_INDOOR_SHARP = "camera_indoor_sharp"
    CAMERA_INDOOR_ROUNDED = "camera_indoor_rounded"
    CAMERA_INDOOR_OUTLINED = "camera_indoor_outlined"
    CAMERA_OUTDOOR = "camera_outdoor"
    CAMERA_OUTDOOR_SHARP = "camera_outdoor_sharp"
    CAMERA_OUTDOOR_ROUNDED = "camera_outdoor_rounded"
    CAMERA_OUTDOOR_OUTLINED = "camera_outdoor_outlined"
    CAMERA_REAR = "camera_rear"
    CAMERA_REAR_SHARP = "camera_rear_sharp"
    CAMERA_REAR_ROUNDED = "camera_rear_rounded"
    CAMERA_REAR_OUTLINED = "camera_rear_outlined"
    CAMERA_ROLL = "camera_roll"
    CAMERA_ROLL_SHARP = "camera_roll_sharp"
    CAMERA_ROLL_ROUNDED = "camera_roll_rounded"
    CAMERA_ROLL_OUTLINED = "camera_roll_outlined"
    CAMERASWITCH = "cameraswitch"
    CAMERASWITCH_SHARP = "cameraswitch_sharp"
    CAMERASWITCH_ROUNDED = "cameraswitch_rounded"
    CAMERASWITCH_OUTLINED = "cameraswitch_outlined"
    CAMPAIGN = "campaign"
    CAMPAIGN_SHARP = "campaign_sharp"
    CAMPAIGN_ROUNDED = "campaign_rounded"
    CAMPAIGN_OUTLINED = "campaign_outlined"
    CANCEL = "cancel"
    CANCEL_SHARP = "cancel_sharp"
    CANCEL_ROUNDED = "cancel_rounded"
    CANCEL_OUTLINED = "cancel_outlined"
    CANCEL_PRESENTATION = "cancel_presentation"
    CANCEL_PRESENTATION_SHARP = "cancel_presentation_sharp"
    CANCEL_PRESENTATION_ROUNDED = "cancel_presentation_rounded"
    CANCEL_PRESENTATION_OUTLINED = "cancel_presentation_outlined"
    CANCEL_SCHEDULE_SEND = "cancel_schedule_send"
    CANCEL_SCHEDULE_SEND_SHARP = "cancel_schedule_send_sharp"
    CANCEL_SCHEDULE_SEND_ROUNDED = "cancel_schedule_send_rounded"
    CANCEL_SCHEDULE_SEND_OUTLINED = "cancel_schedule_send_outlined"
    CANDLESTICK_CHART = "candlestick_chart"
    CANDLESTICK_CHART_SHARP = "candlestick_chart_sharp"
    CANDLESTICK_CHART_ROUNDED = "candlestick_chart_rounded"
    CANDLESTICK_CHART_OUTLINED = "candlestick_chart_outlined"
    CAR_CRASH = "car_crash"
    CAR_CRASH_SHARP = "car_crash_sharp"
    CAR_CRASH_ROUNDED = "car_crash_rounded"
    CAR_CRASH_OUTLINED = "car_crash_outlined"
    CAR_RENTAL = "car_rental"
    CAR_RENTAL_SHARP = "car_rental_sharp"
    CAR_RENTAL_ROUNDED = "car_rental_rounded"
    CAR_RENTAL_OUTLINED = "car_rental_outlined"
    CAR_REPAIR = "car_repair"
    CAR_REPAIR_SHARP = "car_repair_sharp"
    CAR_REPAIR_ROUNDED = "car_repair_rounded"
    CAR_REPAIR_OUTLINED = "car_repair_outlined"
    CARD_GIFTCARD = "card_giftcard"
    CARD_GIFTCARD_SHARP = "card_giftcard_sharp"
    CARD_GIFTCARD_ROUNDED = "card_giftcard_rounded"
    CARD_GIFTCARD_OUTLINED = "card_giftcard_outlined"
    CARD_MEMBERSHIP = "card_membership"
    CARD_MEMBERSHIP_SHARP = "card_membership_sharp"
    CARD_MEMBERSHIP_ROUNDED = "card_membership_rounded"
    CARD_MEMBERSHIP_OUTLINED = "card_membership_outlined"
    CARD_TRAVEL = "card_travel"
    CARD_TRAVEL_SHARP = "card_travel_sharp"
    CARD_TRAVEL_ROUNDED = "card_travel_rounded"
    CARD_TRAVEL_OUTLINED = "card_travel_outlined"
    CARPENTER = "carpenter"
    CARPENTER_SHARP = "carpenter_sharp"
    CARPENTER_ROUNDED = "carpenter_rounded"
    CARPENTER_OUTLINED = "carpenter_outlined"
    CASES = "cases"
    CASES_SHARP = "cases_sharp"
    CASES_ROUNDED = "cases_rounded"
    CASES_OUTLINED = "cases_outlined"
    CASINO = "casino"
    CASINO_SHARP = "casino_sharp"
    CASINO_ROUNDED = "casino_rounded"
    CASINO_OUTLINED = "casino_outlined"
    CAST = "cast"
    CAST_SHARP = "cast_sharp"
    CAST_ROUNDED = "cast_rounded"
    CAST_OUTLINED = "cast_outlined"
    CAST_CONNECTED = "cast_connected"
    CAST_CONNECTED_SHARP = "cast_connected_sharp"
    CAST_CONNECTED_ROUNDED = "cast_connected_rounded"
    CAST_CONNECTED_OUTLINED = "cast_connected_outlined"
    CAST_FOR_EDUCATION = "cast_for_education"
    CAST_FOR_EDUCATION_SHARP = "cast_for_education_sharp"
    CAST_FOR_EDUCATION_ROUNDED = "cast_for_education_rounded"
    CAST_FOR_EDUCATION_OUTLINED = "cast_for_education_outlined"
    CASTLE = "castle"
    CASTLE_SHARP = "castle_sharp"
    CASTLE_ROUNDED = "castle_rounded"
    CASTLE_OUTLINED = "castle_outlined"
    CATCHING_POKEMON = "catching_pokemon"
    CATCHING_POKEMON_SHARP = "catching_pokemon_sharp"
    CATCHING_POKEMON_ROUNDED = "catching_pokemon_rounded"
    CATCHING_POKEMON_OUTLINED = "catching_pokemon_outlined"
    CATEGORY = "category"
    CATEGORY_SHARP = "category_sharp"
    CATEGORY_ROUNDED = "category_rounded"
    CATEGORY_OUTLINED = "category_outlined"
    CELEBRATION = "celebration"
    CELEBRATION_SHARP = "celebration_sharp"
    CELEBRATION_ROUNDED = "celebration_rounded"
    CELEBRATION_OUTLINED = "celebration_outlined"
    CELL_TOWER = "cell_tower"
    CELL_TOWER_SHARP = "cell_tower_sharp"
    CELL_TOWER_ROUNDED = "cell_tower_rounded"
    CELL_TOWER_OUTLINED = "cell_tower_outlined"
    CELL_WIFI = "cell_wifi"
    CELL_WIFI_SHARP = "cell_wifi_sharp"
    CELL_WIFI_ROUNDED = "cell_wifi_rounded"
    CELL_WIFI_OUTLINED = "cell_wifi_outlined"
    CENTER_FOCUS_STRONG = "center_focus_strong"
    CENTER_FOCUS_STRONG_SHARP = "center_focus_strong_sharp"
    CENTER_FOCUS_STRONG_ROUNDED = "center_focus_strong_rounded"
    CENTER_FOCUS_STRONG_OUTLINED = "center_focus_strong_outlined"
    CENTER_FOCUS_WEAK = "center_focus_weak"
    CENTER_FOCUS_WEAK_SHARP = "center_focus_weak_sharp"
    CENTER_FOCUS_WEAK_ROUNDED = "center_focus_weak_rounded"
    CENTER_FOCUS_WEAK_OUTLINED = "center_focus_weak_outlined"
    CHAIR = "chair"
    CHAIR_SHARP = "chair_sharp"
    CHAIR_ROUNDED = "chair_rounded"
    CHAIR_OUTLINED = "chair_outlined"
    CHAIR_ALT = "chair_alt"
    CHAIR_ALT_SHARP = "chair_alt_sharp"
    CHAIR_ALT_ROUNDED = "chair_alt_rounded"
    CHAIR_ALT_OUTLINED = "chair_alt_outlined"
    CHALET = "chalet"
    CHALET_SHARP = "chalet_sharp"
    CHALET_ROUNDED = "chalet_rounded"
    CHALET_OUTLINED = "chalet_outlined"
    CHANGE_CIRCLE = "change_circle"
    CHANGE_CIRCLE_SHARP = "change_circle_sharp"
    CHANGE_CIRCLE_ROUNDED = "change_circle_rounded"
    CHANGE_CIRCLE_OUTLINED = "change_circle_outlined"
    CHANGE_HISTORY = "change_history"
    CHANGE_HISTORY_SHARP = "change_history_sharp"
    CHANGE_HISTORY_ROUNDED = "change_history_rounded"
    CHANGE_HISTORY_OUTLINED = "change_history_outlined"
    CHARGING_STATION = "charging_station"
    CHARGING_STATION_SHARP = "charging_station_sharp"
    CHARGING_STATION_ROUNDED = "charging_station_rounded"
    CHARGING_STATION_OUTLINED = "charging_station_outlined"
    CHAT = "chat"
    CHAT_SHARP = "chat_sharp"
    CHAT_ROUNDED = "chat_rounded"
    CHAT_OUTLINED = "chat_outlined"
    CHAT_BUBBLE = "chat_bubble"
    CHAT_BUBBLE_SHARP = "chat_bubble_sharp"
    CHAT_BUBBLE_ROUNDED = "chat_bubble_rounded"
    CHAT_BUBBLE_OUTLINED = "chat_bubble_outlined"
    CHAT_BUBBLE_OUTLINE = "chat_bubble_outline"
    CHAT_BUBBLE_OUTLINE_SHARP = "chat_bubble_outline_sharp"
    CHAT_BUBBLE_OUTLINE_ROUNDED = "chat_bubble_outline_rounded"
    CHAT_BUBBLE_OUTLINE_OUTLINED = "chat_bubble_outline_outlined"
    CHECK = "check"
    CHECK_SHARP = "check_sharp"
    CHECK_ROUNDED = "check_rounded"
    CHECK_OUTLINED = "check_outlined"
    CHECK_BOX = "check_box"
    CHECK_BOX_SHARP = "check_box_sharp"
    CHECK_BOX_ROUNDED = "check_box_rounded"
    CHECK_BOX_OUTLINED = "check_box_outlined"
    CHECK_BOX_OUTLINE_BLANK = "check_box_outline_blank"
    CHECK_BOX_OUTLINE_BLANK_SHARP = "check_box_outline_blank_sharp"
    CHECK_BOX_OUTLINE_BLANK_ROUNDED = "check_box_outline_blank_rounded"
    CHECK_BOX_OUTLINE_BLANK_OUTLINED = "check_box_outline_blank_outlined"
    CHECK_CIRCLE = "check_circle"
    CHECK_CIRCLE_SHARP = "check_circle_sharp"
    CHECK_CIRCLE_ROUNDED = "check_circle_rounded"
    CHECK_CIRCLE_OUTLINED = "check_circle_outlined"
    CHECK_CIRCLE_OUTLINE = "check_circle_outline"
    CHECK_CIRCLE_OUTLINE_SHARP = "check_circle_outline_sharp"
    CHECK_CIRCLE_OUTLINE_ROUNDED = "check_circle_outline_rounded"
    CHECK_CIRCLE_OUTLINE_OUTLINED = "check_circle_outline_outlined"
    CHECKLIST = "checklist"
    CHECKLIST_SHARP = "checklist_sharp"
    CHECKLIST_ROUNDED = "checklist_rounded"
    CHECKLIST_OUTLINED = "checklist_outlined"
    CHECKLIST_RTL = "checklist_rtl"
    CHECKLIST_RTL_SHARP = "checklist_rtl_sharp"
    CHECKLIST_RTL_ROUNDED = "checklist_rtl_rounded"
    CHECKLIST_RTL_OUTLINED = "checklist_rtl_outlined"
    CHECKROOM = "checkroom"
    CHECKROOM_SHARP = "checkroom_sharp"
    CHECKROOM_ROUNDED = "checkroom_rounded"
    CHECKROOM_OUTLINED = "checkroom_outlined"
    CHEVRON_LEFT = "chevron_left"
    CHEVRON_LEFT_SHARP = "chevron_left_sharp"
    CHEVRON_LEFT_ROUNDED = "chevron_left_rounded"
    CHEVRON_LEFT_OUTLINED = "chevron_left_outlined"
    CHEVRON_RIGHT = "chevron_right"
    CHEVRON_RIGHT_SHARP = "chevron_right_sharp"
    CHEVRON_RIGHT_ROUNDED = "chevron_right_rounded"
    CHEVRON_RIGHT_OUTLINED = "chevron_right_outlined"
    CHILD_CARE = "child_care"
    CHILD_CARE_SHARP = "child_care_sharp"
    CHILD_CARE_ROUNDED = "child_care_rounded"
    CHILD_CARE_OUTLINED = "child_care_outlined"
    CHILD_FRIENDLY = "child_friendly"
    CHILD_FRIENDLY_SHARP = "child_friendly_sharp"
    CHILD_FRIENDLY_ROUNDED = "child_friendly_rounded"
    CHILD_FRIENDLY_OUTLINED = "child_friendly_outlined"
    CHROME_READER_MODE = "chrome_reader_mode"
    CHROME_READER_MODE_SHARP = "chrome_reader_mode_sharp"
    CHROME_READER_MODE_ROUNDED = "chrome_reader_mode_rounded"
    CHROME_READER_MODE_OUTLINED = "chrome_reader_mode_outlined"
    CHURCH = "church"
    CHURCH_SHARP = "church_sharp"
    CHURCH_ROUNDED = "church_rounded"
    CHURCH_OUTLINED = "church_outlined"
    CIRCLE = "circle"
    CIRCLE_SHARP = "circle_sharp"
    CIRCLE_ROUNDED = "circle_rounded"
    CIRCLE_OUTLINED = "circle_outlined"
    CIRCLE_NOTIFICATIONS = "circle_notifications"
    CIRCLE_NOTIFICATIONS_SHARP = "circle_notifications_sharp"
    CIRCLE_NOTIFICATIONS_ROUNDED = "circle_notifications_rounded"
    CIRCLE_NOTIFICATIONS_OUTLINED = "circle_notifications_outlined"
    CLASS_ = "class_"
    CLASS_SHARP = "class_sharp"
    CLASS_ROUNDED = "class_rounded"
    CLASS_OUTLINED = "class_outlined"
    CLEAN_HANDS = "clean_hands"
    CLEAN_HANDS_SHARP = "clean_hands_sharp"
    CLEAN_HANDS_ROUNDED = "clean_hands_rounded"
    CLEAN_HANDS_OUTLINED = "clean_hands_outlined"
    CLEANING_SERVICES = "cleaning_services"
    CLEANING_SERVICES_SHARP = "cleaning_services_sharp"
    CLEANING_SERVICES_ROUNDED = "cleaning_services_rounded"
    CLEANING_SERVICES_OUTLINED = "cleaning_services_outlined"
    CLEAR = "clear"
    CLEAR_SHARP = "clear_sharp"
    CLEAR_ROUNDED = "clear_rounded"
    CLEAR_OUTLINED = "clear_outlined"
    CLEAR_ALL = "clear_all"
    CLEAR_ALL_SHARP = "clear_all_sharp"
    CLEAR_ALL_ROUNDED = "clear_all_rounded"
    CLEAR_ALL_OUTLINED = "clear_all_outlined"
    CLOSE = "close"
    CLOSE_SHARP = "close_sharp"
    CLOSE_ROUNDED = "close_rounded"
    CLOSE_OUTLINED = "close_outlined"
    CLOSE_FULLSCREEN = "close_fullscreen"
    CLOSE_FULLSCREEN_SHARP = "close_fullscreen_sharp"
    CLOSE_FULLSCREEN_ROUNDED = "close_fullscreen_rounded"
    CLOSE_FULLSCREEN_OUTLINED = "close_fullscreen_outlined"
    CLOSED_CAPTION = "closed_caption"
    CLOSED_CAPTION_SHARP = "closed_caption_sharp"
    CLOSED_CAPTION_ROUNDED = "closed_caption_rounded"
    CLOSED_CAPTION_OUTLINED = "closed_caption_outlined"
    CLOSED_CAPTION_DISABLED = "closed_caption_disabled"
    CLOSED_CAPTION_DISABLED_SHARP = "closed_caption_disabled_sharp"
    CLOSED_CAPTION_DISABLED_ROUNDED = "closed_caption_disabled_rounded"
    CLOSED_CAPTION_DISABLED_OUTLINED = "closed_caption_disabled_outlined"
    CLOSED_CAPTION_OFF = "closed_caption_off"
    CLOSED_CAPTION_OFF_SHARP = "closed_caption_off_sharp"
    CLOSED_CAPTION_OFF_ROUNDED = "closed_caption_off_rounded"
    CLOSED_CAPTION_OFF_OUTLINED = "closed_caption_off_outlined"
    CLOUD = "cloud"
    CLOUD_SHARP = "cloud_sharp"
    CLOUD_ROUNDED = "cloud_rounded"
    CLOUD_OUTLINED = "cloud_outlined"
    CLOUD_CIRCLE = "cloud_circle"
    CLOUD_CIRCLE_SHARP = "cloud_circle_sharp"
    CLOUD_CIRCLE_ROUNDED = "cloud_circle_rounded"
    CLOUD_CIRCLE_OUTLINED = "cloud_circle_outlined"
    CLOUD_DONE = "cloud_done"
    CLOUD_DONE_SHARP = "cloud_done_sharp"
    CLOUD_DONE_ROUNDED = "cloud_done_rounded"
    CLOUD_DONE_OUTLINED = "cloud_done_outlined"
    CLOUD_DOWNLOAD = "cloud_download"
    CLOUD_DOWNLOAD_SHARP = "cloud_download_sharp"
    CLOUD_DOWNLOAD_ROUNDED = "cloud_download_rounded"
    CLOUD_DOWNLOAD_OUTLINED = "cloud_download_outlined"
    CLOUD_OFF = "cloud_off"
    CLOUD_OFF_SHARP = "cloud_off_sharp"
    CLOUD_OFF_ROUNDED = "cloud_off_rounded"
    CLOUD_OFF_OUTLINED = "cloud_off_outlined"
    CLOUD_QUEUE = "cloud_queue"
    CLOUD_QUEUE_SHARP = "cloud_queue_sharp"
    CLOUD_QUEUE_ROUNDED = "cloud_queue_rounded"
    CLOUD_QUEUE_OUTLINED = "cloud_queue_outlined"
    CLOUD_SYNC = "cloud_sync"
    CLOUD_SYNC_SHARP = "cloud_sync_sharp"
    CLOUD_SYNC_ROUNDED = "cloud_sync_rounded"
    CLOUD_SYNC_OUTLINED = "cloud_sync_outlined"
    CLOUD_UPLOAD = "cloud_upload"
    CLOUD_UPLOAD_SHARP = "cloud_upload_sharp"
    CLOUD_UPLOAD_ROUNDED = "cloud_upload_rounded"
    CLOUD_UPLOAD_OUTLINED = "cloud_upload_outlined"
    CLOUDY_SNOWING = "cloudy_snowing"
    CO2 = "co2"
    CO2_SHARP = "co2_sharp"
    CO2_ROUNDED = "co2_rounded"
    CO2_OUTLINED = "co2_outlined"
    CO_PRESENT = "co_present"
    CO_PRESENT_SHARP = "co_present_sharp"
    CO_PRESENT_ROUNDED = "co_present_rounded"
    CO_PRESENT_OUTLINED = "co_present_outlined"
    CODE = "code"
    CODE_SHARP = "code_sharp"
    CODE_ROUNDED = "code_rounded"
    CODE_OUTLINED = "code_outlined"
    CODE_OFF = "code_off"
    CODE_OFF_SHARP = "code_off_sharp"
    CODE_OFF_ROUNDED = "code_off_rounded"
    CODE_OFF_OUTLINED = "code_off_outlined"
    COFFEE = "coffee"
    COFFEE_SHARP = "coffee_sharp"
    COFFEE_ROUNDED = "coffee_rounded"
    COFFEE_OUTLINED = "coffee_outlined"
    COFFEE_MAKER = "coffee_maker"
    COFFEE_MAKER_SHARP = "coffee_maker_sharp"
    COFFEE_MAKER_ROUNDED = "coffee_maker_rounded"
    COFFEE_MAKER_OUTLINED = "coffee_maker_outlined"
    COLLECTIONS = "collections"
    COLLECTIONS_SHARP = "collections_sharp"
    COLLECTIONS_ROUNDED = "collections_rounded"
    COLLECTIONS_OUTLINED = "collections_outlined"
    COLLECTIONS_BOOKMARK = "collections_bookmark"
    COLLECTIONS_BOOKMARK_SHARP = "collections_bookmark_sharp"
    COLLECTIONS_BOOKMARK_ROUNDED = "collections_bookmark_rounded"
    COLLECTIONS_BOOKMARK_OUTLINED = "collections_bookmark_outlined"
    COLOR_LENS = "color_lens"
    COLOR_LENS_SHARP = "color_lens_sharp"
    COLOR_LENS_ROUNDED = "color_lens_rounded"
    COLOR_LENS_OUTLINED = "color_lens_outlined"
    COLORIZE = "colorize"
    COLORIZE_SHARP = "colorize_sharp"
    COLORIZE_ROUNDED = "colorize_rounded"
    COLORIZE_OUTLINED = "colorize_outlined"
    COMMENT = "comment"
    COMMENT_SHARP = "comment_sharp"
    COMMENT_ROUNDED = "comment_rounded"
    COMMENT_OUTLINED = "comment_outlined"
    COMMENT_BANK = "comment_bank"
    COMMENT_BANK_SHARP = "comment_bank_sharp"
    COMMENT_BANK_ROUNDED = "comment_bank_rounded"
    COMMENT_BANK_OUTLINED = "comment_bank_outlined"
    COMMENTS_DISABLED = "comments_disabled"
    COMMENTS_DISABLED_SHARP = "comments_disabled_sharp"
    COMMENTS_DISABLED_ROUNDED = "comments_disabled_rounded"
    COMMENTS_DISABLED_OUTLINED = "comments_disabled_outlined"
    COMMIT = "commit"
    COMMIT_SHARP = "commit_sharp"
    COMMIT_ROUNDED = "commit_rounded"
    COMMIT_OUTLINED = "commit_outlined"
    COMMUTE = "commute"
    COMMUTE_SHARP = "commute_sharp"
    COMMUTE_ROUNDED = "commute_rounded"
    COMMUTE_OUTLINED = "commute_outlined"
    COMPARE = "compare"
    COMPARE_SHARP = "compare_sharp"
    COMPARE_ROUNDED = "compare_rounded"
    COMPARE_OUTLINED = "compare_outlined"
    COMPARE_ARROWS = "compare_arrows"
    COMPARE_ARROWS_SHARP = "compare_arrows_sharp"
    COMPARE_ARROWS_ROUNDED = "compare_arrows_rounded"
    COMPARE_ARROWS_OUTLINED = "compare_arrows_outlined"
    COMPASS_CALIBRATION = "compass_calibration"
    COMPASS_CALIBRATION_SHARP = "compass_calibration_sharp"
    COMPASS_CALIBRATION_ROUNDED = "compass_calibration_rounded"
    COMPASS_CALIBRATION_OUTLINED = "compass_calibration_outlined"
    COMPOST = "compost"
    COMPOST_SHARP = "compost_sharp"
    COMPOST_ROUNDED = "compost_rounded"
    COMPOST_OUTLINED = "compost_outlined"
    COMPRESS = "compress"
    COMPRESS_SHARP = "compress_sharp"
    COMPRESS_ROUNDED = "compress_rounded"
    COMPRESS_OUTLINED = "compress_outlined"
    COMPUTER = "computer"
    COMPUTER_SHARP = "computer_sharp"
    COMPUTER_ROUNDED = "computer_rounded"
    COMPUTER_OUTLINED = "computer_outlined"
    CONFIRMATION_NUM = "confirmation_num"
    CONFIRMATION_NUM_SHARP = "confirmation_num_sharp"
    CONFIRMATION_NUM_ROUNDED = "confirmation_num_rounded"
    CONFIRMATION_NUM_OUTLINED = "confirmation_num_outlined"
    CONFIRMATION_NUMBER = "confirmation_number"
    CONFIRMATION_NUMBER_SHARP = "confirmation_number_sharp"
    CONFIRMATION_NUMBER_ROUNDED = "confirmation_number_rounded"
    CONFIRMATION_NUMBER_OUTLINED = "confirmation_number_outlined"
    CONNECT_WITHOUT_CONTACT = "connect_without_contact"
    CONNECT_WITHOUT_CONTACT_SHARP = "connect_without_contact_sharp"
    CONNECT_WITHOUT_CONTACT_ROUNDED = "connect_without_contact_rounded"
    CONNECT_WITHOUT_CONTACT_OUTLINED = "connect_without_contact_outlined"
    CONNECTED_TV = "connected_tv"
    CONNECTED_TV_SHARP = "connected_tv_sharp"
    CONNECTED_TV_ROUNDED = "connected_tv_rounded"
    CONNECTED_TV_OUTLINED = "connected_tv_outlined"
    CONNECTING_AIRPORTS = "connecting_airports"
    CONNECTING_AIRPORTS_SHARP = "connecting_airports_sharp"
    CONNECTING_AIRPORTS_ROUNDED = "connecting_airports_rounded"
    CONNECTING_AIRPORTS_OUTLINED = "connecting_airports_outlined"
    CONSTRUCTION = "construction"
    CONSTRUCTION_SHARP = "construction_sharp"
    CONSTRUCTION_ROUNDED = "construction_rounded"
    CONSTRUCTION_OUTLINED = "construction_outlined"
    CONTACT_EMERGENCY = "contact_emergency"
    CONTACT_EMERGENCY_SHARP = "contact_emergency_sharp"
    CONTACT_EMERGENCY_ROUNDED = "contact_emergency_rounded"
    CONTACT_EMERGENCY_OUTLINED = "contact_emergency_outlined"
    CONTACT_MAIL = "contact_mail"
    CONTACT_MAIL_SHARP = "contact_mail_sharp"
    CONTACT_MAIL_ROUNDED = "contact_mail_rounded"
    CONTACT_MAIL_OUTLINED = "contact_mail_outlined"
    CONTACT_PAGE = "contact_page"
    CONTACT_PAGE_SHARP = "contact_page_sharp"
    CONTACT_PAGE_ROUNDED = "contact_page_rounded"
    CONTACT_PAGE_OUTLINED = "contact_page_outlined"
    CONTACT_PHONE = "contact_phone"
    CONTACT_PHONE_SHARP = "contact_phone_sharp"
    CONTACT_PHONE_ROUNDED = "contact_phone_rounded"
    CONTACT_PHONE_OUTLINED = "contact_phone_outlined"
    CONTACT_SUPPORT = "contact_support"
    CONTACT_SUPPORT_SHARP = "contact_support_sharp"
    CONTACT_SUPPORT_ROUNDED = "contact_support_rounded"
    CONTACT_SUPPORT_OUTLINED = "contact_support_outlined"
    CONTACTLESS = "contactless"
    CONTACTLESS_SHARP = "contactless_sharp"
    CONTACTLESS_ROUNDED = "contactless_rounded"
    CONTACTLESS_OUTLINED = "contactless_outlined"
    CONTACTS = "contacts"
    CONTACTS_SHARP = "contacts_sharp"
    CONTACTS_ROUNDED = "contacts_rounded"
    CONTACTS_OUTLINED = "contacts_outlined"
    CONTENT_COPY = "content_copy"
    CONTENT_COPY_SHARP = "content_copy_sharp"
    CONTENT_COPY_ROUNDED = "content_copy_rounded"
    CONTENT_COPY_OUTLINED = "content_copy_outlined"
    CONTENT_CUT = "content_cut"
    CONTENT_CUT_SHARP = "content_cut_sharp"
    CONTENT_CUT_ROUNDED = "content_cut_rounded"
    CONTENT_CUT_OUTLINED = "content_cut_outlined"
    CONTENT_PASTE = "content_paste"
    CONTENT_PASTE_SHARP = "content_paste_sharp"
    CONTENT_PASTE_ROUNDED = "content_paste_rounded"
    CONTENT_PASTE_OUTLINED = "content_paste_outlined"
    CONTENT_PASTE_GO = "content_paste_go"
    CONTENT_PASTE_GO_SHARP = "content_paste_go_sharp"
    CONTENT_PASTE_GO_ROUNDED = "content_paste_go_rounded"
    CONTENT_PASTE_GO_OUTLINED = "content_paste_go_outlined"
    CONTENT_PASTE_OFF = "content_paste_off"
    CONTENT_PASTE_OFF_SHARP = "content_paste_off_sharp"
    CONTENT_PASTE_OFF_ROUNDED = "content_paste_off_rounded"
    CONTENT_PASTE_OFF_OUTLINED = "content_paste_off_outlined"
    CONTENT_PASTE_SEARCH = "content_paste_search"
    CONTENT_PASTE_SEARCH_SHARP = "content_paste_search_sharp"
    CONTENT_PASTE_SEARCH_ROUNDED = "content_paste_search_rounded"
    CONTENT_PASTE_SEARCH_OUTLINED = "content_paste_search_outlined"
    CONTRAST = "contrast"
    CONTRAST_SHARP = "contrast_sharp"
    CONTRAST_ROUNDED = "contrast_rounded"
    CONTRAST_OUTLINED = "contrast_outlined"
    CONTROL_CAMERA = "control_camera"
    CONTROL_CAMERA_SHARP = "control_camera_sharp"
    CONTROL_CAMERA_ROUNDED = "control_camera_rounded"
    CONTROL_CAMERA_OUTLINED = "control_camera_outlined"
    CONTROL_POINT = "control_point"
    CONTROL_POINT_SHARP = "control_point_sharp"
    CONTROL_POINT_ROUNDED = "control_point_rounded"
    CONTROL_POINT_OUTLINED = "control_point_outlined"
    CONTROL_POINT_DUPLICATE = "control_point_duplicate"
    CONTROL_POINT_DUPLICATE_SHARP = "control_point_duplicate_sharp"
    CONTROL_POINT_DUPLICATE_ROUNDED = "control_point_duplicate_rounded"
    CONTROL_POINT_DUPLICATE_OUTLINED = "control_point_duplicate_outlined"
    CONVEYOR_BELT = "conveyor_belt"
    COOKIE = "cookie"
    COOKIE_SHARP = "cookie_sharp"
    COOKIE_ROUNDED = "cookie_rounded"
    COOKIE_OUTLINED = "cookie_outlined"
    COPY = "copy"
    COPY_SHARP = "copy_sharp"
    COPY_ROUNDED = "copy_rounded"
    COPY_OUTLINED = "copy_outlined"
    COPY_ALL = "copy_all"
    COPY_ALL_SHARP = "copy_all_sharp"
    COPY_ALL_ROUNDED = "copy_all_rounded"
    COPY_ALL_OUTLINED = "copy_all_outlined"
    COPYRIGHT = "copyright"
    COPYRIGHT_SHARP = "copyright_sharp"
    COPYRIGHT_ROUNDED = "copyright_rounded"
    COPYRIGHT_OUTLINED = "copyright_outlined"
    CORONAVIRUS = "coronavirus"
    CORONAVIRUS_SHARP = "coronavirus_sharp"
    CORONAVIRUS_ROUNDED = "coronavirus_rounded"
    CORONAVIRUS_OUTLINED = "coronavirus_outlined"
    CORPORATE_FARE = "corporate_fare"
    CORPORATE_FARE_SHARP = "corporate_fare_sharp"
    CORPORATE_FARE_ROUNDED = "corporate_fare_rounded"
    CORPORATE_FARE_OUTLINED = "corporate_fare_outlined"
    COTTAGE = "cottage"
    COTTAGE_SHARP = "cottage_sharp"
    COTTAGE_ROUNDED = "cottage_rounded"
    COTTAGE_OUTLINED = "cottage_outlined"
    COUNTERTOPS = "countertops"
    COUNTERTOPS_SHARP = "countertops_sharp"
    COUNTERTOPS_ROUNDED = "countertops_rounded"
    COUNTERTOPS_OUTLINED = "countertops_outlined"
    CREATE = "create"
    CREATE_SHARP = "create_sharp"
    CREATE_ROUNDED = "create_rounded"
    CREATE_OUTLINED = "create_outlined"
    CREATE_NEW_FOLDER = "create_new_folder"
    CREATE_NEW_FOLDER_SHARP = "create_new_folder_sharp"
    CREATE_NEW_FOLDER_ROUNDED = "create_new_folder_rounded"
    CREATE_NEW_FOLDER_OUTLINED = "create_new_folder_outlined"
    CREDIT_CARD = "credit_card"
    CREDIT_CARD_SHARP = "credit_card_sharp"
    CREDIT_CARD_ROUNDED = "credit_card_rounded"
    CREDIT_CARD_OUTLINED = "credit_card_outlined"
    CREDIT_CARD_OFF = "credit_card_off"
    CREDIT_CARD_OFF_SHARP = "credit_card_off_sharp"
    CREDIT_CARD_OFF_ROUNDED = "credit_card_off_rounded"
    CREDIT_CARD_OFF_OUTLINED = "credit_card_off_outlined"
    CREDIT_SCORE = "credit_score"
    CREDIT_SCORE_SHARP = "credit_score_sharp"
    CREDIT_SCORE_ROUNDED = "credit_score_rounded"
    CREDIT_SCORE_OUTLINED = "credit_score_outlined"
    CRIB = "crib"
    CRIB_SHARP = "crib_sharp"
    CRIB_ROUNDED = "crib_rounded"
    CRIB_OUTLINED = "crib_outlined"
    CRISIS_ALERT = "crisis_alert"
    CRISIS_ALERT_SHARP = "crisis_alert_sharp"
    CRISIS_ALERT_ROUNDED = "crisis_alert_rounded"
    CRISIS_ALERT_OUTLINED = "crisis_alert_outlined"
    CROP = "crop"
    CROP_SHARP = "crop_sharp"
    CROP_ROUNDED = "crop_rounded"
    CROP_OUTLINED = "crop_outlined"
    CROP_16_9 = "crop_16_9"
    CROP_16_9_SHARP = "crop_16_9_sharp"
    CROP_16_9_ROUNDED = "crop_16_9_rounded"
    CROP_16_9_OUTLINED = "crop_16_9_outlined"
    CROP_3_2 = "crop_3_2"
    CROP_3_2_SHARP = "crop_3_2_sharp"
    CROP_3_2_ROUNDED = "crop_3_2_rounded"
    CROP_3_2_OUTLINED = "crop_3_2_outlined"
    CROP_5_4 = "crop_5_4"
    CROP_5_4_SHARP = "crop_5_4_sharp"
    CROP_5_4_ROUNDED = "crop_5_4_rounded"
    CROP_5_4_OUTLINED = "crop_5_4_outlined"
    CROP_7_5 = "crop_7_5"
    CROP_7_5_SHARP = "crop_7_5_sharp"
    CROP_7_5_ROUNDED = "crop_7_5_rounded"
    CROP_7_5_OUTLINED = "crop_7_5_outlined"
    CROP_DIN = "crop_din"
    CROP_DIN_SHARP = "crop_din_sharp"
    CROP_DIN_ROUNDED = "crop_din_rounded"
    CROP_DIN_OUTLINED = "crop_din_outlined"
    CROP_FREE = "crop_free"
    CROP_FREE_SHARP = "crop_free_sharp"
    CROP_FREE_ROUNDED = "crop_free_rounded"
    CROP_FREE_OUTLINED = "crop_free_outlined"
    CROP_LANDSCAPE = "crop_landscape"
    CROP_LANDSCAPE_SHARP = "crop_landscape_sharp"
    CROP_LANDSCAPE_ROUNDED = "crop_landscape_rounded"
    CROP_LANDSCAPE_OUTLINED = "crop_landscape_outlined"
    CROP_ORIGINAL = "crop_original"
    CROP_ORIGINAL_SHARP = "crop_original_sharp"
    CROP_ORIGINAL_ROUNDED = "crop_original_rounded"
    CROP_ORIGINAL_OUTLINED = "crop_original_outlined"
    CROP_PORTRAIT = "crop_portrait"
    CROP_PORTRAIT_SHARP = "crop_portrait_sharp"
    CROP_PORTRAIT_ROUNDED = "crop_portrait_rounded"
    CROP_PORTRAIT_OUTLINED = "crop_portrait_outlined"
    CROP_ROTATE = "crop_rotate"
    CROP_ROTATE_SHARP = "crop_rotate_sharp"
    CROP_ROTATE_ROUNDED = "crop_rotate_rounded"
    CROP_ROTATE_OUTLINED = "crop_rotate_outlined"
    CROP_SQUARE = "crop_square"
    CROP_SQUARE_SHARP = "crop_square_sharp"
    CROP_SQUARE_ROUNDED = "crop_square_rounded"
    CROP_SQUARE_OUTLINED = "crop_square_outlined"
    CRUELTY_FREE = "cruelty_free"
    CRUELTY_FREE_SHARP = "cruelty_free_sharp"
    CRUELTY_FREE_ROUNDED = "cruelty_free_rounded"
    CRUELTY_FREE_OUTLINED = "cruelty_free_outlined"
    CSS = "css"
    CSS_SHARP = "css_sharp"
    CSS_ROUNDED = "css_rounded"
    CSS_OUTLINED = "css_outlined"
    CURRENCY_BITCOIN = "currency_bitcoin"
    CURRENCY_BITCOIN_SHARP = "currency_bitcoin_sharp"
    CURRENCY_BITCOIN_ROUNDED = "currency_bitcoin_rounded"
    CURRENCY_BITCOIN_OUTLINED = "currency_bitcoin_outlined"
    CURRENCY_EXCHANGE = "currency_exchange"
    CURRENCY_EXCHANGE_SHARP = "currency_exchange_sharp"
    CURRENCY_EXCHANGE_ROUNDED = "currency_exchange_rounded"
    CURRENCY_EXCHANGE_OUTLINED = "currency_exchange_outlined"
    CURRENCY_FRANC = "currency_franc"
    CURRENCY_FRANC_SHARP = "currency_franc_sharp"
    CURRENCY_FRANC_ROUNDED = "currency_franc_rounded"
    CURRENCY_FRANC_OUTLINED = "currency_franc_outlined"
    CURRENCY_LIRA = "currency_lira"
    CURRENCY_LIRA_SHARP = "currency_lira_sharp"
    CURRENCY_LIRA_ROUNDED = "currency_lira_rounded"
    CURRENCY_LIRA_OUTLINED = "currency_lira_outlined"
    CURRENCY_POUND = "currency_pound"
    CURRENCY_POUND_SHARP = "currency_pound_sharp"
    CURRENCY_POUND_ROUNDED = "currency_pound_rounded"
    CURRENCY_POUND_OUTLINED = "currency_pound_outlined"
    CURRENCY_RUBLE = "currency_ruble"
    CURRENCY_RUBLE_SHARP = "currency_ruble_sharp"
    CURRENCY_RUBLE_ROUNDED = "currency_ruble_rounded"
    CURRENCY_RUBLE_OUTLINED = "currency_ruble_outlined"
    CURRENCY_RUPEE = "currency_rupee"
    CURRENCY_RUPEE_SHARP = "currency_rupee_sharp"
    CURRENCY_RUPEE_ROUNDED = "currency_rupee_rounded"
    CURRENCY_RUPEE_OUTLINED = "currency_rupee_outlined"
    CURRENCY_YEN = "currency_yen"
    CURRENCY_YEN_SHARP = "currency_yen_sharp"
    CURRENCY_YEN_ROUNDED = "currency_yen_rounded"
    CURRENCY_YEN_OUTLINED = "currency_yen_outlined"
    CURRENCY_YUAN = "currency_yuan"
    CURRENCY_YUAN_SHARP = "currency_yuan_sharp"
    CURRENCY_YUAN_ROUNDED = "currency_yuan_rounded"
    CURRENCY_YUAN_OUTLINED = "currency_yuan_outlined"
    CURTAINS = "curtains"
    CURTAINS_SHARP = "curtains_sharp"
    CURTAINS_ROUNDED = "curtains_rounded"
    CURTAINS_OUTLINED = "curtains_outlined"
    CURTAINS_CLOSED = "curtains_closed"
    CURTAINS_CLOSED_SHARP = "curtains_closed_sharp"
    CURTAINS_CLOSED_ROUNDED = "curtains_closed_rounded"
    CURTAINS_CLOSED_OUTLINED = "curtains_closed_outlined"
    CUT = "cut"
    CUT_SHARP = "cut_sharp"
    CUT_ROUNDED = "cut_rounded"
    CUT_OUTLINED = "cut_outlined"
    CYCLONE = "cyclone"
    CYCLONE_SHARP = "cyclone_sharp"
    CYCLONE_ROUNDED = "cyclone_rounded"
    CYCLONE_OUTLINED = "cyclone_outlined"
    DANGEROUS = "dangerous"
    DANGEROUS_SHARP = "dangerous_sharp"
    DANGEROUS_ROUNDED = "dangerous_rounded"
    DANGEROUS_OUTLINED = "dangerous_outlined"
    DARK_MODE = "dark_mode"
    DARK_MODE_SHARP = "dark_mode_sharp"
    DARK_MODE_ROUNDED = "dark_mode_rounded"
    DARK_MODE_OUTLINED = "dark_mode_outlined"
    DASHBOARD = "dashboard"
    DASHBOARD_SHARP = "dashboard_sharp"
    DASHBOARD_ROUNDED = "dashboard_rounded"
    DASHBOARD_OUTLINED = "dashboard_outlined"
    DASHBOARD_CUSTOMIZE = "dashboard_customize"
    DASHBOARD_CUSTOMIZE_SHARP = "dashboard_customize_sharp"
    DASHBOARD_CUSTOMIZE_ROUNDED = "dashboard_customize_rounded"
    DASHBOARD_CUSTOMIZE_OUTLINED = "dashboard_customize_outlined"
    DATA_ARRAY = "data_array"
    DATA_ARRAY_SHARP = "data_array_sharp"
    DATA_ARRAY_ROUNDED = "data_array_rounded"
    DATA_ARRAY_OUTLINED = "data_array_outlined"
    DATA_EXPLORATION = "data_exploration"
    DATA_EXPLORATION_SHARP = "data_exploration_sharp"
    DATA_EXPLORATION_ROUNDED = "data_exploration_rounded"
    DATA_EXPLORATION_OUTLINED = "data_exploration_outlined"
    DATA_OBJECT = "data_object"
    DATA_OBJECT_SHARP = "data_object_sharp"
    DATA_OBJECT_ROUNDED = "data_object_rounded"
    DATA_OBJECT_OUTLINED = "data_object_outlined"
    DATA_SAVER_OFF = "data_saver_off"
    DATA_SAVER_OFF_SHARP = "data_saver_off_sharp"
    DATA_SAVER_OFF_ROUNDED = "data_saver_off_rounded"
    DATA_SAVER_OFF_OUTLINED = "data_saver_off_outlined"
    DATA_SAVER_ON = "data_saver_on"
    DATA_SAVER_ON_SHARP = "data_saver_on_sharp"
    DATA_SAVER_ON_ROUNDED = "data_saver_on_rounded"
    DATA_SAVER_ON_OUTLINED = "data_saver_on_outlined"
    DATA_THRESHOLDING = "data_thresholding"
    DATA_THRESHOLDING_SHARP = "data_thresholding_sharp"
    DATA_THRESHOLDING_ROUNDED = "data_thresholding_rounded"
    DATA_THRESHOLDING_OUTLINED = "data_thresholding_outlined"
    DATA_USAGE = "data_usage"
    DATA_USAGE_SHARP = "data_usage_sharp"
    DATA_USAGE_ROUNDED = "data_usage_rounded"
    DATA_USAGE_OUTLINED = "data_usage_outlined"
    DATASET = "dataset"
    DATASET_SHARP = "dataset_sharp"
    DATASET_ROUNDED = "dataset_rounded"
    DATASET_OUTLINED = "dataset_outlined"
    DATASET_LINKED = "dataset_linked"
    DATASET_LINKED_SHARP = "dataset_linked_sharp"
    DATASET_LINKED_ROUNDED = "dataset_linked_rounded"
    DATASET_LINKED_OUTLINED = "dataset_linked_outlined"
    DATE_RANGE = "date_range"
    DATE_RANGE_SHARP = "date_range_sharp"
    DATE_RANGE_ROUNDED = "date_range_rounded"
    DATE_RANGE_OUTLINED = "date_range_outlined"
    DEBLUR = "deblur"
    DEBLUR_SHARP = "deblur_sharp"
    DEBLUR_ROUNDED = "deblur_rounded"
    DEBLUR_OUTLINED = "deblur_outlined"
    DECK = "deck"
    DECK_SHARP = "deck_sharp"
    DECK_ROUNDED = "deck_rounded"
    DECK_OUTLINED = "deck_outlined"
    DEHAZE = "dehaze"
    DEHAZE_SHARP = "dehaze_sharp"
    DEHAZE_ROUNDED = "dehaze_rounded"
    DEHAZE_OUTLINED = "dehaze_outlined"
    DELETE = "delete"
    DELETE_SHARP = "delete_sharp"
    DELETE_ROUNDED = "delete_rounded"
    DELETE_OUTLINED = "delete_outlined"
    DELETE_FOREVER = "delete_forever"
    DELETE_FOREVER_SHARP = "delete_forever_sharp"
    DELETE_FOREVER_ROUNDED = "delete_forever_rounded"
    DELETE_FOREVER_OUTLINED = "delete_forever_outlined"
    DELETE_OUTLINE = "delete_outline"
    DELETE_OUTLINE_SHARP = "delete_outline_sharp"
    DELETE_OUTLINE_ROUNDED = "delete_outline_rounded"
    DELETE_OUTLINE_OUTLINED = "delete_outline_outlined"
    DELETE_SWEEP = "delete_sweep"
    DELETE_SWEEP_SHARP = "delete_sweep_sharp"
    DELETE_SWEEP_ROUNDED = "delete_sweep_rounded"
    DELETE_SWEEP_OUTLINED = "delete_sweep_outlined"
    DELIVERY_DINING = "delivery_dining"
    DELIVERY_DINING_SHARP = "delivery_dining_sharp"
    DELIVERY_DINING_ROUNDED = "delivery_dining_rounded"
    DELIVERY_DINING_OUTLINED = "delivery_dining_outlined"
    DENSITY_LARGE = "density_large"
    DENSITY_LARGE_SHARP = "density_large_sharp"
    DENSITY_LARGE_ROUNDED = "density_large_rounded"
    DENSITY_LARGE_OUTLINED = "density_large_outlined"
    DENSITY_MEDIUM = "density_medium"
    DENSITY_MEDIUM_SHARP = "density_medium_sharp"
    DENSITY_MEDIUM_ROUNDED = "density_medium_rounded"
    DENSITY_MEDIUM_OUTLINED = "density_medium_outlined"
    DENSITY_SMALL = "density_small"
    DENSITY_SMALL_SHARP = "density_small_sharp"
    DENSITY_SMALL_ROUNDED = "density_small_rounded"
    DENSITY_SMALL_OUTLINED = "density_small_outlined"
    DEPARTURE_BOARD = "departure_board"
    DEPARTURE_BOARD_SHARP = "departure_board_sharp"
    DEPARTURE_BOARD_ROUNDED = "departure_board_rounded"
    DEPARTURE_BOARD_OUTLINED = "departure_board_outlined"
    DESCRIPTION = "description"
    DESCRIPTION_SHARP = "description_sharp"
    DESCRIPTION_ROUNDED = "description_rounded"
    DESCRIPTION_OUTLINED = "description_outlined"
    DESELECT = "deselect"
    DESELECT_SHARP = "deselect_sharp"
    DESELECT_ROUNDED = "deselect_rounded"
    DESELECT_OUTLINED = "deselect_outlined"
    DESIGN_SERVICES = "design_services"
    DESIGN_SERVICES_SHARP = "design_services_sharp"
    DESIGN_SERVICES_ROUNDED = "design_services_rounded"
    DESIGN_SERVICES_OUTLINED = "design_services_outlined"
    DESK = "desk"
    DESK_SHARP = "desk_sharp"
    DESK_ROUNDED = "desk_rounded"
    DESK_OUTLINED = "desk_outlined"
    DESKTOP_ACCESS_DISABLED = "desktop_access_disabled"
    DESKTOP_ACCESS_DISABLED_SHARP = "desktop_access_disabled_sharp"
    DESKTOP_ACCESS_DISABLED_ROUNDED = "desktop_access_disabled_rounded"
    DESKTOP_ACCESS_DISABLED_OUTLINED = "desktop_access_disabled_outlined"
    DESKTOP_MAC = "desktop_mac"
    DESKTOP_MAC_SHARP = "desktop_mac_sharp"
    DESKTOP_MAC_ROUNDED = "desktop_mac_rounded"
    DESKTOP_MAC_OUTLINED = "desktop_mac_outlined"
    DESKTOP_WINDOWS = "desktop_windows"
    DESKTOP_WINDOWS_SHARP = "desktop_windows_sharp"
    DESKTOP_WINDOWS_ROUNDED = "desktop_windows_rounded"
    DESKTOP_WINDOWS_OUTLINED = "desktop_windows_outlined"
    DETAILS = "details"
    DETAILS_SHARP = "details_sharp"
    DETAILS_ROUNDED = "details_rounded"
    DETAILS_OUTLINED = "details_outlined"
    DEVELOPER_BOARD = "developer_board"
    DEVELOPER_BOARD_SHARP = "developer_board_sharp"
    DEVELOPER_BOARD_ROUNDED = "developer_board_rounded"
    DEVELOPER_BOARD_OUTLINED = "developer_board_outlined"
    DEVELOPER_BOARD_OFF = "developer_board_off"
    DEVELOPER_BOARD_OFF_SHARP = "developer_board_off_sharp"
    DEVELOPER_BOARD_OFF_ROUNDED = "developer_board_off_rounded"
    DEVELOPER_BOARD_OFF_OUTLINED = "developer_board_off_outlined"
    DEVELOPER_MODE = "developer_mode"
    DEVELOPER_MODE_SHARP = "developer_mode_sharp"
    DEVELOPER_MODE_ROUNDED = "developer_mode_rounded"
    DEVELOPER_MODE_OUTLINED = "developer_mode_outlined"
    DEVICE_HUB = "device_hub"
    DEVICE_HUB_SHARP = "device_hub_sharp"
    DEVICE_HUB_ROUNDED = "device_hub_rounded"
    DEVICE_HUB_OUTLINED = "device_hub_outlined"
    DEVICE_THERMOSTAT = "device_thermostat"
    DEVICE_THERMOSTAT_SHARP = "device_thermostat_sharp"
    DEVICE_THERMOSTAT_ROUNDED = "device_thermostat_rounded"
    DEVICE_THERMOSTAT_OUTLINED = "device_thermostat_outlined"
    DEVICE_UNKNOWN = "device_unknown"
    DEVICE_UNKNOWN_SHARP = "device_unknown_sharp"
    DEVICE_UNKNOWN_ROUNDED = "device_unknown_rounded"
    DEVICE_UNKNOWN_OUTLINED = "device_unknown_outlined"
    DEVICES = "devices"
    DEVICES_SHARP = "devices_sharp"
    DEVICES_ROUNDED = "devices_rounded"
    DEVICES_OUTLINED = "devices_outlined"
    DEVICES_FOLD = "devices_fold"
    DEVICES_FOLD_SHARP = "devices_fold_sharp"
    DEVICES_FOLD_ROUNDED = "devices_fold_rounded"
    DEVICES_FOLD_OUTLINED = "devices_fold_outlined"
    DEVICES_OTHER = "devices_other"
    DEVICES_OTHER_SHARP = "devices_other_sharp"
    DEVICES_OTHER_ROUNDED = "devices_other_rounded"
    DEVICES_OTHER_OUTLINED = "devices_other_outlined"
    DEW_POINT = "dew_point"
    DIALER_SIP = "dialer_sip"
    DIALER_SIP_SHARP = "dialer_sip_sharp"
    DIALER_SIP_ROUNDED = "dialer_sip_rounded"
    DIALER_SIP_OUTLINED = "dialer_sip_outlined"
    DIALPAD = "dialpad"
    DIALPAD_SHARP = "dialpad_sharp"
    DIALPAD_ROUNDED = "dialpad_rounded"
    DIALPAD_OUTLINED = "dialpad_outlined"
    DIAMOND = "diamond"
    DIAMOND_SHARP = "diamond_sharp"
    DIAMOND_ROUNDED = "diamond_rounded"
    DIAMOND_OUTLINED = "diamond_outlined"
    DIFFERENCE = "difference"
    DIFFERENCE_SHARP = "difference_sharp"
    DIFFERENCE_ROUNDED = "difference_rounded"
    DIFFERENCE_OUTLINED = "difference_outlined"
    DINING = "dining"
    DINING_SHARP = "dining_sharp"
    DINING_ROUNDED = "dining_rounded"
    DINING_OUTLINED = "dining_outlined"
    DINNER_DINING = "dinner_dining"
    DINNER_DINING_SHARP = "dinner_dining_sharp"
    DINNER_DINING_ROUNDED = "dinner_dining_rounded"
    DINNER_DINING_OUTLINED = "dinner_dining_outlined"
    DIRECTIONS = "directions"
    DIRECTIONS_SHARP = "directions_sharp"
    DIRECTIONS_ROUNDED = "directions_rounded"
    DIRECTIONS_OUTLINED = "directions_outlined"
    DIRECTIONS_BIKE = "directions_bike"
    DIRECTIONS_BIKE_SHARP = "directions_bike_sharp"
    DIRECTIONS_BIKE_ROUNDED = "directions_bike_rounded"
    DIRECTIONS_BIKE_OUTLINED = "directions_bike_outlined"
    DIRECTIONS_BOAT = "directions_boat"
    DIRECTIONS_BOAT_SHARP = "directions_boat_sharp"
    DIRECTIONS_BOAT_ROUNDED = "directions_boat_rounded"
    DIRECTIONS_BOAT_OUTLINED = "directions_boat_outlined"
    DIRECTIONS_BOAT_FILLED = "directions_boat_filled"
    DIRECTIONS_BOAT_FILLED_SHARP = "directions_boat_filled_sharp"
    DIRECTIONS_BOAT_FILLED_ROUNDED = "directions_boat_filled_rounded"
    DIRECTIONS_BOAT_FILLED_OUTLINED = "directions_boat_filled_outlined"
    DIRECTIONS_BUS = "directions_bus"
    DIRECTIONS_BUS_SHARP = "directions_bus_sharp"
    DIRECTIONS_BUS_ROUNDED = "directions_bus_rounded"
    DIRECTIONS_BUS_OUTLINED = "directions_bus_outlined"
    DIRECTIONS_BUS_FILLED = "directions_bus_filled"
    DIRECTIONS_BUS_FILLED_SHARP = "directions_bus_filled_sharp"
    DIRECTIONS_BUS_FILLED_ROUNDED = "directions_bus_filled_rounded"
    DIRECTIONS_BUS_FILLED_OUTLINED = "directions_bus_filled_outlined"
    DIRECTIONS_CAR = "directions_car"
    DIRECTIONS_CAR_SHARP = "directions_car_sharp"
    DIRECTIONS_CAR_ROUNDED = "directions_car_rounded"
    DIRECTIONS_CAR_OUTLINED = "directions_car_outlined"
    DIRECTIONS_CAR_FILLED = "directions_car_filled"
    DIRECTIONS_CAR_FILLED_SHARP = "directions_car_filled_sharp"
    DIRECTIONS_CAR_FILLED_ROUNDED = "directions_car_filled_rounded"
    DIRECTIONS_CAR_FILLED_OUTLINED = "directions_car_filled_outlined"
    DIRECTIONS_FERRY = "directions_ferry"
    DIRECTIONS_FERRY_SHARP = "directions_ferry_sharp"
    DIRECTIONS_FERRY_ROUNDED = "directions_ferry_rounded"
    DIRECTIONS_FERRY_OUTLINED = "directions_ferry_outlined"
    DIRECTIONS_OFF = "directions_off"
    DIRECTIONS_OFF_SHARP = "directions_off_sharp"
    DIRECTIONS_OFF_ROUNDED = "directions_off_rounded"
    DIRECTIONS_OFF_OUTLINED = "directions_off_outlined"
    DIRECTIONS_RAILWAY = "directions_railway"
    DIRECTIONS_RAILWAY_SHARP = "directions_railway_sharp"
    DIRECTIONS_RAILWAY_ROUNDED = "directions_railway_rounded"
    DIRECTIONS_RAILWAY_OUTLINED = "directions_railway_outlined"
    DIRECTIONS_RAILWAY_FILLED = "directions_railway_filled"
    DIRECTIONS_RAILWAY_FILLED_SHARP = "directions_railway_filled_sharp"
    DIRECTIONS_RAILWAY_FILLED_ROUNDED = "directions_railway_filled_rounded"
    DIRECTIONS_RAILWAY_FILLED_OUTLINED = "directions_railway_filled_outlined"
    DIRECTIONS_RUN = "directions_run"
    DIRECTIONS_RUN_SHARP = "directions_run_sharp"
    DIRECTIONS_RUN_ROUNDED = "directions_run_rounded"
    DIRECTIONS_RUN_OUTLINED = "directions_run_outlined"
    DIRECTIONS_SUBWAY = "directions_subway"
    DIRECTIONS_SUBWAY_SHARP = "directions_subway_sharp"
    DIRECTIONS_SUBWAY_ROUNDED = "directions_subway_rounded"
    DIRECTIONS_SUBWAY_OUTLINED = "directions_subway_outlined"
    DIRECTIONS_SUBWAY_FILLED = "directions_subway_filled"
    DIRECTIONS_SUBWAY_FILLED_SHARP = "directions_subway_filled_sharp"
    DIRECTIONS_SUBWAY_FILLED_ROUNDED = "directions_subway_filled_rounded"
    DIRECTIONS_SUBWAY_FILLED_OUTLINED = "directions_subway_filled_outlined"
    DIRECTIONS_TRAIN = "directions_train"
    DIRECTIONS_TRAIN_SHARP = "directions_train_sharp"
    DIRECTIONS_TRAIN_ROUNDED = "directions_train_rounded"
    DIRECTIONS_TRAIN_OUTLINED = "directions_train_outlined"
    DIRECTIONS_TRANSIT = "directions_transit"
    DIRECTIONS_TRANSIT_SHARP = "directions_transit_sharp"
    DIRECTIONS_TRANSIT_ROUNDED = "directions_transit_rounded"
    DIRECTIONS_TRANSIT_OUTLINED = "directions_transit_outlined"
    DIRECTIONS_TRANSIT_FILLED = "directions_transit_filled"
    DIRECTIONS_TRANSIT_FILLED_SHARP = "directions_transit_filled_sharp"
    DIRECTIONS_TRANSIT_FILLED_ROUNDED = "directions_transit_filled_rounded"
    DIRECTIONS_TRANSIT_FILLED_OUTLINED = "directions_transit_filled_outlined"
    DIRECTIONS_WALK = "directions_walk"
    DIRECTIONS_WALK_SHARP = "directions_walk_sharp"
    DIRECTIONS_WALK_ROUNDED = "directions_walk_rounded"
    DIRECTIONS_WALK_OUTLINED = "directions_walk_outlined"
    DIRTY_LENS = "dirty_lens"
    DIRTY_LENS_SHARP = "dirty_lens_sharp"
    DIRTY_LENS_ROUNDED = "dirty_lens_rounded"
    DIRTY_LENS_OUTLINED = "dirty_lens_outlined"
    DISABLED_BY_DEFAULT = "disabled_by_default"
    DISABLED_BY_DEFAULT_SHARP = "disabled_by_default_sharp"
    DISABLED_BY_DEFAULT_ROUNDED = "disabled_by_default_rounded"
    DISABLED_BY_DEFAULT_OUTLINED = "disabled_by_default_outlined"
    DISABLED_VISIBLE = "disabled_visible"
    DISABLED_VISIBLE_SHARP = "disabled_visible_sharp"
    DISABLED_VISIBLE_ROUNDED = "disabled_visible_rounded"
    DISABLED_VISIBLE_OUTLINED = "disabled_visible_outlined"
    DISC_FULL = "disc_full"
    DISC_FULL_SHARP = "disc_full_sharp"
    DISC_FULL_ROUNDED = "disc_full_rounded"
    DISC_FULL_OUTLINED = "disc_full_outlined"
    DISCORD = "discord"
    DISCORD_SHARP = "discord_sharp"
    DISCORD_ROUNDED = "discord_rounded"
    DISCORD_OUTLINED = "discord_outlined"
    DISCOUNT = "discount"
    DISCOUNT_SHARP = "discount_sharp"
    DISCOUNT_ROUNDED = "discount_rounded"
    DISCOUNT_OUTLINED = "discount_outlined"
    DISPLAY_SETTINGS = "display_settings"
    DISPLAY_SETTINGS_SHARP = "display_settings_sharp"
    DISPLAY_SETTINGS_ROUNDED = "display_settings_rounded"
    DISPLAY_SETTINGS_OUTLINED = "display_settings_outlined"
    DIVERSITY_1 = "diversity_1"
    DIVERSITY_1_SHARP = "diversity_1_sharp"
    DIVERSITY_1_ROUNDED = "diversity_1_rounded"
    DIVERSITY_1_OUTLINED = "diversity_1_outlined"
    DIVERSITY_2 = "diversity_2"
    DIVERSITY_2_SHARP = "diversity_2_sharp"
    DIVERSITY_2_ROUNDED = "diversity_2_rounded"
    DIVERSITY_2_OUTLINED = "diversity_2_outlined"
    DIVERSITY_3 = "diversity_3"
    DIVERSITY_3_SHARP = "diversity_3_sharp"
    DIVERSITY_3_ROUNDED = "diversity_3_rounded"
    DIVERSITY_3_OUTLINED = "diversity_3_outlined"
    DND_FORWARDSLASH = "dnd_forwardslash"
    DND_FORWARDSLASH_SHARP = "dnd_forwardslash_sharp"
    DND_FORWARDSLASH_ROUNDED = "dnd_forwardslash_rounded"
    DND_FORWARDSLASH_OUTLINED = "dnd_forwardslash_outlined"
    DNS = "dns"
    DNS_SHARP = "dns_sharp"
    DNS_ROUNDED = "dns_rounded"
    DNS_OUTLINED = "dns_outlined"
    DO_DISTURB = "do_disturb"
    DO_DISTURB_SHARP = "do_disturb_sharp"
    DO_DISTURB_ROUNDED = "do_disturb_rounded"
    DO_DISTURB_OUTLINED = "do_disturb_outlined"
    DO_DISTURB_ALT = "do_disturb_alt"
    DO_DISTURB_ALT_SHARP = "do_disturb_alt_sharp"
    DO_DISTURB_ALT_ROUNDED = "do_disturb_alt_rounded"
    DO_DISTURB_ALT_OUTLINED = "do_disturb_alt_outlined"
    DO_DISTURB_OFF = "do_disturb_off"
    DO_DISTURB_OFF_SHARP = "do_disturb_off_sharp"
    DO_DISTURB_OFF_ROUNDED = "do_disturb_off_rounded"
    DO_DISTURB_OFF_OUTLINED = "do_disturb_off_outlined"
    DO_DISTURB_ON = "do_disturb_on"
    DO_DISTURB_ON_SHARP = "do_disturb_on_sharp"
    DO_DISTURB_ON_ROUNDED = "do_disturb_on_rounded"
    DO_DISTURB_ON_OUTLINED = "do_disturb_on_outlined"
    DO_NOT_DISTURB = "do_not_disturb"
    DO_NOT_DISTURB_SHARP = "do_not_disturb_sharp"
    DO_NOT_DISTURB_ROUNDED = "do_not_disturb_rounded"
    DO_NOT_DISTURB_OUTLINED = "do_not_disturb_outlined"
    DO_NOT_DISTURB_ALT = "do_not_disturb_alt"
    DO_NOT_DISTURB_ALT_SHARP = "do_not_disturb_alt_sharp"
    DO_NOT_DISTURB_ALT_ROUNDED = "do_not_disturb_alt_rounded"
    DO_NOT_DISTURB_ALT_OUTLINED = "do_not_disturb_alt_outlined"
    DO_NOT_DISTURB_OFF = "do_not_disturb_off"
    DO_NOT_DISTURB_OFF_SHARP = "do_not_disturb_off_sharp"
    DO_NOT_DISTURB_OFF_ROUNDED = "do_not_disturb_off_rounded"
    DO_NOT_DISTURB_OFF_OUTLINED = "do_not_disturb_off_outlined"
    DO_NOT_DISTURB_ON = "do_not_disturb_on"
    DO_NOT_DISTURB_ON_SHARP = "do_not_disturb_on_sharp"
    DO_NOT_DISTURB_ON_ROUNDED = "do_not_disturb_on_rounded"
    DO_NOT_DISTURB_ON_OUTLINED = "do_not_disturb_on_outlined"
    DO_NOT_DISTURB_ON_TOTAL_SILENCE = "do_not_disturb_on_total_silence"
    DO_NOT_DISTURB_ON_TOTAL_SILENCE_SHARP = "do_not_disturb_on_total_silence_sharp"
    DO_NOT_DISTURB_ON_TOTAL_SILENCE_ROUNDED = "do_not_disturb_on_total_silence_rounded"
    DO_NOT_DISTURB_ON_TOTAL_SILENCE_OUTLINED = (
        "do_not_disturb_on_total_silence_outlined"
    )
    DO_NOT_STEP = "do_not_step"
    DO_NOT_STEP_SHARP = "do_not_step_sharp"
    DO_NOT_STEP_ROUNDED = "do_not_step_rounded"
    DO_NOT_STEP_OUTLINED = "do_not_step_outlined"
    DO_NOT_TOUCH = "do_not_touch"
    DO_NOT_TOUCH_SHARP = "do_not_touch_sharp"
    DO_NOT_TOUCH_ROUNDED = "do_not_touch_rounded"
    DO_NOT_TOUCH_OUTLINED = "do_not_touch_outlined"
    DOCK = "dock"
    DOCK_SHARP = "dock_sharp"
    DOCK_ROUNDED = "dock_rounded"
    DOCK_OUTLINED = "dock_outlined"
    DOCUMENT_SCANNER = "document_scanner"
    DOCUMENT_SCANNER_SHARP = "document_scanner_sharp"
    DOCUMENT_SCANNER_ROUNDED = "document_scanner_rounded"
    DOCUMENT_SCANNER_OUTLINED = "document_scanner_outlined"
    DOMAIN = "domain"
    DOMAIN_SHARP = "domain_sharp"
    DOMAIN_ROUNDED = "domain_rounded"
    DOMAIN_OUTLINED = "domain_outlined"
    DOMAIN_ADD = "domain_add"
    DOMAIN_ADD_SHARP = "domain_add_sharp"
    DOMAIN_ADD_ROUNDED = "domain_add_rounded"
    DOMAIN_ADD_OUTLINED = "domain_add_outlined"
    DOMAIN_DISABLED = "domain_disabled"
    DOMAIN_DISABLED_SHARP = "domain_disabled_sharp"
    DOMAIN_DISABLED_ROUNDED = "domain_disabled_rounded"
    DOMAIN_DISABLED_OUTLINED = "domain_disabled_outlined"
    DOMAIN_VERIFICATION = "domain_verification"
    DOMAIN_VERIFICATION_SHARP = "domain_verification_sharp"
    DOMAIN_VERIFICATION_ROUNDED = "domain_verification_rounded"
    DOMAIN_VERIFICATION_OUTLINED = "domain_verification_outlined"
    DONE = "done"
    DONE_SHARP = "done_sharp"
    DONE_ROUNDED = "done_rounded"
    DONE_OUTLINED = "done_outlined"
    DONE_ALL = "done_all"
    DONE_ALL_SHARP = "done_all_sharp"
    DONE_ALL_ROUNDED = "done_all_rounded"
    DONE_ALL_OUTLINED = "done_all_outlined"
    DONE_OUTLINE = "done_outline"
    DONE_OUTLINE_SHARP = "done_outline_sharp"
    DONE_OUTLINE_ROUNDED = "done_outline_rounded"
    DONE_OUTLINE_OUTLINED = "done_outline_outlined"
    DONUT_LARGE = "donut_large"
    DONUT_LARGE_SHARP = "donut_large_sharp"
    DONUT_LARGE_ROUNDED = "donut_large_rounded"
    DONUT_LARGE_OUTLINED = "donut_large_outlined"
    DONUT_SMALL = "donut_small"
    DONUT_SMALL_SHARP = "donut_small_sharp"
    DONUT_SMALL_ROUNDED = "donut_small_rounded"
    DONUT_SMALL_OUTLINED = "donut_small_outlined"
    DOOR_BACK_DOOR = "door_back_door"
    DOOR_BACK_DOOR_SHARP = "door_back_door_sharp"
    DOOR_BACK_DOOR_ROUNDED = "door_back_door_rounded"
    DOOR_BACK_DOOR_OUTLINED = "door_back_door_outlined"
    DOOR_FRONT_DOOR = "door_front_door"
    DOOR_FRONT_DOOR_SHARP = "door_front_door_sharp"
    DOOR_FRONT_DOOR_ROUNDED = "door_front_door_rounded"
    DOOR_FRONT_DOOR_OUTLINED = "door_front_door_outlined"
    DOOR_SLIDING = "door_sliding"
    DOOR_SLIDING_SHARP = "door_sliding_sharp"
    DOOR_SLIDING_ROUNDED = "door_sliding_rounded"
    DOOR_SLIDING_OUTLINED = "door_sliding_outlined"
    DOORBELL = "doorbell"
    DOORBELL_SHARP = "doorbell_sharp"
    DOORBELL_ROUNDED = "doorbell_rounded"
    DOORBELL_OUTLINED = "doorbell_outlined"
    DOUBLE_ARROW = "double_arrow"
    DOUBLE_ARROW_SHARP = "double_arrow_sharp"
    DOUBLE_ARROW_ROUNDED = "double_arrow_rounded"
    DOUBLE_ARROW_OUTLINED = "double_arrow_outlined"
    DOWNHILL_SKIING = "downhill_skiing"
    DOWNHILL_SKIING_SHARP = "downhill_skiing_sharp"
    DOWNHILL_SKIING_ROUNDED = "downhill_skiing_rounded"
    DOWNHILL_SKIING_OUTLINED = "downhill_skiing_outlined"
    DOWNLOAD = "download"
    DOWNLOAD_SHARP = "download_sharp"
    DOWNLOAD_ROUNDED = "download_rounded"
    DOWNLOAD_OUTLINED = "download_outlined"
    DOWNLOAD_DONE = "download_done"
    DOWNLOAD_DONE_SHARP = "download_done_sharp"
    DOWNLOAD_DONE_ROUNDED = "download_done_rounded"
    DOWNLOAD_DONE_OUTLINED = "download_done_outlined"
    DOWNLOAD_FOR_OFFLINE = "download_for_offline"
    DOWNLOAD_FOR_OFFLINE_SHARP = "download_for_offline_sharp"
    DOWNLOAD_FOR_OFFLINE_ROUNDED = "download_for_offline_rounded"
    DOWNLOAD_FOR_OFFLINE_OUTLINED = "download_for_offline_outlined"
    DOWNLOADING = "downloading"
    DOWNLOADING_SHARP = "downloading_sharp"
    DOWNLOADING_ROUNDED = "downloading_rounded"
    DOWNLOADING_OUTLINED = "downloading_outlined"
    DRAFTS = "drafts"
    DRAFTS_SHARP = "drafts_sharp"
    DRAFTS_ROUNDED = "drafts_rounded"
    DRAFTS_OUTLINED = "drafts_outlined"
    DRAG_HANDLE = "drag_handle"
    DRAG_HANDLE_SHARP = "drag_handle_sharp"
    DRAG_HANDLE_ROUNDED = "drag_handle_rounded"
    DRAG_HANDLE_OUTLINED = "drag_handle_outlined"
    DRAG_INDICATOR = "drag_indicator"
    DRAG_INDICATOR_SHARP = "drag_indicator_sharp"
    DRAG_INDICATOR_ROUNDED = "drag_indicator_rounded"
    DRAG_INDICATOR_OUTLINED = "drag_indicator_outlined"
    DRAW = "draw"
    DRAW_SHARP = "draw_sharp"
    DRAW_ROUNDED = "draw_rounded"
    DRAW_OUTLINED = "draw_outlined"
    DRIVE_ETA = "drive_eta"
    DRIVE_ETA_SHARP = "drive_eta_sharp"
    DRIVE_ETA_ROUNDED = "drive_eta_rounded"
    DRIVE_ETA_OUTLINED = "drive_eta_outlined"
    DRIVE_FILE_MOVE = "drive_file_move"
    DRIVE_FILE_MOVE_SHARP = "drive_file_move_sharp"
    DRIVE_FILE_MOVE_ROUNDED = "drive_file_move_rounded"
    DRIVE_FILE_MOVE_OUTLINED = "drive_file_move_outlined"
    DRIVE_FILE_MOVE_OUTLINE = "drive_file_move_outline"
    DRIVE_FILE_MOVE_RTL = "drive_file_move_rtl"
    DRIVE_FILE_MOVE_RTL_SHARP = "drive_file_move_rtl_sharp"
    DRIVE_FILE_MOVE_RTL_ROUNDED = "drive_file_move_rtl_rounded"
    DRIVE_FILE_MOVE_RTL_OUTLINED = "drive_file_move_rtl_outlined"
    DRIVE_FILE_RENAME_OUTLINE = "drive_file_rename_outline"
    DRIVE_FILE_RENAME_OUTLINE_SHARP = "drive_file_rename_outline_sharp"
    DRIVE_FILE_RENAME_OUTLINE_ROUNDED = "drive_file_rename_outline_rounded"
    DRIVE_FILE_RENAME_OUTLINE_OUTLINED = "drive_file_rename_outline_outlined"
    DRIVE_FOLDER_UPLOAD = "drive_folder_upload"
    DRIVE_FOLDER_UPLOAD_SHARP = "drive_folder_upload_sharp"
    DRIVE_FOLDER_UPLOAD_ROUNDED = "drive_folder_upload_rounded"
    DRIVE_FOLDER_UPLOAD_OUTLINED = "drive_folder_upload_outlined"
    DRY = "dry"
    DRY_SHARP = "dry_sharp"
    DRY_ROUNDED = "dry_rounded"
    DRY_OUTLINED = "dry_outlined"
    DRY_CLEANING = "dry_cleaning"
    DRY_CLEANING_SHARP = "dry_cleaning_sharp"
    DRY_CLEANING_ROUNDED = "dry_cleaning_rounded"
    DRY_CLEANING_OUTLINED = "dry_cleaning_outlined"
    DUO = "duo"
    DUO_SHARP = "duo_sharp"
    DUO_ROUNDED = "duo_rounded"
    DUO_OUTLINED = "duo_outlined"
    DVR = "dvr"
    DVR_SHARP = "dvr_sharp"
    DVR_ROUNDED = "dvr_rounded"
    DVR_OUTLINED = "dvr_outlined"
    DYNAMIC_FEED = "dynamic_feed"
    DYNAMIC_FEED_SHARP = "dynamic_feed_sharp"
    DYNAMIC_FEED_ROUNDED = "dynamic_feed_rounded"
    DYNAMIC_FEED_OUTLINED = "dynamic_feed_outlined"
    DYNAMIC_FORM = "dynamic_form"
    DYNAMIC_FORM_SHARP = "dynamic_form_sharp"
    DYNAMIC_FORM_ROUNDED = "dynamic_form_rounded"
    DYNAMIC_FORM_OUTLINED = "dynamic_form_outlined"
    E_MOBILEDATA = "e_mobiledata"
    E_MOBILEDATA_SHARP = "e_mobiledata_sharp"
    E_MOBILEDATA_ROUNDED = "e_mobiledata_rounded"
    E_MOBILEDATA_OUTLINED = "e_mobiledata_outlined"
    EARBUDS = "earbuds"
    EARBUDS_SHARP = "earbuds_sharp"
    EARBUDS_ROUNDED = "earbuds_rounded"
    EARBUDS_OUTLINED = "earbuds_outlined"
    EARBUDS_BATTERY = "earbuds_battery"
    EARBUDS_BATTERY_SHARP = "earbuds_battery_sharp"
    EARBUDS_BATTERY_ROUNDED = "earbuds_battery_rounded"
    EARBUDS_BATTERY_OUTLINED = "earbuds_battery_outlined"
    EAST = "east"
    EAST_SHARP = "east_sharp"
    EAST_ROUNDED = "east_rounded"
    EAST_OUTLINED = "east_outlined"
    ECO = "eco"
    ECO_SHARP = "eco_sharp"
    ECO_ROUNDED = "eco_rounded"
    ECO_OUTLINED = "eco_outlined"
    EDGESENSOR_HIGH = "edgesensor_high"
    EDGESENSOR_HIGH_SHARP = "edgesensor_high_sharp"
    EDGESENSOR_HIGH_ROUNDED = "edgesensor_high_rounded"
    EDGESENSOR_HIGH_OUTLINED = "edgesensor_high_outlined"
    EDGESENSOR_LOW = "edgesensor_low"
    EDGESENSOR_LOW_SHARP = "edgesensor_low_sharp"
    EDGESENSOR_LOW_ROUNDED = "edgesensor_low_rounded"
    EDGESENSOR_LOW_OUTLINED = "edgesensor_low_outlined"
    EDIT = "edit"
    EDIT_SHARP = "edit_sharp"
    EDIT_ROUNDED = "edit_rounded"
    EDIT_OUTLINED = "edit_outlined"
    EDIT_ATTRIBUTES = "edit_attributes"
    EDIT_ATTRIBUTES_SHARP = "edit_attributes_sharp"
    EDIT_ATTRIBUTES_ROUNDED = "edit_attributes_rounded"
    EDIT_ATTRIBUTES_OUTLINED = "edit_attributes_outlined"
    EDIT_CALENDAR = "edit_calendar"
    EDIT_CALENDAR_SHARP = "edit_calendar_sharp"
    EDIT_CALENDAR_ROUNDED = "edit_calendar_rounded"
    EDIT_CALENDAR_OUTLINED = "edit_calendar_outlined"
    EDIT_DOCUMENT = "edit_document"
    EDIT_LOCATION = "edit_location"
    EDIT_LOCATION_SHARP = "edit_location_sharp"
    EDIT_LOCATION_ROUNDED = "edit_location_rounded"
    EDIT_LOCATION_OUTLINED = "edit_location_outlined"
    EDIT_LOCATION_ALT = "edit_location_alt"
    EDIT_LOCATION_ALT_SHARP = "edit_location_alt_sharp"
    EDIT_LOCATION_ALT_ROUNDED = "edit_location_alt_rounded"
    EDIT_LOCATION_ALT_OUTLINED = "edit_location_alt_outlined"
    EDIT_NOTE = "edit_note"
    EDIT_NOTE_SHARP = "edit_note_sharp"
    EDIT_NOTE_ROUNDED = "edit_note_rounded"
    EDIT_NOTE_OUTLINED = "edit_note_outlined"
    EDIT_NOTIFICATIONS = "edit_notifications"
    EDIT_NOTIFICATIONS_SHARP = "edit_notifications_sharp"
    EDIT_NOTIFICATIONS_ROUNDED = "edit_notifications_rounded"
    EDIT_NOTIFICATIONS_OUTLINED = "edit_notifications_outlined"
    EDIT_OFF = "edit_off"
    EDIT_OFF_SHARP = "edit_off_sharp"
    EDIT_OFF_ROUNDED = "edit_off_rounded"
    EDIT_OFF_OUTLINED = "edit_off_outlined"
    EDIT_ROAD = "edit_road"
    EDIT_ROAD_SHARP = "edit_road_sharp"
    EDIT_ROAD_ROUNDED = "edit_road_rounded"
    EDIT_ROAD_OUTLINED = "edit_road_outlined"
    EDIT_SQUARE = "edit_square"
    EGG = "egg"
    EGG_SHARP = "egg_sharp"
    EGG_ROUNDED = "egg_rounded"
    EGG_OUTLINED = "egg_outlined"
    EGG_ALT = "egg_alt"
    EGG_ALT_SHARP = "egg_alt_sharp"
    EGG_ALT_ROUNDED = "egg_alt_rounded"
    EGG_ALT_OUTLINED = "egg_alt_outlined"
    EJECT = "eject"
    EJECT_SHARP = "eject_sharp"
    EJECT_ROUNDED = "eject_rounded"
    EJECT_OUTLINED = "eject_outlined"
    ELDERLY = "elderly"
    ELDERLY_SHARP = "elderly_sharp"
    ELDERLY_ROUNDED = "elderly_rounded"
    ELDERLY_OUTLINED = "elderly_outlined"
    ELDERLY_WOMAN = "elderly_woman"
    ELDERLY_WOMAN_SHARP = "elderly_woman_sharp"
    ELDERLY_WOMAN_ROUNDED = "elderly_woman_rounded"
    ELDERLY_WOMAN_OUTLINED = "elderly_woman_outlined"
    ELECTRIC_BIKE = "electric_bike"
    ELECTRIC_BIKE_SHARP = "electric_bike_sharp"
    ELECTRIC_BIKE_ROUNDED = "electric_bike_rounded"
    ELECTRIC_BIKE_OUTLINED = "electric_bike_outlined"
    ELECTRIC_BOLT = "electric_bolt"
    ELECTRIC_BOLT_SHARP = "electric_bolt_sharp"
    ELECTRIC_BOLT_ROUNDED = "electric_bolt_rounded"
    ELECTRIC_BOLT_OUTLINED = "electric_bolt_outlined"
    ELECTRIC_CAR = "electric_car"
    ELECTRIC_CAR_SHARP = "electric_car_sharp"
    ELECTRIC_CAR_ROUNDED = "electric_car_rounded"
    ELECTRIC_CAR_OUTLINED = "electric_car_outlined"
    ELECTRIC_METER = "electric_meter"
    ELECTRIC_METER_SHARP = "electric_meter_sharp"
    ELECTRIC_METER_ROUNDED = "electric_meter_rounded"
    ELECTRIC_METER_OUTLINED = "electric_meter_outlined"
    ELECTRIC_MOPED = "electric_moped"
    ELECTRIC_MOPED_SHARP = "electric_moped_sharp"
    ELECTRIC_MOPED_ROUNDED = "electric_moped_rounded"
    ELECTRIC_MOPED_OUTLINED = "electric_moped_outlined"
    ELECTRIC_RICKSHAW = "electric_rickshaw"
    ELECTRIC_RICKSHAW_SHARP = "electric_rickshaw_sharp"
    ELECTRIC_RICKSHAW_ROUNDED = "electric_rickshaw_rounded"
    ELECTRIC_RICKSHAW_OUTLINED = "electric_rickshaw_outlined"
    ELECTRIC_SCOOTER = "electric_scooter"
    ELECTRIC_SCOOTER_SHARP = "electric_scooter_sharp"
    ELECTRIC_SCOOTER_ROUNDED = "electric_scooter_rounded"
    ELECTRIC_SCOOTER_OUTLINED = "electric_scooter_outlined"
    ELECTRICAL_SERVICES = "electrical_services"
    ELECTRICAL_SERVICES_SHARP = "electrical_services_sharp"
    ELECTRICAL_SERVICES_ROUNDED = "electrical_services_rounded"
    ELECTRICAL_SERVICES_OUTLINED = "electrical_services_outlined"
    ELEVATOR = "elevator"
    ELEVATOR_SHARP = "elevator_sharp"
    ELEVATOR_ROUNDED = "elevator_rounded"
    ELEVATOR_OUTLINED = "elevator_outlined"
    EMAIL = "email"
    EMAIL_SHARP = "email_sharp"
    EMAIL_ROUNDED = "email_rounded"
    EMAIL_OUTLINED = "email_outlined"
    EMERGENCY = "emergency"
    EMERGENCY_SHARP = "emergency_sharp"
    EMERGENCY_ROUNDED = "emergency_rounded"
    EMERGENCY_OUTLINED = "emergency_outlined"
    EMERGENCY_RECORDING = "emergency_recording"
    EMERGENCY_RECORDING_SHARP = "emergency_recording_sharp"
    EMERGENCY_RECORDING_ROUNDED = "emergency_recording_rounded"
    EMERGENCY_RECORDING_OUTLINED = "emergency_recording_outlined"
    EMERGENCY_SHARE = "emergency_share"
    EMERGENCY_SHARE_SHARP = "emergency_share_sharp"
    EMERGENCY_SHARE_ROUNDED = "emergency_share_rounded"
    EMERGENCY_SHARE_OUTLINED = "emergency_share_outlined"
    EMOJI_EMOTIONS = "emoji_emotions"
    EMOJI_EMOTIONS_SHARP = "emoji_emotions_sharp"
    EMOJI_EMOTIONS_ROUNDED = "emoji_emotions_rounded"
    EMOJI_EMOTIONS_OUTLINED = "emoji_emotions_outlined"
    EMOJI_EVENTS = "emoji_events"
    EMOJI_EVENTS_SHARP = "emoji_events_sharp"
    EMOJI_EVENTS_ROUNDED = "emoji_events_rounded"
    EMOJI_EVENTS_OUTLINED = "emoji_events_outlined"
    EMOJI_FLAGS = "emoji_flags"
    EMOJI_FLAGS_SHARP = "emoji_flags_sharp"
    EMOJI_FLAGS_ROUNDED = "emoji_flags_rounded"
    EMOJI_FLAGS_OUTLINED = "emoji_flags_outlined"
    EMOJI_FOOD_BEVERAGE = "emoji_food_beverage"
    EMOJI_FOOD_BEVERAGE_SHARP = "emoji_food_beverage_sharp"
    EMOJI_FOOD_BEVERAGE_ROUNDED = "emoji_food_beverage_rounded"
    EMOJI_FOOD_BEVERAGE_OUTLINED = "emoji_food_beverage_outlined"
    EMOJI_NATURE = "emoji_nature"
    EMOJI_NATURE_SHARP = "emoji_nature_sharp"
    EMOJI_NATURE_ROUNDED = "emoji_nature_rounded"
    EMOJI_NATURE_OUTLINED = "emoji_nature_outlined"
    EMOJI_OBJECTS = "emoji_objects"
    EMOJI_OBJECTS_SHARP = "emoji_objects_sharp"
    EMOJI_OBJECTS_ROUNDED = "emoji_objects_rounded"
    EMOJI_OBJECTS_OUTLINED = "emoji_objects_outlined"
    EMOJI_PEOPLE = "emoji_people"
    EMOJI_PEOPLE_SHARP = "emoji_people_sharp"
    EMOJI_PEOPLE_ROUNDED = "emoji_people_rounded"
    EMOJI_PEOPLE_OUTLINED = "emoji_people_outlined"
    EMOJI_SYMBOLS = "emoji_symbols"
    EMOJI_SYMBOLS_SHARP = "emoji_symbols_sharp"
    EMOJI_SYMBOLS_ROUNDED = "emoji_symbols_rounded"
    EMOJI_SYMBOLS_OUTLINED = "emoji_symbols_outlined"
    EMOJI_TRANSPORTATION = "emoji_transportation"
    EMOJI_TRANSPORTATION_SHARP = "emoji_transportation_sharp"
    EMOJI_TRANSPORTATION_ROUNDED = "emoji_transportation_rounded"
    EMOJI_TRANSPORTATION_OUTLINED = "emoji_transportation_outlined"
    ENERGY_SAVINGS_LEAF = "energy_savings_leaf"
    ENERGY_SAVINGS_LEAF_SHARP = "energy_savings_leaf_sharp"
    ENERGY_SAVINGS_LEAF_ROUNDED = "energy_savings_leaf_rounded"
    ENERGY_SAVINGS_LEAF_OUTLINED = "energy_savings_leaf_outlined"
    ENGINEERING = "engineering"
    ENGINEERING_SHARP = "engineering_sharp"
    ENGINEERING_ROUNDED = "engineering_rounded"
    ENGINEERING_OUTLINED = "engineering_outlined"
    ENHANCE_PHOTO_TRANSLATE = "enhance_photo_translate"
    ENHANCE_PHOTO_TRANSLATE_SHARP = "enhance_photo_translate_sharp"
    ENHANCE_PHOTO_TRANSLATE_ROUNDED = "enhance_photo_translate_rounded"
    ENHANCE_PHOTO_TRANSLATE_OUTLINED = "enhance_photo_translate_outlined"
    ENHANCED_ENCRYPTION = "enhanced_encryption"
    ENHANCED_ENCRYPTION_SHARP = "enhanced_encryption_sharp"
    ENHANCED_ENCRYPTION_ROUNDED = "enhanced_encryption_rounded"
    ENHANCED_ENCRYPTION_OUTLINED = "enhanced_encryption_outlined"
    EQUALIZER = "equalizer"
    EQUALIZER_SHARP = "equalizer_sharp"
    EQUALIZER_ROUNDED = "equalizer_rounded"
    EQUALIZER_OUTLINED = "equalizer_outlined"
    ERROR = "error"
    ERROR_SHARP = "error_sharp"
    ERROR_ROUNDED = "error_rounded"
    ERROR_OUTLINED = "error_outlined"
    ERROR_OUTLINE = "error_outline"
    ERROR_OUTLINE_SHARP = "error_outline_sharp"
    ERROR_OUTLINE_ROUNDED = "error_outline_rounded"
    ERROR_OUTLINE_OUTLINED = "error_outline_outlined"
    ESCALATOR = "escalator"
    ESCALATOR_SHARP = "escalator_sharp"
    ESCALATOR_ROUNDED = "escalator_rounded"
    ESCALATOR_OUTLINED = "escalator_outlined"
    ESCALATOR_WARNING = "escalator_warning"
    ESCALATOR_WARNING_SHARP = "escalator_warning_sharp"
    ESCALATOR_WARNING_ROUNDED = "escalator_warning_rounded"
    ESCALATOR_WARNING_OUTLINED = "escalator_warning_outlined"
    EURO = "euro"
    EURO_SHARP = "euro_sharp"
    EURO_ROUNDED = "euro_rounded"
    EURO_OUTLINED = "euro_outlined"
    EURO_SYMBOL = "euro_symbol"
    EURO_SYMBOL_SHARP = "euro_symbol_sharp"
    EURO_SYMBOL_ROUNDED = "euro_symbol_rounded"
    EURO_SYMBOL_OUTLINED = "euro_symbol_outlined"
    EV_STATION = "ev_station"
    EV_STATION_SHARP = "ev_station_sharp"
    EV_STATION_ROUNDED = "ev_station_rounded"
    EV_STATION_OUTLINED = "ev_station_outlined"
    EVENT = "event"
    EVENT_SHARP = "event_sharp"
    EVENT_ROUNDED = "event_rounded"
    EVENT_OUTLINED = "event_outlined"
    EVENT_AVAILABLE = "event_available"
    EVENT_AVAILABLE_SHARP = "event_available_sharp"
    EVENT_AVAILABLE_ROUNDED = "event_available_rounded"
    EVENT_AVAILABLE_OUTLINED = "event_available_outlined"
    EVENT_BUSY = "event_busy"
    EVENT_BUSY_SHARP = "event_busy_sharp"
    EVENT_BUSY_ROUNDED = "event_busy_rounded"
    EVENT_BUSY_OUTLINED = "event_busy_outlined"
    EVENT_NOTE = "event_note"
    EVENT_NOTE_SHARP = "event_note_sharp"
    EVENT_NOTE_ROUNDED = "event_note_rounded"
    EVENT_NOTE_OUTLINED = "event_note_outlined"
    EVENT_REPEAT = "event_repeat"
    EVENT_REPEAT_SHARP = "event_repeat_sharp"
    EVENT_REPEAT_ROUNDED = "event_repeat_rounded"
    EVENT_REPEAT_OUTLINED = "event_repeat_outlined"
    EVENT_SEAT = "event_seat"
    EVENT_SEAT_SHARP = "event_seat_sharp"
    EVENT_SEAT_ROUNDED = "event_seat_rounded"
    EVENT_SEAT_OUTLINED = "event_seat_outlined"
    EXIT_TO_APP = "exit_to_app"
    EXIT_TO_APP_SHARP = "exit_to_app_sharp"
    EXIT_TO_APP_ROUNDED = "exit_to_app_rounded"
    EXIT_TO_APP_OUTLINED = "exit_to_app_outlined"
    EXPAND = "expand"
    EXPAND_SHARP = "expand_sharp"
    EXPAND_ROUNDED = "expand_rounded"
    EXPAND_OUTLINED = "expand_outlined"
    EXPAND_CIRCLE_DOWN = "expand_circle_down"
    EXPAND_CIRCLE_DOWN_SHARP = "expand_circle_down_sharp"
    EXPAND_CIRCLE_DOWN_ROUNDED = "expand_circle_down_rounded"
    EXPAND_CIRCLE_DOWN_OUTLINED = "expand_circle_down_outlined"
    EXPAND_LESS = "expand_less"
    EXPAND_LESS_SHARP = "expand_less_sharp"
    EXPAND_LESS_ROUNDED = "expand_less_rounded"
    EXPAND_LESS_OUTLINED = "expand_less_outlined"
    EXPAND_MORE = "expand_more"
    EXPAND_MORE_SHARP = "expand_more_sharp"
    EXPAND_MORE_ROUNDED = "expand_more_rounded"
    EXPAND_MORE_OUTLINED = "expand_more_outlined"
    EXPLICIT = "explicit"
    EXPLICIT_SHARP = "explicit_sharp"
    EXPLICIT_ROUNDED = "explicit_rounded"
    EXPLICIT_OUTLINED = "explicit_outlined"
    EXPLORE = "explore"
    EXPLORE_SHARP = "explore_sharp"
    EXPLORE_ROUNDED = "explore_rounded"
    EXPLORE_OUTLINED = "explore_outlined"
    EXPLORE_OFF = "explore_off"
    EXPLORE_OFF_SHARP = "explore_off_sharp"
    EXPLORE_OFF_ROUNDED = "explore_off_rounded"
    EXPLORE_OFF_OUTLINED = "explore_off_outlined"
    EXPOSURE = "exposure"
    EXPOSURE_SHARP = "exposure_sharp"
    EXPOSURE_ROUNDED = "exposure_rounded"
    EXPOSURE_OUTLINED = "exposure_outlined"
    EXPOSURE_MINUS_1 = "exposure_minus_1"
    EXPOSURE_MINUS_1_SHARP = "exposure_minus_1_sharp"
    EXPOSURE_MINUS_1_ROUNDED = "exposure_minus_1_rounded"
    EXPOSURE_MINUS_1_OUTLINED = "exposure_minus_1_outlined"
    EXPOSURE_MINUS_2 = "exposure_minus_2"
    EXPOSURE_MINUS_2_SHARP = "exposure_minus_2_sharp"
    EXPOSURE_MINUS_2_ROUNDED = "exposure_minus_2_rounded"
    EXPOSURE_MINUS_2_OUTLINED = "exposure_minus_2_outlined"
    EXPOSURE_NEG_1 = "exposure_neg_1"
    EXPOSURE_NEG_1_SHARP = "exposure_neg_1_sharp"
    EXPOSURE_NEG_1_ROUNDED = "exposure_neg_1_rounded"
    EXPOSURE_NEG_1_OUTLINED = "exposure_neg_1_outlined"
    EXPOSURE_NEG_2 = "exposure_neg_2"
    EXPOSURE_NEG_2_SHARP = "exposure_neg_2_sharp"
    EXPOSURE_NEG_2_ROUNDED = "exposure_neg_2_rounded"
    EXPOSURE_NEG_2_OUTLINED = "exposure_neg_2_outlined"
    EXPOSURE_PLUS_1 = "exposure_plus_1"
    EXPOSURE_PLUS_1_SHARP = "exposure_plus_1_sharp"
    EXPOSURE_PLUS_1_ROUNDED = "exposure_plus_1_rounded"
    EXPOSURE_PLUS_1_OUTLINED = "exposure_plus_1_outlined"
    EXPOSURE_PLUS_2 = "exposure_plus_2"
    EXPOSURE_PLUS_2_SHARP = "exposure_plus_2_sharp"
    EXPOSURE_PLUS_2_ROUNDED = "exposure_plus_2_rounded"
    EXPOSURE_PLUS_2_OUTLINED = "exposure_plus_2_outlined"
    EXPOSURE_ZERO = "exposure_zero"
    EXPOSURE_ZERO_SHARP = "exposure_zero_sharp"
    EXPOSURE_ZERO_ROUNDED = "exposure_zero_rounded"
    EXPOSURE_ZERO_OUTLINED = "exposure_zero_outlined"
    EXTENSION = "extension"
    EXTENSION_SHARP = "extension_sharp"
    EXTENSION_ROUNDED = "extension_rounded"
    EXTENSION_OUTLINED = "extension_outlined"
    EXTENSION_OFF = "extension_off"
    EXTENSION_OFF_SHARP = "extension_off_sharp"
    EXTENSION_OFF_ROUNDED = "extension_off_rounded"
    EXTENSION_OFF_OUTLINED = "extension_off_outlined"
    FACE = "face"
    FACE_SHARP = "face_sharp"
    FACE_ROUNDED = "face_rounded"
    FACE_OUTLINED = "face_outlined"
    FACE_2 = "face_2"
    FACE_2_SHARP = "face_2_sharp"
    FACE_2_ROUNDED = "face_2_rounded"
    FACE_2_OUTLINED = "face_2_outlined"
    FACE_3 = "face_3"
    FACE_3_SHARP = "face_3_sharp"
    FACE_3_ROUNDED = "face_3_rounded"
    FACE_3_OUTLINED = "face_3_outlined"
    FACE_4 = "face_4"
    FACE_4_SHARP = "face_4_sharp"
    FACE_4_ROUNDED = "face_4_rounded"
    FACE_4_OUTLINED = "face_4_outlined"
    FACE_5 = "face_5"
    FACE_5_SHARP = "face_5_sharp"
    FACE_5_ROUNDED = "face_5_rounded"
    FACE_5_OUTLINED = "face_5_outlined"
    FACE_6 = "face_6"
    FACE_6_SHARP = "face_6_sharp"
    FACE_6_ROUNDED = "face_6_rounded"
    FACE_6_OUTLINED = "face_6_outlined"
    FACE_RETOUCHING_NATURAL = "face_retouching_natural"
    FACE_RETOUCHING_NATURAL_SHARP = "face_retouching_natural_sharp"
    FACE_RETOUCHING_NATURAL_ROUNDED = "face_retouching_natural_rounded"
    FACE_RETOUCHING_NATURAL_OUTLINED = "face_retouching_natural_outlined"
    FACE_RETOUCHING_OFF = "face_retouching_off"
    FACE_RETOUCHING_OFF_SHARP = "face_retouching_off_sharp"
    FACE_RETOUCHING_OFF_ROUNDED = "face_retouching_off_rounded"
    FACE_RETOUCHING_OFF_OUTLINED = "face_retouching_off_outlined"
    FACE_UNLOCK_SHARP = "face_unlock_sharp"
    FACE_UNLOCK_ROUNDED = "face_unlock_rounded"
    FACE_UNLOCK_OUTLINED = "face_unlock_outlined"
    FACEBOOK = "facebook"
    FACEBOOK_SHARP = "facebook_sharp"
    FACEBOOK_ROUNDED = "facebook_rounded"
    FACEBOOK_OUTLINED = "facebook_outlined"
    FACT_CHECK = "fact_check"
    FACT_CHECK_SHARP = "fact_check_sharp"
    FACT_CHECK_ROUNDED = "fact_check_rounded"
    FACT_CHECK_OUTLINED = "fact_check_outlined"
    FACTORY = "factory"
    FACTORY_SHARP = "factory_sharp"
    FACTORY_ROUNDED = "factory_rounded"
    FACTORY_OUTLINED = "factory_outlined"
    FAMILY_RESTROOM = "family_restroom"
    FAMILY_RESTROOM_SHARP = "family_restroom_sharp"
    FAMILY_RESTROOM_ROUNDED = "family_restroom_rounded"
    FAMILY_RESTROOM_OUTLINED = "family_restroom_outlined"
    FAST_FORWARD = "fast_forward"
    FAST_FORWARD_SHARP = "fast_forward_sharp"
    FAST_FORWARD_ROUNDED = "fast_forward_rounded"
    FAST_FORWARD_OUTLINED = "fast_forward_outlined"
    FAST_REWIND = "fast_rewind"
    FAST_REWIND_SHARP = "fast_rewind_sharp"
    FAST_REWIND_ROUNDED = "fast_rewind_rounded"
    FAST_REWIND_OUTLINED = "fast_rewind_outlined"
    FASTFOOD = "fastfood"
    FASTFOOD_SHARP = "fastfood_sharp"
    FASTFOOD_ROUNDED = "fastfood_rounded"
    FASTFOOD_OUTLINED = "fastfood_outlined"
    FAVORITE = "favorite"
    FAVORITE_SHARP = "favorite_sharp"
    FAVORITE_ROUNDED = "favorite_rounded"
    FAVORITE_OUTLINED = "favorite_outlined"
    FAVORITE_BORDER = "favorite_border"
    FAVORITE_BORDER_SHARP = "favorite_border_sharp"
    FAVORITE_BORDER_ROUNDED = "favorite_border_rounded"
    FAVORITE_BORDER_OUTLINED = "favorite_border_outlined"
    FAVORITE_OUTLINE = "favorite_outline"
    FAVORITE_OUTLINE_SHARP = "favorite_outline_sharp"
    FAVORITE_OUTLINE_ROUNDED = "favorite_outline_rounded"
    FAVORITE_OUTLINE_OUTLINED = "favorite_outline_outlined"
    FAX = "fax"
    FAX_SHARP = "fax_sharp"
    FAX_ROUNDED = "fax_rounded"
    FAX_OUTLINED = "fax_outlined"
    FEATURED_PLAY_LIST = "featured_play_list"
    FEATURED_PLAY_LIST_SHARP = "featured_play_list_sharp"
    FEATURED_PLAY_LIST_ROUNDED = "featured_play_list_rounded"
    FEATURED_PLAY_LIST_OUTLINED = "featured_play_list_outlined"
    FEATURED_VIDEO = "featured_video"
    FEATURED_VIDEO_SHARP = "featured_video_sharp"
    FEATURED_VIDEO_ROUNDED = "featured_video_rounded"
    FEATURED_VIDEO_OUTLINED = "featured_video_outlined"
    FEED = "feed"
    FEED_SHARP = "feed_sharp"
    FEED_ROUNDED = "feed_rounded"
    FEED_OUTLINED = "feed_outlined"
    FEEDBACK = "feedback"
    FEEDBACK_SHARP = "feedback_sharp"
    FEEDBACK_ROUNDED = "feedback_rounded"
    FEEDBACK_OUTLINED = "feedback_outlined"
    FEMALE = "female"
    FEMALE_SHARP = "female_sharp"
    FEMALE_ROUNDED = "female_rounded"
    FEMALE_OUTLINED = "female_outlined"
    FENCE = "fence"
    FENCE_SHARP = "fence_sharp"
    FENCE_ROUNDED = "fence_rounded"
    FENCE_OUTLINED = "fence_outlined"
    FESTIVAL = "festival"
    FESTIVAL_SHARP = "festival_sharp"
    FESTIVAL_ROUNDED = "festival_rounded"
    FESTIVAL_OUTLINED = "festival_outlined"
    FIBER_DVR = "fiber_dvr"
    FIBER_DVR_SHARP = "fiber_dvr_sharp"
    FIBER_DVR_ROUNDED = "fiber_dvr_rounded"
    FIBER_DVR_OUTLINED = "fiber_dvr_outlined"
    FIBER_MANUAL_RECORD = "fiber_manual_record"
    FIBER_MANUAL_RECORD_SHARP = "fiber_manual_record_sharp"
    FIBER_MANUAL_RECORD_ROUNDED = "fiber_manual_record_rounded"
    FIBER_MANUAL_RECORD_OUTLINED = "fiber_manual_record_outlined"
    FIBER_NEW = "fiber_new"
    FIBER_NEW_SHARP = "fiber_new_sharp"
    FIBER_NEW_ROUNDED = "fiber_new_rounded"
    FIBER_NEW_OUTLINED = "fiber_new_outlined"
    FIBER_PIN = "fiber_pin"
    FIBER_PIN_SHARP = "fiber_pin_sharp"
    FIBER_PIN_ROUNDED = "fiber_pin_rounded"
    FIBER_PIN_OUTLINED = "fiber_pin_outlined"
    FIBER_SMART_RECORD = "fiber_smart_record"
    FIBER_SMART_RECORD_SHARP = "fiber_smart_record_sharp"
    FIBER_SMART_RECORD_ROUNDED = "fiber_smart_record_rounded"
    FIBER_SMART_RECORD_OUTLINED = "fiber_smart_record_outlined"
    FILE_COPY = "file_copy"
    FILE_COPY_SHARP = "file_copy_sharp"
    FILE_COPY_ROUNDED = "file_copy_rounded"
    FILE_COPY_OUTLINED = "file_copy_outlined"
    FILE_DOWNLOAD = "file_download"
    FILE_DOWNLOAD_SHARP = "file_download_sharp"
    FILE_DOWNLOAD_ROUNDED = "file_download_rounded"
    FILE_DOWNLOAD_OUTLINED = "file_download_outlined"
    FILE_DOWNLOAD_DONE = "file_download_done"
    FILE_DOWNLOAD_DONE_SHARP = "file_download_done_sharp"
    FILE_DOWNLOAD_DONE_ROUNDED = "file_download_done_rounded"
    FILE_DOWNLOAD_DONE_OUTLINED = "file_download_done_outlined"
    FILE_DOWNLOAD_OFF = "file_download_off"
    FILE_DOWNLOAD_OFF_SHARP = "file_download_off_sharp"
    FILE_DOWNLOAD_OFF_ROUNDED = "file_download_off_rounded"
    FILE_DOWNLOAD_OFF_OUTLINED = "file_download_off_outlined"
    FILE_OPEN = "file_open"
    FILE_OPEN_SHARP = "file_open_sharp"
    FILE_OPEN_ROUNDED = "file_open_rounded"
    FILE_OPEN_OUTLINED = "file_open_outlined"
    FILE_PRESENT = "file_present"
    FILE_PRESENT_SHARP = "file_present_sharp"
    FILE_PRESENT_ROUNDED = "file_present_rounded"
    FILE_PRESENT_OUTLINED = "file_present_outlined"
    FILE_UPLOAD = "file_upload"
    FILE_UPLOAD_SHARP = "file_upload_sharp"
    FILE_UPLOAD_ROUNDED = "file_upload_rounded"
    FILE_UPLOAD_OUTLINED = "file_upload_outlined"
    FILE_UPLOAD_OFF = "file_upload_off"
    FILTER = "filter"
    FILTER_SHARP = "filter_sharp"
    FILTER_ROUNDED = "filter_rounded"
    FILTER_OUTLINED = "filter_outlined"
    FILTER_1 = "filter_1"
    FILTER_1_SHARP = "filter_1_sharp"
    FILTER_1_ROUNDED = "filter_1_rounded"
    FILTER_1_OUTLINED = "filter_1_outlined"
    FILTER_2 = "filter_2"
    FILTER_2_SHARP = "filter_2_sharp"
    FILTER_2_ROUNDED = "filter_2_rounded"
    FILTER_2_OUTLINED = "filter_2_outlined"
    FILTER_3 = "filter_3"
    FILTER_3_SHARP = "filter_3_sharp"
    FILTER_3_ROUNDED = "filter_3_rounded"
    FILTER_3_OUTLINED = "filter_3_outlined"
    FILTER_4 = "filter_4"
    FILTER_4_SHARP = "filter_4_sharp"
    FILTER_4_ROUNDED = "filter_4_rounded"
    FILTER_4_OUTLINED = "filter_4_outlined"
    FILTER_5 = "filter_5"
    FILTER_5_SHARP = "filter_5_sharp"
    FILTER_5_ROUNDED = "filter_5_rounded"
    FILTER_5_OUTLINED = "filter_5_outlined"
    FILTER_6 = "filter_6"
    FILTER_6_SHARP = "filter_6_sharp"
    FILTER_6_ROUNDED = "filter_6_rounded"
    FILTER_6_OUTLINED = "filter_6_outlined"
    FILTER_7 = "filter_7"
    FILTER_7_SHARP = "filter_7_sharp"
    FILTER_7_ROUNDED = "filter_7_rounded"
    FILTER_7_OUTLINED = "filter_7_outlined"
    FILTER_8 = "filter_8"
    FILTER_8_SHARP = "filter_8_sharp"
    FILTER_8_ROUNDED = "filter_8_rounded"
    FILTER_8_OUTLINED = "filter_8_outlined"
    FILTER_9 = "filter_9"
    FILTER_9_SHARP = "filter_9_sharp"
    FILTER_9_ROUNDED = "filter_9_rounded"
    FILTER_9_OUTLINED = "filter_9_outlined"
    FILTER_9_PLUS = "filter_9_plus"
    FILTER_9_PLUS_SHARP = "filter_9_plus_sharp"
    FILTER_9_PLUS_ROUNDED = "filter_9_plus_rounded"
    FILTER_9_PLUS_OUTLINED = "filter_9_plus_outlined"
    FILTER_ALT = "filter_alt"
    FILTER_ALT_SHARP = "filter_alt_sharp"
    FILTER_ALT_ROUNDED = "filter_alt_rounded"
    FILTER_ALT_OUTLINED = "filter_alt_outlined"
    FILTER_ALT_OFF = "filter_alt_off"
    FILTER_ALT_OFF_SHARP = "filter_alt_off_sharp"
    FILTER_ALT_OFF_ROUNDED = "filter_alt_off_rounded"
    FILTER_ALT_OFF_OUTLINED = "filter_alt_off_outlined"
    FILTER_B_AND_W = "filter_b_and_w"
    FILTER_B_AND_W_SHARP = "filter_b_and_w_sharp"
    FILTER_B_AND_W_ROUNDED = "filter_b_and_w_rounded"
    FILTER_B_AND_W_OUTLINED = "filter_b_and_w_outlined"
    FILTER_CENTER_FOCUS = "filter_center_focus"
    FILTER_CENTER_FOCUS_SHARP = "filter_center_focus_sharp"
    FILTER_CENTER_FOCUS_ROUNDED = "filter_center_focus_rounded"
    FILTER_CENTER_FOCUS_OUTLINED = "filter_center_focus_outlined"
    FILTER_DRAMA = "filter_drama"
    FILTER_DRAMA_SHARP = "filter_drama_sharp"
    FILTER_DRAMA_ROUNDED = "filter_drama_rounded"
    FILTER_DRAMA_OUTLINED = "filter_drama_outlined"
    FILTER_FRAMES = "filter_frames"
    FILTER_FRAMES_SHARP = "filter_frames_sharp"
    FILTER_FRAMES_ROUNDED = "filter_frames_rounded"
    FILTER_FRAMES_OUTLINED = "filter_frames_outlined"
    FILTER_HDR = "filter_hdr"
    FILTER_HDR_SHARP = "filter_hdr_sharp"
    FILTER_HDR_ROUNDED = "filter_hdr_rounded"
    FILTER_HDR_OUTLINED = "filter_hdr_outlined"
    FILTER_LIST = "filter_list"
    FILTER_LIST_SHARP = "filter_list_sharp"
    FILTER_LIST_ROUNDED = "filter_list_rounded"
    FILTER_LIST_OUTLINED = "filter_list_outlined"
    FILTER_LIST_ALT = "filter_list_alt"
    FILTER_LIST_OFF = "filter_list_off"
    FILTER_LIST_OFF_SHARP = "filter_list_off_sharp"
    FILTER_LIST_OFF_ROUNDED = "filter_list_off_rounded"
    FILTER_LIST_OFF_OUTLINED = "filter_list_off_outlined"
    FILTER_NONE = "filter_none"
    FILTER_NONE_SHARP = "filter_none_sharp"
    FILTER_NONE_ROUNDED = "filter_none_rounded"
    FILTER_NONE_OUTLINED = "filter_none_outlined"
    FILTER_TILT_SHIFT = "filter_tilt_shift"
    FILTER_TILT_SHIFT_SHARP = "filter_tilt_shift_sharp"
    FILTER_TILT_SHIFT_ROUNDED = "filter_tilt_shift_rounded"
    FILTER_TILT_SHIFT_OUTLINED = "filter_tilt_shift_outlined"
    FILTER_VINTAGE = "filter_vintage"
    FILTER_VINTAGE_SHARP = "filter_vintage_sharp"
    FILTER_VINTAGE_ROUNDED = "filter_vintage_rounded"
    FILTER_VINTAGE_OUTLINED = "filter_vintage_outlined"
    FIND_IN_PAGE = "find_in_page"
    FIND_IN_PAGE_SHARP = "find_in_page_sharp"
    FIND_IN_PAGE_ROUNDED = "find_in_page_rounded"
    FIND_IN_PAGE_OUTLINED = "find_in_page_outlined"
    FIND_REPLACE = "find_replace"
    FIND_REPLACE_SHARP = "find_replace_sharp"
    FIND_REPLACE_ROUNDED = "find_replace_rounded"
    FIND_REPLACE_OUTLINED = "find_replace_outlined"
    FINGERPRINT = "fingerprint"
    FINGERPRINT_SHARP = "fingerprint_sharp"
    FINGERPRINT_ROUNDED = "fingerprint_rounded"
    FINGERPRINT_OUTLINED = "fingerprint_outlined"
    FIRE_EXTINGUISHER = "fire_extinguisher"
    FIRE_EXTINGUISHER_SHARP = "fire_extinguisher_sharp"
    FIRE_EXTINGUISHER_ROUNDED = "fire_extinguisher_rounded"
    FIRE_EXTINGUISHER_OUTLINED = "fire_extinguisher_outlined"
    FIRE_HYDRANT = "fire_hydrant"
    FIRE_HYDRANT_ALT = "fire_hydrant_alt"
    FIRE_HYDRANT_ALT_SHARP = "fire_hydrant_alt_sharp"
    FIRE_HYDRANT_ALT_ROUNDED = "fire_hydrant_alt_rounded"
    FIRE_HYDRANT_ALT_OUTLINED = "fire_hydrant_alt_outlined"
    FIRE_TRUCK = "fire_truck"
    FIRE_TRUCK_SHARP = "fire_truck_sharp"
    FIRE_TRUCK_ROUNDED = "fire_truck_rounded"
    FIRE_TRUCK_OUTLINED = "fire_truck_outlined"
    FIREPLACE = "fireplace"
    FIREPLACE_SHARP = "fireplace_sharp"
    FIREPLACE_ROUNDED = "fireplace_rounded"
    FIREPLACE_OUTLINED = "fireplace_outlined"
    FIRST_PAGE = "first_page"
    FIRST_PAGE_SHARP = "first_page_sharp"
    FIRST_PAGE_ROUNDED = "first_page_rounded"
    FIRST_PAGE_OUTLINED = "first_page_outlined"
    FIT_SCREEN = "fit_screen"
    FIT_SCREEN_SHARP = "fit_screen_sharp"
    FIT_SCREEN_ROUNDED = "fit_screen_rounded"
    FIT_SCREEN_OUTLINED = "fit_screen_outlined"
    FITBIT = "fitbit"
    FITBIT_SHARP = "fitbit_sharp"
    FITBIT_ROUNDED = "fitbit_rounded"
    FITBIT_OUTLINED = "fitbit_outlined"
    FITNESS_CENTER = "fitness_center"
    FITNESS_CENTER_SHARP = "fitness_center_sharp"
    FITNESS_CENTER_ROUNDED = "fitness_center_rounded"
    FITNESS_CENTER_OUTLINED = "fitness_center_outlined"
    FLAG = "flag"
    FLAG_SHARP = "flag_sharp"
    FLAG_ROUNDED = "flag_rounded"
    FLAG_OUTLINED = "flag_outlined"
    FLAG_CIRCLE = "flag_circle"
    FLAG_CIRCLE_SHARP = "flag_circle_sharp"
    FLAG_CIRCLE_ROUNDED = "flag_circle_rounded"
    FLAG_CIRCLE_OUTLINED = "flag_circle_outlined"
    FLAKY = "flaky"
    FLAKY_SHARP = "flaky_sharp"
    FLAKY_ROUNDED = "flaky_rounded"
    FLAKY_OUTLINED = "flaky_outlined"
    FLARE = "flare"
    FLARE_SHARP = "flare_sharp"
    FLARE_ROUNDED = "flare_rounded"
    FLARE_OUTLINED = "flare_outlined"
    FLASH_AUTO = "flash_auto"
    FLASH_AUTO_SHARP = "flash_auto_sharp"
    FLASH_AUTO_ROUNDED = "flash_auto_rounded"
    FLASH_AUTO_OUTLINED = "flash_auto_outlined"
    FLASH_OFF = "flash_off"
    FLASH_OFF_SHARP = "flash_off_sharp"
    FLASH_OFF_ROUNDED = "flash_off_rounded"
    FLASH_OFF_OUTLINED = "flash_off_outlined"
    FLASH_ON = "flash_on"
    FLASH_ON_SHARP = "flash_on_sharp"
    FLASH_ON_ROUNDED = "flash_on_rounded"
    FLASH_ON_OUTLINED = "flash_on_outlined"
    FLASHLIGHT_OFF = "flashlight_off"
    FLASHLIGHT_OFF_SHARP = "flashlight_off_sharp"
    FLASHLIGHT_OFF_ROUNDED = "flashlight_off_rounded"
    FLASHLIGHT_OFF_OUTLINED = "flashlight_off_outlined"
    FLASHLIGHT_ON = "flashlight_on"
    FLASHLIGHT_ON_SHARP = "flashlight_on_sharp"
    FLASHLIGHT_ON_ROUNDED = "flashlight_on_rounded"
    FLASHLIGHT_ON_OUTLINED = "flashlight_on_outlined"
    FLATWARE = "flatware"
    FLATWARE_SHARP = "flatware_sharp"
    FLATWARE_ROUNDED = "flatware_rounded"
    FLATWARE_OUTLINED = "flatware_outlined"
    FLIGHT = "flight"
    FLIGHT_SHARP = "flight_sharp"
    FLIGHT_ROUNDED = "flight_rounded"
    FLIGHT_OUTLINED = "flight_outlined"
    FLIGHT_CLASS = "flight_class"
    FLIGHT_CLASS_SHARP = "flight_class_sharp"
    FLIGHT_CLASS_ROUNDED = "flight_class_rounded"
    FLIGHT_CLASS_OUTLINED = "flight_class_outlined"
    FLIGHT_LAND = "flight_land"
    FLIGHT_LAND_SHARP = "flight_land_sharp"
    FLIGHT_LAND_ROUNDED = "flight_land_rounded"
    FLIGHT_LAND_OUTLINED = "flight_land_outlined"
    FLIGHT_TAKEOFF = "flight_takeoff"
    FLIGHT_TAKEOFF_SHARP = "flight_takeoff_sharp"
    FLIGHT_TAKEOFF_ROUNDED = "flight_takeoff_rounded"
    FLIGHT_TAKEOFF_OUTLINED = "flight_takeoff_outlined"
    FLIP = "flip"
    FLIP_SHARP = "flip_sharp"
    FLIP_ROUNDED = "flip_rounded"
    FLIP_OUTLINED = "flip_outlined"
    FLIP_CAMERA_ANDROID = "flip_camera_android"
    FLIP_CAMERA_ANDROID_SHARP = "flip_camera_android_sharp"
    FLIP_CAMERA_ANDROID_ROUNDED = "flip_camera_android_rounded"
    FLIP_CAMERA_ANDROID_OUTLINED = "flip_camera_android_outlined"
    FLIP_CAMERA_IOS = "flip_camera_ios"
    FLIP_CAMERA_IOS_SHARP = "flip_camera_ios_sharp"
    FLIP_CAMERA_IOS_ROUNDED = "flip_camera_ios_rounded"
    FLIP_CAMERA_IOS_OUTLINED = "flip_camera_ios_outlined"
    FLIP_TO_BACK = "flip_to_back"
    FLIP_TO_BACK_SHARP = "flip_to_back_sharp"
    FLIP_TO_BACK_ROUNDED = "flip_to_back_rounded"
    FLIP_TO_BACK_OUTLINED = "flip_to_back_outlined"
    FLIP_TO_FRONT = "flip_to_front"
    FLIP_TO_FRONT_SHARP = "flip_to_front_sharp"
    FLIP_TO_FRONT_ROUNDED = "flip_to_front_rounded"
    FLIP_TO_FRONT_OUTLINED = "flip_to_front_outlined"
    FLOOD = "flood"
    FLOOD_SHARP = "flood_sharp"
    FLOOD_ROUNDED = "flood_rounded"
    FLOOD_OUTLINED = "flood_outlined"
    FLOURESCENT = "flourescent"
    FLOURESCENT_SHARP = "flourescent_sharp"
    FLOURESCENT_ROUNDED = "flourescent_rounded"
    FLOURESCENT_OUTLINED = "flourescent_outlined"
    FLUORESCENT = "fluorescent"
    FLUORESCENT_SHARP = "fluorescent_sharp"
    FLUORESCENT_ROUNDED = "fluorescent_rounded"
    FLUORESCENT_OUTLINED = "fluorescent_outlined"
    FLUTTER_DASH = "flutter_dash"
    FLUTTER_DASH_SHARP = "flutter_dash_sharp"
    FLUTTER_DASH_ROUNDED = "flutter_dash_rounded"
    FLUTTER_DASH_OUTLINED = "flutter_dash_outlined"
    FMD_BAD = "fmd_bad"
    FMD_BAD_SHARP = "fmd_bad_sharp"
    FMD_BAD_ROUNDED = "fmd_bad_rounded"
    FMD_BAD_OUTLINED = "fmd_bad_outlined"
    FMD_GOOD = "fmd_good"
    FMD_GOOD_SHARP = "fmd_good_sharp"
    FMD_GOOD_ROUNDED = "fmd_good_rounded"
    FMD_GOOD_OUTLINED = "fmd_good_outlined"
    FOGGY = "foggy"
    FOLDER = "folder"
    FOLDER_SHARP = "folder_sharp"
    FOLDER_ROUNDED = "folder_rounded"
    FOLDER_OUTLINED = "folder_outlined"
    FOLDER_COPY = "folder_copy"
    FOLDER_COPY_SHARP = "folder_copy_sharp"
    FOLDER_COPY_ROUNDED = "folder_copy_rounded"
    FOLDER_COPY_OUTLINED = "folder_copy_outlined"
    FOLDER_DELETE = "folder_delete"
    FOLDER_DELETE_SHARP = "folder_delete_sharp"
    FOLDER_DELETE_ROUNDED = "folder_delete_rounded"
    FOLDER_DELETE_OUTLINED = "folder_delete_outlined"
    FOLDER_OFF = "folder_off"
    FOLDER_OFF_SHARP = "folder_off_sharp"
    FOLDER_OFF_ROUNDED = "folder_off_rounded"
    FOLDER_OFF_OUTLINED = "folder_off_outlined"
    FOLDER_OPEN = "folder_open"
    FOLDER_OPEN_SHARP = "folder_open_sharp"
    FOLDER_OPEN_ROUNDED = "folder_open_rounded"
    FOLDER_OPEN_OUTLINED = "folder_open_outlined"
    FOLDER_SHARED = "folder_shared"
    FOLDER_SHARED_SHARP = "folder_shared_sharp"
    FOLDER_SHARED_ROUNDED = "folder_shared_rounded"
    FOLDER_SHARED_OUTLINED = "folder_shared_outlined"
    FOLDER_SPECIAL = "folder_special"
    FOLDER_SPECIAL_SHARP = "folder_special_sharp"
    FOLDER_SPECIAL_ROUNDED = "folder_special_rounded"
    FOLDER_SPECIAL_OUTLINED = "folder_special_outlined"
    FOLDER_ZIP = "folder_zip"
    FOLDER_ZIP_SHARP = "folder_zip_sharp"
    FOLDER_ZIP_ROUNDED = "folder_zip_rounded"
    FOLDER_ZIP_OUTLINED = "folder_zip_outlined"
    FOLLOW_THE_SIGNS = "follow_the_signs"
    FOLLOW_THE_SIGNS_SHARP = "follow_the_signs_sharp"
    FOLLOW_THE_SIGNS_ROUNDED = "follow_the_signs_rounded"
    FOLLOW_THE_SIGNS_OUTLINED = "follow_the_signs_outlined"
    FONT_DOWNLOAD = "font_download"
    FONT_DOWNLOAD_SHARP = "font_download_sharp"
    FONT_DOWNLOAD_ROUNDED = "font_download_rounded"
    FONT_DOWNLOAD_OUTLINED = "font_download_outlined"
    FONT_DOWNLOAD_OFF = "font_download_off"
    FONT_DOWNLOAD_OFF_SHARP = "font_download_off_sharp"
    FONT_DOWNLOAD_OFF_ROUNDED = "font_download_off_rounded"
    FONT_DOWNLOAD_OFF_OUTLINED = "font_download_off_outlined"
    FOOD_BANK = "food_bank"
    FOOD_BANK_SHARP = "food_bank_sharp"
    FOOD_BANK_ROUNDED = "food_bank_rounded"
    FOOD_BANK_OUTLINED = "food_bank_outlined"
    FOREST = "forest"
    FOREST_SHARP = "forest_sharp"
    FOREST_ROUNDED = "forest_rounded"
    FOREST_OUTLINED = "forest_outlined"
    FORK_LEFT = "fork_left"
    FORK_LEFT_SHARP = "fork_left_sharp"
    FORK_LEFT_ROUNDED = "fork_left_rounded"
    FORK_LEFT_OUTLINED = "fork_left_outlined"
    FORK_RIGHT = "fork_right"
    FORK_RIGHT_SHARP = "fork_right_sharp"
    FORK_RIGHT_ROUNDED = "fork_right_rounded"
    FORK_RIGHT_OUTLINED = "fork_right_outlined"
    FORKLIFT = "forklift"
    FORMAT_ALIGN_CENTER = "format_align_center"
    FORMAT_ALIGN_CENTER_SHARP = "format_align_center_sharp"
    FORMAT_ALIGN_CENTER_ROUNDED = "format_align_center_rounded"
    FORMAT_ALIGN_CENTER_OUTLINED = "format_align_center_outlined"
    FORMAT_ALIGN_JUSTIFY = "format_align_justify"
    FORMAT_ALIGN_JUSTIFY_SHARP = "format_align_justify_sharp"
    FORMAT_ALIGN_JUSTIFY_ROUNDED = "format_align_justify_rounded"
    FORMAT_ALIGN_JUSTIFY_OUTLINED = "format_align_justify_outlined"
    FORMAT_ALIGN_LEFT = "format_align_left"
    FORMAT_ALIGN_LEFT_SHARP = "format_align_left_sharp"
    FORMAT_ALIGN_LEFT_ROUNDED = "format_align_left_rounded"
    FORMAT_ALIGN_LEFT_OUTLINED = "format_align_left_outlined"
    FORMAT_ALIGN_RIGHT = "format_align_right"
    FORMAT_ALIGN_RIGHT_SHARP = "format_align_right_sharp"
    FORMAT_ALIGN_RIGHT_ROUNDED = "format_align_right_rounded"
    FORMAT_ALIGN_RIGHT_OUTLINED = "format_align_right_outlined"
    FORMAT_BOLD = "format_bold"
    FORMAT_BOLD_SHARP = "format_bold_sharp"
    FORMAT_BOLD_ROUNDED = "format_bold_rounded"
    FORMAT_BOLD_OUTLINED = "format_bold_outlined"
    FORMAT_CLEAR = "format_clear"
    FORMAT_CLEAR_SHARP = "format_clear_sharp"
    FORMAT_CLEAR_ROUNDED = "format_clear_rounded"
    FORMAT_CLEAR_OUTLINED = "format_clear_outlined"
    FORMAT_COLOR_FILL = "format_color_fill"
    FORMAT_COLOR_FILL_SHARP = "format_color_fill_sharp"
    FORMAT_COLOR_FILL_ROUNDED = "format_color_fill_rounded"
    FORMAT_COLOR_FILL_OUTLINED = "format_color_fill_outlined"
    FORMAT_COLOR_RESET = "format_color_reset"
    FORMAT_COLOR_RESET_SHARP = "format_color_reset_sharp"
    FORMAT_COLOR_RESET_ROUNDED = "format_color_reset_rounded"
    FORMAT_COLOR_RESET_OUTLINED = "format_color_reset_outlined"
    FORMAT_COLOR_TEXT = "format_color_text"
    FORMAT_COLOR_TEXT_SHARP = "format_color_text_sharp"
    FORMAT_COLOR_TEXT_ROUNDED = "format_color_text_rounded"
    FORMAT_COLOR_TEXT_OUTLINED = "format_color_text_outlined"
    FORMAT_INDENT_DECREASE = "format_indent_decrease"
    FORMAT_INDENT_DECREASE_SHARP = "format_indent_decrease_sharp"
    FORMAT_INDENT_DECREASE_ROUNDED = "format_indent_decrease_rounded"
    FORMAT_INDENT_DECREASE_OUTLINED = "format_indent_decrease_outlined"
    FORMAT_INDENT_INCREASE = "format_indent_increase"
    FORMAT_INDENT_INCREASE_SHARP = "format_indent_increase_sharp"
    FORMAT_INDENT_INCREASE_ROUNDED = "format_indent_increase_rounded"
    FORMAT_INDENT_INCREASE_OUTLINED = "format_indent_increase_outlined"
    FORMAT_ITALIC = "format_italic"
    FORMAT_ITALIC_SHARP = "format_italic_sharp"
    FORMAT_ITALIC_ROUNDED = "format_italic_rounded"
    FORMAT_ITALIC_OUTLINED = "format_italic_outlined"
    FORMAT_LINE_SPACING = "format_line_spacing"
    FORMAT_LINE_SPACING_SHARP = "format_line_spacing_sharp"
    FORMAT_LINE_SPACING_ROUNDED = "format_line_spacing_rounded"
    FORMAT_LINE_SPACING_OUTLINED = "format_line_spacing_outlined"
    FORMAT_LIST_BULLETED = "format_list_bulleted"
    FORMAT_LIST_BULLETED_SHARP = "format_list_bulleted_sharp"
    FORMAT_LIST_BULLETED_ROUNDED = "format_list_bulleted_rounded"
    FORMAT_LIST_BULLETED_OUTLINED = "format_list_bulleted_outlined"
    FORMAT_LIST_BULLETED_ADD = "format_list_bulleted_add"
    FORMAT_LIST_NUMBERED = "format_list_numbered"
    FORMAT_LIST_NUMBERED_SHARP = "format_list_numbered_sharp"
    FORMAT_LIST_NUMBERED_ROUNDED = "format_list_numbered_rounded"
    FORMAT_LIST_NUMBERED_OUTLINED = "format_list_numbered_outlined"
    FORMAT_LIST_NUMBERED_RTL = "format_list_numbered_rtl"
    FORMAT_LIST_NUMBERED_RTL_SHARP = "format_list_numbered_rtl_sharp"
    FORMAT_LIST_NUMBERED_RTL_ROUNDED = "format_list_numbered_rtl_rounded"
    FORMAT_LIST_NUMBERED_RTL_OUTLINED = "format_list_numbered_rtl_outlined"
    FORMAT_OVERLINE = "format_overline"
    FORMAT_OVERLINE_SHARP = "format_overline_sharp"
    FORMAT_OVERLINE_ROUNDED = "format_overline_rounded"
    FORMAT_OVERLINE_OUTLINED = "format_overline_outlined"
    FORMAT_PAINT = "format_paint"
    FORMAT_PAINT_SHARP = "format_paint_sharp"
    FORMAT_PAINT_ROUNDED = "format_paint_rounded"
    FORMAT_PAINT_OUTLINED = "format_paint_outlined"
    FORMAT_QUOTE = "format_quote"
    FORMAT_QUOTE_SHARP = "format_quote_sharp"
    FORMAT_QUOTE_ROUNDED = "format_quote_rounded"
    FORMAT_QUOTE_OUTLINED = "format_quote_outlined"
    FORMAT_SHAPES = "format_shapes"
    FORMAT_SHAPES_SHARP = "format_shapes_sharp"
    FORMAT_SHAPES_ROUNDED = "format_shapes_rounded"
    FORMAT_SHAPES_OUTLINED = "format_shapes_outlined"
    FORMAT_SIZE = "format_size"
    FORMAT_SIZE_SHARP = "format_size_sharp"
    FORMAT_SIZE_ROUNDED = "format_size_rounded"
    FORMAT_SIZE_OUTLINED = "format_size_outlined"
    FORMAT_STRIKETHROUGH = "format_strikethrough"
    FORMAT_STRIKETHROUGH_SHARP = "format_strikethrough_sharp"
    FORMAT_STRIKETHROUGH_ROUNDED = "format_strikethrough_rounded"
    FORMAT_STRIKETHROUGH_OUTLINED = "format_strikethrough_outlined"
    FORMAT_TEXTDIRECTION_L_TO_R = "format_textdirection_l_to_r"
    FORMAT_TEXTDIRECTION_L_TO_R_SHARP = "format_textdirection_l_to_r_sharp"
    FORMAT_TEXTDIRECTION_L_TO_R_ROUNDED = "format_textdirection_l_to_r_rounded"
    FORMAT_TEXTDIRECTION_L_TO_R_OUTLINED = "format_textdirection_l_to_r_outlined"
    FORMAT_TEXTDIRECTION_R_TO_L = "format_textdirection_r_to_l"
    FORMAT_TEXTDIRECTION_R_TO_L_SHARP = "format_textdirection_r_to_l_sharp"
    FORMAT_TEXTDIRECTION_R_TO_L_ROUNDED = "format_textdirection_r_to_l_rounded"
    FORMAT_TEXTDIRECTION_R_TO_L_OUTLINED = "format_textdirection_r_to_l_outlined"
    FORMAT_UNDERLINE = "format_underline"
    FORMAT_UNDERLINE_SHARP = "format_underline_sharp"
    FORMAT_UNDERLINE_ROUNDED = "format_underline_rounded"
    FORMAT_UNDERLINE_OUTLINED = "format_underline_outlined"
    FORMAT_UNDERLINED = "format_underlined"
    FORMAT_UNDERLINED_SHARP = "format_underlined_sharp"
    FORMAT_UNDERLINED_ROUNDED = "format_underlined_rounded"
    FORMAT_UNDERLINED_OUTLINED = "format_underlined_outlined"
    FORT = "fort"
    FORT_SHARP = "fort_sharp"
    FORT_ROUNDED = "fort_rounded"
    FORT_OUTLINED = "fort_outlined"
    FORUM = "forum"
    FORUM_SHARP = "forum_sharp"
    FORUM_ROUNDED = "forum_rounded"
    FORUM_OUTLINED = "forum_outlined"
    FORWARD = "forward"
    FORWARD_SHARP = "forward_sharp"
    FORWARD_ROUNDED = "forward_rounded"
    FORWARD_OUTLINED = "forward_outlined"
    FORWARD_10 = "forward_10"
    FORWARD_10_SHARP = "forward_10_sharp"
    FORWARD_10_ROUNDED = "forward_10_rounded"
    FORWARD_10_OUTLINED = "forward_10_outlined"
    FORWARD_30 = "forward_30"
    FORWARD_30_SHARP = "forward_30_sharp"
    FORWARD_30_ROUNDED = "forward_30_rounded"
    FORWARD_30_OUTLINED = "forward_30_outlined"
    FORWARD_5 = "forward_5"
    FORWARD_5_SHARP = "forward_5_sharp"
    FORWARD_5_ROUNDED = "forward_5_rounded"
    FORWARD_5_OUTLINED = "forward_5_outlined"
    FORWARD_TO_INBOX = "forward_to_inbox"
    FORWARD_TO_INBOX_SHARP = "forward_to_inbox_sharp"
    FORWARD_TO_INBOX_ROUNDED = "forward_to_inbox_rounded"
    FORWARD_TO_INBOX_OUTLINED = "forward_to_inbox_outlined"
    FOUNDATION = "foundation"
    FOUNDATION_SHARP = "foundation_sharp"
    FOUNDATION_ROUNDED = "foundation_rounded"
    FOUNDATION_OUTLINED = "foundation_outlined"
    FREE_BREAKFAST = "free_breakfast"
    FREE_BREAKFAST_SHARP = "free_breakfast_sharp"
    FREE_BREAKFAST_ROUNDED = "free_breakfast_rounded"
    FREE_BREAKFAST_OUTLINED = "free_breakfast_outlined"
    FREE_CANCELLATION = "free_cancellation"
    FREE_CANCELLATION_SHARP = "free_cancellation_sharp"
    FREE_CANCELLATION_ROUNDED = "free_cancellation_rounded"
    FREE_CANCELLATION_OUTLINED = "free_cancellation_outlined"
    FRONT_HAND = "front_hand"
    FRONT_HAND_SHARP = "front_hand_sharp"
    FRONT_HAND_ROUNDED = "front_hand_rounded"
    FRONT_HAND_OUTLINED = "front_hand_outlined"
    FRONT_LOADER = "front_loader"
    FULLSCREEN = "fullscreen"
    FULLSCREEN_SHARP = "fullscreen_sharp"
    FULLSCREEN_ROUNDED = "fullscreen_rounded"
    FULLSCREEN_OUTLINED = "fullscreen_outlined"
    FULLSCREEN_EXIT = "fullscreen_exit"
    FULLSCREEN_EXIT_SHARP = "fullscreen_exit_sharp"
    FULLSCREEN_EXIT_ROUNDED = "fullscreen_exit_rounded"
    FULLSCREEN_EXIT_OUTLINED = "fullscreen_exit_outlined"
    FUNCTIONS = "functions"
    FUNCTIONS_SHARP = "functions_sharp"
    FUNCTIONS_ROUNDED = "functions_rounded"
    FUNCTIONS_OUTLINED = "functions_outlined"
    G_MOBILEDATA = "g_mobiledata"
    G_MOBILEDATA_SHARP = "g_mobiledata_sharp"
    G_MOBILEDATA_ROUNDED = "g_mobiledata_rounded"
    G_MOBILEDATA_OUTLINED = "g_mobiledata_outlined"
    G_TRANSLATE = "g_translate"
    G_TRANSLATE_SHARP = "g_translate_sharp"
    G_TRANSLATE_ROUNDED = "g_translate_rounded"
    G_TRANSLATE_OUTLINED = "g_translate_outlined"
    GAMEPAD = "gamepad"
    GAMEPAD_SHARP = "gamepad_sharp"
    GAMEPAD_ROUNDED = "gamepad_rounded"
    GAMEPAD_OUTLINED = "gamepad_outlined"
    GAMES = "games"
    GAMES_SHARP = "games_sharp"
    GAMES_ROUNDED = "games_rounded"
    GAMES_OUTLINED = "games_outlined"
    GARAGE = "garage"
    GARAGE_SHARP = "garage_sharp"
    GARAGE_ROUNDED = "garage_rounded"
    GARAGE_OUTLINED = "garage_outlined"
    GAS_METER = "gas_meter"
    GAS_METER_SHARP = "gas_meter_sharp"
    GAS_METER_ROUNDED = "gas_meter_rounded"
    GAS_METER_OUTLINED = "gas_meter_outlined"
    GAVEL = "gavel"
    GAVEL_SHARP = "gavel_sharp"
    GAVEL_ROUNDED = "gavel_rounded"
    GAVEL_OUTLINED = "gavel_outlined"
    GENERATING_TOKENS = "generating_tokens"
    GENERATING_TOKENS_SHARP = "generating_tokens_sharp"
    GENERATING_TOKENS_ROUNDED = "generating_tokens_rounded"
    GENERATING_TOKENS_OUTLINED = "generating_tokens_outlined"
    GESTURE = "gesture"
    GESTURE_SHARP = "gesture_sharp"
    GESTURE_ROUNDED = "gesture_rounded"
    GESTURE_OUTLINED = "gesture_outlined"
    GET_APP = "get_app"
    GET_APP_SHARP = "get_app_sharp"
    GET_APP_ROUNDED = "get_app_rounded"
    GET_APP_OUTLINED = "get_app_outlined"
    GIF = "gif"
    GIF_SHARP = "gif_sharp"
    GIF_ROUNDED = "gif_rounded"
    GIF_OUTLINED = "gif_outlined"
    GIF_BOX = "gif_box"
    GIF_BOX_SHARP = "gif_box_sharp"
    GIF_BOX_ROUNDED = "gif_box_rounded"
    GIF_BOX_OUTLINED = "gif_box_outlined"
    GIRL = "girl"
    GIRL_SHARP = "girl_sharp"
    GIRL_ROUNDED = "girl_rounded"
    GIRL_OUTLINED = "girl_outlined"
    GITE = "gite"
    GITE_SHARP = "gite_sharp"
    GITE_ROUNDED = "gite_rounded"
    GITE_OUTLINED = "gite_outlined"
    GOLF_COURSE = "golf_course"
    GOLF_COURSE_SHARP = "golf_course_sharp"
    GOLF_COURSE_ROUNDED = "golf_course_rounded"
    GOLF_COURSE_OUTLINED = "golf_course_outlined"
    GPP_BAD = "gpp_bad"
    GPP_BAD_SHARP = "gpp_bad_sharp"
    GPP_BAD_ROUNDED = "gpp_bad_rounded"
    GPP_BAD_OUTLINED = "gpp_bad_outlined"
    GPP_GOOD = "gpp_good"
    GPP_GOOD_SHARP = "gpp_good_sharp"
    GPP_GOOD_ROUNDED = "gpp_good_rounded"
    GPP_GOOD_OUTLINED = "gpp_good_outlined"
    GPP_MAYBE = "gpp_maybe"
    GPP_MAYBE_SHARP = "gpp_maybe_sharp"
    GPP_MAYBE_ROUNDED = "gpp_maybe_rounded"
    GPP_MAYBE_OUTLINED = "gpp_maybe_outlined"
    GPS_FIXED = "gps_fixed"
    GPS_FIXED_SHARP = "gps_fixed_sharp"
    GPS_FIXED_ROUNDED = "gps_fixed_rounded"
    GPS_FIXED_OUTLINED = "gps_fixed_outlined"
    GPS_NOT_FIXED = "gps_not_fixed"
    GPS_NOT_FIXED_SHARP = "gps_not_fixed_sharp"
    GPS_NOT_FIXED_ROUNDED = "gps_not_fixed_rounded"
    GPS_NOT_FIXED_OUTLINED = "gps_not_fixed_outlined"
    GPS_OFF = "gps_off"
    GPS_OFF_SHARP = "gps_off_sharp"
    GPS_OFF_ROUNDED = "gps_off_rounded"
    GPS_OFF_OUTLINED = "gps_off_outlined"
    GRADE = "grade"
    GRADE_SHARP = "grade_sharp"
    GRADE_ROUNDED = "grade_rounded"
    GRADE_OUTLINED = "grade_outlined"
    GRADIENT = "gradient"
    GRADIENT_SHARP = "gradient_sharp"
    GRADIENT_ROUNDED = "gradient_rounded"
    GRADIENT_OUTLINED = "gradient_outlined"
    GRADING = "grading"
    GRADING_SHARP = "grading_sharp"
    GRADING_ROUNDED = "grading_rounded"
    GRADING_OUTLINED = "grading_outlined"
    GRAIN = "grain"
    GRAIN_SHARP = "grain_sharp"
    GRAIN_ROUNDED = "grain_rounded"
    GRAIN_OUTLINED = "grain_outlined"
    GRAPHIC_EQ = "graphic_eq"
    GRAPHIC_EQ_SHARP = "graphic_eq_sharp"
    GRAPHIC_EQ_ROUNDED = "graphic_eq_rounded"
    GRAPHIC_EQ_OUTLINED = "graphic_eq_outlined"
    GRASS = "grass"
    GRASS_SHARP = "grass_sharp"
    GRASS_ROUNDED = "grass_rounded"
    GRASS_OUTLINED = "grass_outlined"
    GRID_3X3 = "grid_3x3"
    GRID_3X3_SHARP = "grid_3x3_sharp"
    GRID_3X3_ROUNDED = "grid_3x3_rounded"
    GRID_3X3_OUTLINED = "grid_3x3_outlined"
    GRID_4X4 = "grid_4x4"
    GRID_4X4_SHARP = "grid_4x4_sharp"
    GRID_4X4_ROUNDED = "grid_4x4_rounded"
    GRID_4X4_OUTLINED = "grid_4x4_outlined"
    GRID_GOLDENRATIO = "grid_goldenratio"
    GRID_GOLDENRATIO_SHARP = "grid_goldenratio_sharp"
    GRID_GOLDENRATIO_ROUNDED = "grid_goldenratio_rounded"
    GRID_GOLDENRATIO_OUTLINED = "grid_goldenratio_outlined"
    GRID_OFF = "grid_off"
    GRID_OFF_SHARP = "grid_off_sharp"
    GRID_OFF_ROUNDED = "grid_off_rounded"
    GRID_OFF_OUTLINED = "grid_off_outlined"
    GRID_ON = "grid_on"
    GRID_ON_SHARP = "grid_on_sharp"
    GRID_ON_ROUNDED = "grid_on_rounded"
    GRID_ON_OUTLINED = "grid_on_outlined"
    GRID_VIEW = "grid_view"
    GRID_VIEW_SHARP = "grid_view_sharp"
    GRID_VIEW_ROUNDED = "grid_view_rounded"
    GRID_VIEW_OUTLINED = "grid_view_outlined"
    GROUP = "group"
    GROUP_SHARP = "group_sharp"
    GROUP_ROUNDED = "group_rounded"
    GROUP_OUTLINED = "group_outlined"
    GROUP_ADD = "group_add"
    GROUP_ADD_SHARP = "group_add_sharp"
    GROUP_ADD_ROUNDED = "group_add_rounded"
    GROUP_ADD_OUTLINED = "group_add_outlined"
    GROUP_OFF = "group_off"
    GROUP_OFF_SHARP = "group_off_sharp"
    GROUP_OFF_ROUNDED = "group_off_rounded"
    GROUP_OFF_OUTLINED = "group_off_outlined"
    GROUP_REMOVE = "group_remove"
    GROUP_REMOVE_SHARP = "group_remove_sharp"
    GROUP_REMOVE_ROUNDED = "group_remove_rounded"
    GROUP_REMOVE_OUTLINED = "group_remove_outlined"
    GROUP_WORK = "group_work"
    GROUP_WORK_SHARP = "group_work_sharp"
    GROUP_WORK_ROUNDED = "group_work_rounded"
    GROUP_WORK_OUTLINED = "group_work_outlined"
    GROUPS = "groups"
    GROUPS_SHARP = "groups_sharp"
    GROUPS_ROUNDED = "groups_rounded"
    GROUPS_OUTLINED = "groups_outlined"
    GROUPS_2 = "groups_2"
    GROUPS_2_SHARP = "groups_2_sharp"
    GROUPS_2_ROUNDED = "groups_2_rounded"
    GROUPS_2_OUTLINED = "groups_2_outlined"
    GROUPS_3 = "groups_3"
    GROUPS_3_SHARP = "groups_3_sharp"
    GROUPS_3_ROUNDED = "groups_3_rounded"
    GROUPS_3_OUTLINED = "groups_3_outlined"
    H_MOBILEDATA = "h_mobiledata"
    H_MOBILEDATA_SHARP = "h_mobiledata_sharp"
    H_MOBILEDATA_ROUNDED = "h_mobiledata_rounded"
    H_MOBILEDATA_OUTLINED = "h_mobiledata_outlined"
    H_PLUS_MOBILEDATA = "h_plus_mobiledata"
    H_PLUS_MOBILEDATA_SHARP = "h_plus_mobiledata_sharp"
    H_PLUS_MOBILEDATA_ROUNDED = "h_plus_mobiledata_rounded"
    H_PLUS_MOBILEDATA_OUTLINED = "h_plus_mobiledata_outlined"
    HAIL = "hail"
    HAIL_SHARP = "hail_sharp"
    HAIL_ROUNDED = "hail_rounded"
    HAIL_OUTLINED = "hail_outlined"
    HANDSHAKE = "handshake"
    HANDSHAKE_SHARP = "handshake_sharp"
    HANDSHAKE_ROUNDED = "handshake_rounded"
    HANDSHAKE_OUTLINED = "handshake_outlined"
    HANDYMAN = "handyman"
    HANDYMAN_SHARP = "handyman_sharp"
    HANDYMAN_ROUNDED = "handyman_rounded"
    HANDYMAN_OUTLINED = "handyman_outlined"
    HARDWARE = "hardware"
    HARDWARE_SHARP = "hardware_sharp"
    HARDWARE_ROUNDED = "hardware_rounded"
    HARDWARE_OUTLINED = "hardware_outlined"
    HD = "hd"
    HD_SHARP = "hd_sharp"
    HD_ROUNDED = "hd_rounded"
    HD_OUTLINED = "hd_outlined"
    HDR_AUTO = "hdr_auto"
    HDR_AUTO_SHARP = "hdr_auto_sharp"
    HDR_AUTO_ROUNDED = "hdr_auto_rounded"
    HDR_AUTO_OUTLINED = "hdr_auto_outlined"
    HDR_AUTO_SELECT = "hdr_auto_select"
    HDR_AUTO_SELECT_SHARP = "hdr_auto_select_sharp"
    HDR_AUTO_SELECT_ROUNDED = "hdr_auto_select_rounded"
    HDR_AUTO_SELECT_OUTLINED = "hdr_auto_select_outlined"
    HDR_ENHANCED_SELECT = "hdr_enhanced_select"
    HDR_ENHANCED_SELECT_SHARP = "hdr_enhanced_select_sharp"
    HDR_ENHANCED_SELECT_ROUNDED = "hdr_enhanced_select_rounded"
    HDR_ENHANCED_SELECT_OUTLINED = "hdr_enhanced_select_outlined"
    HDR_OFF = "hdr_off"
    HDR_OFF_SHARP = "hdr_off_sharp"
    HDR_OFF_ROUNDED = "hdr_off_rounded"
    HDR_OFF_OUTLINED = "hdr_off_outlined"
    HDR_OFF_SELECT = "hdr_off_select"
    HDR_OFF_SELECT_SHARP = "hdr_off_select_sharp"
    HDR_OFF_SELECT_ROUNDED = "hdr_off_select_rounded"
    HDR_OFF_SELECT_OUTLINED = "hdr_off_select_outlined"
    HDR_ON = "hdr_on"
    HDR_ON_SHARP = "hdr_on_sharp"
    HDR_ON_ROUNDED = "hdr_on_rounded"
    HDR_ON_OUTLINED = "hdr_on_outlined"
    HDR_ON_SELECT = "hdr_on_select"
    HDR_ON_SELECT_SHARP = "hdr_on_select_sharp"
    HDR_ON_SELECT_ROUNDED = "hdr_on_select_rounded"
    HDR_ON_SELECT_OUTLINED = "hdr_on_select_outlined"
    HDR_PLUS = "hdr_plus"
    HDR_PLUS_SHARP = "hdr_plus_sharp"
    HDR_PLUS_ROUNDED = "hdr_plus_rounded"
    HDR_PLUS_OUTLINED = "hdr_plus_outlined"
    HDR_STRONG = "hdr_strong"
    HDR_STRONG_SHARP = "hdr_strong_sharp"
    HDR_STRONG_ROUNDED = "hdr_strong_rounded"
    HDR_STRONG_OUTLINED = "hdr_strong_outlined"
    HDR_WEAK = "hdr_weak"
    HDR_WEAK_SHARP = "hdr_weak_sharp"
    HDR_WEAK_ROUNDED = "hdr_weak_rounded"
    HDR_WEAK_OUTLINED = "hdr_weak_outlined"
    HEADPHONES = "headphones"
    HEADPHONES_SHARP = "headphones_sharp"
    HEADPHONES_ROUNDED = "headphones_rounded"
    HEADPHONES_OUTLINED = "headphones_outlined"
    HEADPHONES_BATTERY = "headphones_battery"
    HEADPHONES_BATTERY_SHARP = "headphones_battery_sharp"
    HEADPHONES_BATTERY_ROUNDED = "headphones_battery_rounded"
    HEADPHONES_BATTERY_OUTLINED = "headphones_battery_outlined"
    HEADSET = "headset"
    HEADSET_SHARP = "headset_sharp"
    HEADSET_ROUNDED = "headset_rounded"
    HEADSET_OUTLINED = "headset_outlined"
    HEADSET_MIC = "headset_mic"
    HEADSET_MIC_SHARP = "headset_mic_sharp"
    HEADSET_MIC_ROUNDED = "headset_mic_rounded"
    HEADSET_MIC_OUTLINED = "headset_mic_outlined"
    HEADSET_OFF = "headset_off"
    HEADSET_OFF_SHARP = "headset_off_sharp"
    HEADSET_OFF_ROUNDED = "headset_off_rounded"
    HEADSET_OFF_OUTLINED = "headset_off_outlined"
    HEALING = "healing"
    HEALING_SHARP = "healing_sharp"
    HEALING_ROUNDED = "healing_rounded"
    HEALING_OUTLINED = "healing_outlined"
    HEALTH_AND_SAFETY = "health_and_safety"
    HEALTH_AND_SAFETY_SHARP = "health_and_safety_sharp"
    HEALTH_AND_SAFETY_ROUNDED = "health_and_safety_rounded"
    HEALTH_AND_SAFETY_OUTLINED = "health_and_safety_outlined"
    HEARING = "hearing"
    HEARING_SHARP = "hearing_sharp"
    HEARING_ROUNDED = "hearing_rounded"
    HEARING_OUTLINED = "hearing_outlined"
    HEARING_DISABLED = "hearing_disabled"
    HEARING_DISABLED_SHARP = "hearing_disabled_sharp"
    HEARING_DISABLED_ROUNDED = "hearing_disabled_rounded"
    HEARING_DISABLED_OUTLINED = "hearing_disabled_outlined"
    HEART_BROKEN = "heart_broken"
    HEART_BROKEN_SHARP = "heart_broken_sharp"
    HEART_BROKEN_ROUNDED = "heart_broken_rounded"
    HEART_BROKEN_OUTLINED = "heart_broken_outlined"
    HEAT_PUMP = "heat_pump"
    HEAT_PUMP_SHARP = "heat_pump_sharp"
    HEAT_PUMP_ROUNDED = "heat_pump_rounded"
    HEAT_PUMP_OUTLINED = "heat_pump_outlined"
    HEIGHT = "height"
    HEIGHT_SHARP = "height_sharp"
    HEIGHT_ROUNDED = "height_rounded"
    HEIGHT_OUTLINED = "height_outlined"
    HELP = "help"
    HELP_SHARP = "help_sharp"
    HELP_ROUNDED = "help_rounded"
    HELP_OUTLINED = "help_outlined"
    HELP_CENTER = "help_center"
    HELP_CENTER_SHARP = "help_center_sharp"
    HELP_CENTER_ROUNDED = "help_center_rounded"
    HELP_CENTER_OUTLINED = "help_center_outlined"
    HELP_OUTLINE = "help_outline"
    HELP_OUTLINE_SHARP = "help_outline_sharp"
    HELP_OUTLINE_ROUNDED = "help_outline_rounded"
    HELP_OUTLINE_OUTLINED = "help_outline_outlined"
    HEVC = "hevc"
    HEVC_SHARP = "hevc_sharp"
    HEVC_ROUNDED = "hevc_rounded"
    HEVC_OUTLINED = "hevc_outlined"
    HEXAGON = "hexagon"
    HEXAGON_SHARP = "hexagon_sharp"
    HEXAGON_ROUNDED = "hexagon_rounded"
    HEXAGON_OUTLINED = "hexagon_outlined"
    HIDE_IMAGE = "hide_image"
    HIDE_IMAGE_SHARP = "hide_image_sharp"
    HIDE_IMAGE_ROUNDED = "hide_image_rounded"
    HIDE_IMAGE_OUTLINED = "hide_image_outlined"
    HIDE_SOURCE = "hide_source"
    HIDE_SOURCE_SHARP = "hide_source_sharp"
    HIDE_SOURCE_ROUNDED = "hide_source_rounded"
    HIDE_SOURCE_OUTLINED = "hide_source_outlined"
    HIGH_QUALITY = "high_quality"
    HIGH_QUALITY_SHARP = "high_quality_sharp"
    HIGH_QUALITY_ROUNDED = "high_quality_rounded"
    HIGH_QUALITY_OUTLINED = "high_quality_outlined"
    HIGHLIGHT = "highlight"
    HIGHLIGHT_SHARP = "highlight_sharp"
    HIGHLIGHT_ROUNDED = "highlight_rounded"
    HIGHLIGHT_OUTLINED = "highlight_outlined"
    HIGHLIGHT_ALT = "highlight_alt"
    HIGHLIGHT_ALT_SHARP = "highlight_alt_sharp"
    HIGHLIGHT_ALT_ROUNDED = "highlight_alt_rounded"
    HIGHLIGHT_ALT_OUTLINED = "highlight_alt_outlined"
    HIGHLIGHT_OFF = "highlight_off"
    HIGHLIGHT_OFF_SHARP = "highlight_off_sharp"
    HIGHLIGHT_OFF_ROUNDED = "highlight_off_rounded"
    HIGHLIGHT_OFF_OUTLINED = "highlight_off_outlined"
    HIGHLIGHT_REMOVE = "highlight_remove"
    HIGHLIGHT_REMOVE_SHARP = "highlight_remove_sharp"
    HIGHLIGHT_REMOVE_ROUNDED = "highlight_remove_rounded"
    HIGHLIGHT_REMOVE_OUTLINED = "highlight_remove_outlined"
    HIKING = "hiking"
    HIKING_SHARP = "hiking_sharp"
    HIKING_ROUNDED = "hiking_rounded"
    HIKING_OUTLINED = "hiking_outlined"
    HISTORY = "history"
    HISTORY_SHARP = "history_sharp"
    HISTORY_ROUNDED = "history_rounded"
    HISTORY_OUTLINED = "history_outlined"
    HISTORY_EDU = "history_edu"
    HISTORY_EDU_SHARP = "history_edu_sharp"
    HISTORY_EDU_ROUNDED = "history_edu_rounded"
    HISTORY_EDU_OUTLINED = "history_edu_outlined"
    HISTORY_TOGGLE_OFF = "history_toggle_off"
    HISTORY_TOGGLE_OFF_SHARP = "history_toggle_off_sharp"
    HISTORY_TOGGLE_OFF_ROUNDED = "history_toggle_off_rounded"
    HISTORY_TOGGLE_OFF_OUTLINED = "history_toggle_off_outlined"
    HIVE = "hive"
    HIVE_SHARP = "hive_sharp"
    HIVE_ROUNDED = "hive_rounded"
    HIVE_OUTLINED = "hive_outlined"
    HLS = "hls"
    HLS_SHARP = "hls_sharp"
    HLS_ROUNDED = "hls_rounded"
    HLS_OUTLINED = "hls_outlined"
    HLS_OFF = "hls_off"
    HLS_OFF_SHARP = "hls_off_sharp"
    HLS_OFF_ROUNDED = "hls_off_rounded"
    HLS_OFF_OUTLINED = "hls_off_outlined"
    HOLIDAY_VILLAGE = "holiday_village"
    HOLIDAY_VILLAGE_SHARP = "holiday_village_sharp"
    HOLIDAY_VILLAGE_ROUNDED = "holiday_village_rounded"
    HOLIDAY_VILLAGE_OUTLINED = "holiday_village_outlined"
    HOME = "home"
    HOME_SHARP = "home_sharp"
    HOME_ROUNDED = "home_rounded"
    HOME_OUTLINED = "home_outlined"
    HOME_FILLED = "home_filled"
    HOME_MAX = "home_max"
    HOME_MAX_SHARP = "home_max_sharp"
    HOME_MAX_ROUNDED = "home_max_rounded"
    HOME_MAX_OUTLINED = "home_max_outlined"
    HOME_MINI = "home_mini"
    HOME_MINI_SHARP = "home_mini_sharp"
    HOME_MINI_ROUNDED = "home_mini_rounded"
    HOME_MINI_OUTLINED = "home_mini_outlined"
    HOME_REPAIR_SERVICE = "home_repair_service"
    HOME_REPAIR_SERVICE_SHARP = "home_repair_service_sharp"
    HOME_REPAIR_SERVICE_ROUNDED = "home_repair_service_rounded"
    HOME_REPAIR_SERVICE_OUTLINED = "home_repair_service_outlined"
    HOME_WORK = "home_work"
    HOME_WORK_SHARP = "home_work_sharp"
    HOME_WORK_ROUNDED = "home_work_rounded"
    HOME_WORK_OUTLINED = "home_work_outlined"
    HORIZONTAL_DISTRIBUTE = "horizontal_distribute"
    HORIZONTAL_DISTRIBUTE_SHARP = "horizontal_distribute_sharp"
    HORIZONTAL_DISTRIBUTE_ROUNDED = "horizontal_distribute_rounded"
    HORIZONTAL_DISTRIBUTE_OUTLINED = "horizontal_distribute_outlined"
    HORIZONTAL_RULE = "horizontal_rule"
    HORIZONTAL_RULE_SHARP = "horizontal_rule_sharp"
    HORIZONTAL_RULE_ROUNDED = "horizontal_rule_rounded"
    HORIZONTAL_RULE_OUTLINED = "horizontal_rule_outlined"
    HORIZONTAL_SPLIT = "horizontal_split"
    HORIZONTAL_SPLIT_SHARP = "horizontal_split_sharp"
    HORIZONTAL_SPLIT_ROUNDED = "horizontal_split_rounded"
    HORIZONTAL_SPLIT_OUTLINED = "horizontal_split_outlined"
    HOT_TUB = "hot_tub"
    HOT_TUB_SHARP = "hot_tub_sharp"
    HOT_TUB_ROUNDED = "hot_tub_rounded"
    HOT_TUB_OUTLINED = "hot_tub_outlined"
    HOTEL = "hotel"
    HOTEL_SHARP = "hotel_sharp"
    HOTEL_ROUNDED = "hotel_rounded"
    HOTEL_OUTLINED = "hotel_outlined"
    HOTEL_CLASS = "hotel_class"
    HOTEL_CLASS_SHARP = "hotel_class_sharp"
    HOTEL_CLASS_ROUNDED = "hotel_class_rounded"
    HOTEL_CLASS_OUTLINED = "hotel_class_outlined"
    HOURGLASS_BOTTOM = "hourglass_bottom"
    HOURGLASS_BOTTOM_SHARP = "hourglass_bottom_sharp"
    HOURGLASS_BOTTOM_ROUNDED = "hourglass_bottom_rounded"
    HOURGLASS_BOTTOM_OUTLINED = "hourglass_bottom_outlined"
    HOURGLASS_DISABLED = "hourglass_disabled"
    HOURGLASS_DISABLED_SHARP = "hourglass_disabled_sharp"
    HOURGLASS_DISABLED_ROUNDED = "hourglass_disabled_rounded"
    HOURGLASS_DISABLED_OUTLINED = "hourglass_disabled_outlined"
    HOURGLASS_EMPTY = "hourglass_empty"
    HOURGLASS_EMPTY_SHARP = "hourglass_empty_sharp"
    HOURGLASS_EMPTY_ROUNDED = "hourglass_empty_rounded"
    HOURGLASS_EMPTY_OUTLINED = "hourglass_empty_outlined"
    HOURGLASS_FULL = "hourglass_full"
    HOURGLASS_FULL_SHARP = "hourglass_full_sharp"
    HOURGLASS_FULL_ROUNDED = "hourglass_full_rounded"
    HOURGLASS_FULL_OUTLINED = "hourglass_full_outlined"
    HOURGLASS_TOP = "hourglass_top"
    HOURGLASS_TOP_SHARP = "hourglass_top_sharp"
    HOURGLASS_TOP_ROUNDED = "hourglass_top_rounded"
    HOURGLASS_TOP_OUTLINED = "hourglass_top_outlined"
    HOUSE = "house"
    HOUSE_SHARP = "house_sharp"
    HOUSE_ROUNDED = "house_rounded"
    HOUSE_OUTLINED = "house_outlined"
    HOUSE_SIDING = "house_siding"
    HOUSE_SIDING_SHARP = "house_siding_sharp"
    HOUSE_SIDING_ROUNDED = "house_siding_rounded"
    HOUSE_SIDING_OUTLINED = "house_siding_outlined"
    HOUSEBOAT = "houseboat"
    HOUSEBOAT_SHARP = "houseboat_sharp"
    HOUSEBOAT_ROUNDED = "houseboat_rounded"
    HOUSEBOAT_OUTLINED = "houseboat_outlined"
    HOW_TO_REG = "how_to_reg"
    HOW_TO_REG_SHARP = "how_to_reg_sharp"
    HOW_TO_REG_ROUNDED = "how_to_reg_rounded"
    HOW_TO_REG_OUTLINED = "how_to_reg_outlined"
    HOW_TO_VOTE = "how_to_vote"
    HOW_TO_VOTE_SHARP = "how_to_vote_sharp"
    HOW_TO_VOTE_ROUNDED = "how_to_vote_rounded"
    HOW_TO_VOTE_OUTLINED = "how_to_vote_outlined"
    HTML = "html"
    HTML_SHARP = "html_sharp"
    HTML_ROUNDED = "html_rounded"
    HTML_OUTLINED = "html_outlined"
    HTTP = "http"
    HTTP_SHARP = "http_sharp"
    HTTP_ROUNDED = "http_rounded"
    HTTP_OUTLINED = "http_outlined"
    HTTPS = "https"
    HTTPS_SHARP = "https_sharp"
    HTTPS_ROUNDED = "https_rounded"
    HTTPS_OUTLINED = "https_outlined"
    HUB = "hub"
    HUB_SHARP = "hub_sharp"
    HUB_ROUNDED = "hub_rounded"
    HUB_OUTLINED = "hub_outlined"
    HVAC = "hvac"
    HVAC_SHARP = "hvac_sharp"
    HVAC_ROUNDED = "hvac_rounded"
    HVAC_OUTLINED = "hvac_outlined"
    ICE_SKATING = "ice_skating"
    ICE_SKATING_SHARP = "ice_skating_sharp"
    ICE_SKATING_ROUNDED = "ice_skating_rounded"
    ICE_SKATING_OUTLINED = "ice_skating_outlined"
    ICECREAM = "icecream"
    ICECREAM_SHARP = "icecream_sharp"
    ICECREAM_ROUNDED = "icecream_rounded"
    ICECREAM_OUTLINED = "icecream_outlined"
    IMAGE = "image"
    IMAGE_SHARP = "image_sharp"
    IMAGE_ROUNDED = "image_rounded"
    IMAGE_OUTLINED = "image_outlined"
    IMAGE_ASPECT_RATIO = "image_aspect_ratio"
    IMAGE_ASPECT_RATIO_SHARP = "image_aspect_ratio_sharp"
    IMAGE_ASPECT_RATIO_ROUNDED = "image_aspect_ratio_rounded"
    IMAGE_ASPECT_RATIO_OUTLINED = "image_aspect_ratio_outlined"
    IMAGE_NOT_SUPPORTED = "image_not_supported"
    IMAGE_NOT_SUPPORTED_SHARP = "image_not_supported_sharp"
    IMAGE_NOT_SUPPORTED_ROUNDED = "image_not_supported_rounded"
    IMAGE_NOT_SUPPORTED_OUTLINED = "image_not_supported_outlined"
    IMAGE_SEARCH = "image_search"
    IMAGE_SEARCH_SHARP = "image_search_sharp"
    IMAGE_SEARCH_ROUNDED = "image_search_rounded"
    IMAGE_SEARCH_OUTLINED = "image_search_outlined"
    IMAGESEARCH_ROLLER = "imagesearch_roller"
    IMAGESEARCH_ROLLER_SHARP = "imagesearch_roller_sharp"
    IMAGESEARCH_ROLLER_ROUNDED = "imagesearch_roller_rounded"
    IMAGESEARCH_ROLLER_OUTLINED = "imagesearch_roller_outlined"
    IMPORT_CONTACTS = "import_contacts"
    IMPORT_CONTACTS_SHARP = "import_contacts_sharp"
    IMPORT_CONTACTS_ROUNDED = "import_contacts_rounded"
    IMPORT_CONTACTS_OUTLINED = "import_contacts_outlined"
    IMPORT_EXPORT = "import_export"
    IMPORT_EXPORT_SHARP = "import_export_sharp"
    IMPORT_EXPORT_ROUNDED = "import_export_rounded"
    IMPORT_EXPORT_OUTLINED = "import_export_outlined"
    IMPORTANT_DEVICES = "important_devices"
    IMPORTANT_DEVICES_SHARP = "important_devices_sharp"
    IMPORTANT_DEVICES_ROUNDED = "important_devices_rounded"
    IMPORTANT_DEVICES_OUTLINED = "important_devices_outlined"
    INBOX = "inbox"
    INBOX_SHARP = "inbox_sharp"
    INBOX_ROUNDED = "inbox_rounded"
    INBOX_OUTLINED = "inbox_outlined"
    INCOMPLETE_CIRCLE = "incomplete_circle"
    INCOMPLETE_CIRCLE_SHARP = "incomplete_circle_sharp"
    INCOMPLETE_CIRCLE_ROUNDED = "incomplete_circle_rounded"
    INCOMPLETE_CIRCLE_OUTLINED = "incomplete_circle_outlined"
    INDETERMINATE_CHECK_BOX = "indeterminate_check_box"
    INDETERMINATE_CHECK_BOX_SHARP = "indeterminate_check_box_sharp"
    INDETERMINATE_CHECK_BOX_ROUNDED = "indeterminate_check_box_rounded"
    INDETERMINATE_CHECK_BOX_OUTLINED = "indeterminate_check_box_outlined"
    INFO = "info"
    INFO_SHARP = "info_sharp"
    INFO_ROUNDED = "info_rounded"
    INFO_OUTLINED = "info_outlined"
    INFO_OUTLINE = "info_outline"
    INFO_OUTLINE_SHARP = "info_outline_sharp"
    INFO_OUTLINE_ROUNDED = "info_outline_rounded"
    INPUT = "input"
    INPUT_SHARP = "input_sharp"
    INPUT_ROUNDED = "input_rounded"
    INPUT_OUTLINED = "input_outlined"
    INSERT_CHART = "insert_chart"
    INSERT_CHART_SHARP = "insert_chart_sharp"
    INSERT_CHART_ROUNDED = "insert_chart_rounded"
    INSERT_CHART_OUTLINED = "insert_chart_outlined"
    INSERT_CHART_OUTLINED_SHARP = "insert_chart_outlined_sharp"
    INSERT_CHART_OUTLINED_ROUNDED = "insert_chart_outlined_rounded"
    INSERT_CHART_OUTLINED_OUTLINED = "insert_chart_outlined_outlined"
    INSERT_COMMENT = "insert_comment"
    INSERT_COMMENT_SHARP = "insert_comment_sharp"
    INSERT_COMMENT_ROUNDED = "insert_comment_rounded"
    INSERT_COMMENT_OUTLINED = "insert_comment_outlined"
    INSERT_DRIVE_FILE = "insert_drive_file"
    INSERT_DRIVE_FILE_SHARP = "insert_drive_file_sharp"
    INSERT_DRIVE_FILE_ROUNDED = "insert_drive_file_rounded"
    INSERT_DRIVE_FILE_OUTLINED = "insert_drive_file_outlined"
    INSERT_EMOTICON = "insert_emoticon"
    INSERT_EMOTICON_SHARP = "insert_emoticon_sharp"
    INSERT_EMOTICON_ROUNDED = "insert_emoticon_rounded"
    INSERT_EMOTICON_OUTLINED = "insert_emoticon_outlined"
    INSERT_INVITATION = "insert_invitation"
    INSERT_INVITATION_SHARP = "insert_invitation_sharp"
    INSERT_INVITATION_ROUNDED = "insert_invitation_rounded"
    INSERT_INVITATION_OUTLINED = "insert_invitation_outlined"
    INSERT_LINK = "insert_link"
    INSERT_LINK_SHARP = "insert_link_sharp"
    INSERT_LINK_ROUNDED = "insert_link_rounded"
    INSERT_LINK_OUTLINED = "insert_link_outlined"
    INSERT_PAGE_BREAK = "insert_page_break"
    INSERT_PAGE_BREAK_SHARP = "insert_page_break_sharp"
    INSERT_PAGE_BREAK_ROUNDED = "insert_page_break_rounded"
    INSERT_PAGE_BREAK_OUTLINED = "insert_page_break_outlined"
    INSERT_PHOTO = "insert_photo"
    INSERT_PHOTO_SHARP = "insert_photo_sharp"
    INSERT_PHOTO_ROUNDED = "insert_photo_rounded"
    INSERT_PHOTO_OUTLINED = "insert_photo_outlined"
    INSIGHTS = "insights"
    INSIGHTS_SHARP = "insights_sharp"
    INSIGHTS_ROUNDED = "insights_rounded"
    INSIGHTS_OUTLINED = "insights_outlined"
    INSTALL_DESKTOP = "install_desktop"
    INSTALL_DESKTOP_SHARP = "install_desktop_sharp"
    INSTALL_DESKTOP_ROUNDED = "install_desktop_rounded"
    INSTALL_DESKTOP_OUTLINED = "install_desktop_outlined"
    INSTALL_MOBILE = "install_mobile"
    INSTALL_MOBILE_SHARP = "install_mobile_sharp"
    INSTALL_MOBILE_ROUNDED = "install_mobile_rounded"
    INSTALL_MOBILE_OUTLINED = "install_mobile_outlined"
    INTEGRATION_INSTRUCTIONS = "integration_instructions"
    INTEGRATION_INSTRUCTIONS_SHARP = "integration_instructions_sharp"
    INTEGRATION_INSTRUCTIONS_ROUNDED = "integration_instructions_rounded"
    INTEGRATION_INSTRUCTIONS_OUTLINED = "integration_instructions_outlined"
    INTERESTS = "interests"
    INTERESTS_SHARP = "interests_sharp"
    INTERESTS_ROUNDED = "interests_rounded"
    INTERESTS_OUTLINED = "interests_outlined"
    INTERPRETER_MODE = "interpreter_mode"
    INTERPRETER_MODE_SHARP = "interpreter_mode_sharp"
    INTERPRETER_MODE_ROUNDED = "interpreter_mode_rounded"
    INTERPRETER_MODE_OUTLINED = "interpreter_mode_outlined"
    INVENTORY = "inventory"
    INVENTORY_SHARP = "inventory_sharp"
    INVENTORY_ROUNDED = "inventory_rounded"
    INVENTORY_OUTLINED = "inventory_outlined"
    INVENTORY_2 = "inventory_2"
    INVENTORY_2_SHARP = "inventory_2_sharp"
    INVENTORY_2_ROUNDED = "inventory_2_rounded"
    INVENTORY_2_OUTLINED = "inventory_2_outlined"
    INVERT_COLORS = "invert_colors"
    INVERT_COLORS_SHARP = "invert_colors_sharp"
    INVERT_COLORS_ROUNDED = "invert_colors_rounded"
    INVERT_COLORS_OUTLINED = "invert_colors_outlined"
    INVERT_COLORS_OFF = "invert_colors_off"
    INVERT_COLORS_OFF_SHARP = "invert_colors_off_sharp"
    INVERT_COLORS_OFF_ROUNDED = "invert_colors_off_rounded"
    INVERT_COLORS_OFF_OUTLINED = "invert_colors_off_outlined"
    INVERT_COLORS_ON = "invert_colors_on"
    INVERT_COLORS_ON_SHARP = "invert_colors_on_sharp"
    INVERT_COLORS_ON_ROUNDED = "invert_colors_on_rounded"
    INVERT_COLORS_ON_OUTLINED = "invert_colors_on_outlined"
    IOS_SHARE = "ios_share"
    IOS_SHARE_SHARP = "ios_share_sharp"
    IOS_SHARE_ROUNDED = "ios_share_rounded"
    IOS_SHARE_OUTLINED = "ios_share_outlined"
    IRON = "iron"
    IRON_SHARP = "iron_sharp"
    IRON_ROUNDED = "iron_rounded"
    IRON_OUTLINED = "iron_outlined"
    ISO = "iso"
    ISO_SHARP = "iso_sharp"
    ISO_ROUNDED = "iso_rounded"
    ISO_OUTLINED = "iso_outlined"
    JAVASCRIPT = "javascript"
    JAVASCRIPT_SHARP = "javascript_sharp"
    JAVASCRIPT_ROUNDED = "javascript_rounded"
    JAVASCRIPT_OUTLINED = "javascript_outlined"
    JOIN_FULL = "join_full"
    JOIN_FULL_SHARP = "join_full_sharp"
    JOIN_FULL_ROUNDED = "join_full_rounded"
    JOIN_FULL_OUTLINED = "join_full_outlined"
    JOIN_INNER = "join_inner"
    JOIN_INNER_SHARP = "join_inner_sharp"
    JOIN_INNER_ROUNDED = "join_inner_rounded"
    JOIN_INNER_OUTLINED = "join_inner_outlined"
    JOIN_LEFT = "join_left"
    JOIN_LEFT_SHARP = "join_left_sharp"
    JOIN_LEFT_ROUNDED = "join_left_rounded"
    JOIN_LEFT_OUTLINED = "join_left_outlined"
    JOIN_RIGHT = "join_right"
    JOIN_RIGHT_SHARP = "join_right_sharp"
    JOIN_RIGHT_ROUNDED = "join_right_rounded"
    JOIN_RIGHT_OUTLINED = "join_right_outlined"
    KAYAKING = "kayaking"
    KAYAKING_SHARP = "kayaking_sharp"
    KAYAKING_ROUNDED = "kayaking_rounded"
    KAYAKING_OUTLINED = "kayaking_outlined"
    KEBAB_DINING = "kebab_dining"
    KEBAB_DINING_SHARP = "kebab_dining_sharp"
    KEBAB_DINING_ROUNDED = "kebab_dining_rounded"
    KEBAB_DINING_OUTLINED = "kebab_dining_outlined"
    KEY = "key"
    KEY_SHARP = "key_sharp"
    KEY_ROUNDED = "key_rounded"
    KEY_OUTLINED = "key_outlined"
    KEY_OFF = "key_off"
    KEY_OFF_SHARP = "key_off_sharp"
    KEY_OFF_ROUNDED = "key_off_rounded"
    KEY_OFF_OUTLINED = "key_off_outlined"
    KEYBOARD = "keyboard"
    KEYBOARD_SHARP = "keyboard_sharp"
    KEYBOARD_ROUNDED = "keyboard_rounded"
    KEYBOARD_OUTLINED = "keyboard_outlined"
    KEYBOARD_ALT = "keyboard_alt"
    KEYBOARD_ALT_SHARP = "keyboard_alt_sharp"
    KEYBOARD_ALT_ROUNDED = "keyboard_alt_rounded"
    KEYBOARD_ALT_OUTLINED = "keyboard_alt_outlined"
    KEYBOARD_ARROW_DOWN = "keyboard_arrow_down"
    KEYBOARD_ARROW_DOWN_SHARP = "keyboard_arrow_down_sharp"
    KEYBOARD_ARROW_DOWN_ROUNDED = "keyboard_arrow_down_rounded"
    KEYBOARD_ARROW_DOWN_OUTLINED = "keyboard_arrow_down_outlined"
    KEYBOARD_ARROW_LEFT = "keyboard_arrow_left"
    KEYBOARD_ARROW_LEFT_SHARP = "keyboard_arrow_left_sharp"
    KEYBOARD_ARROW_LEFT_ROUNDED = "keyboard_arrow_left_rounded"
    KEYBOARD_ARROW_LEFT_OUTLINED = "keyboard_arrow_left_outlined"
    KEYBOARD_ARROW_RIGHT = "keyboard_arrow_right"
    KEYBOARD_ARROW_RIGHT_SHARP = "keyboard_arrow_right_sharp"
    KEYBOARD_ARROW_RIGHT_ROUNDED = "keyboard_arrow_right_rounded"
    KEYBOARD_ARROW_RIGHT_OUTLINED = "keyboard_arrow_right_outlined"
    KEYBOARD_ARROW_UP = "keyboard_arrow_up"
    KEYBOARD_ARROW_UP_SHARP = "keyboard_arrow_up_sharp"
    KEYBOARD_ARROW_UP_ROUNDED = "keyboard_arrow_up_rounded"
    KEYBOARD_ARROW_UP_OUTLINED = "keyboard_arrow_up_outlined"
    KEYBOARD_BACKSPACE = "keyboard_backspace"
    KEYBOARD_BACKSPACE_SHARP = "keyboard_backspace_sharp"
    KEYBOARD_BACKSPACE_ROUNDED = "keyboard_backspace_rounded"
    KEYBOARD_BACKSPACE_OUTLINED = "keyboard_backspace_outlined"
    KEYBOARD_CAPSLOCK = "keyboard_capslock"
    KEYBOARD_CAPSLOCK_SHARP = "keyboard_capslock_sharp"
    KEYBOARD_CAPSLOCK_ROUNDED = "keyboard_capslock_rounded"
    KEYBOARD_CAPSLOCK_OUTLINED = "keyboard_capslock_outlined"
    KEYBOARD_COMMAND_KEY = "keyboard_command_key"
    KEYBOARD_COMMAND_KEY_SHARP = "keyboard_command_key_sharp"
    KEYBOARD_COMMAND_KEY_ROUNDED = "keyboard_command_key_rounded"
    KEYBOARD_COMMAND_KEY_OUTLINED = "keyboard_command_key_outlined"
    KEYBOARD_CONTROL = "keyboard_control"
    KEYBOARD_CONTROL_SHARP = "keyboard_control_sharp"
    KEYBOARD_CONTROL_ROUNDED = "keyboard_control_rounded"
    KEYBOARD_CONTROL_OUTLINED = "keyboard_control_outlined"
    KEYBOARD_CONTROL_KEY = "keyboard_control_key"
    KEYBOARD_CONTROL_KEY_SHARP = "keyboard_control_key_sharp"
    KEYBOARD_CONTROL_KEY_ROUNDED = "keyboard_control_key_rounded"
    KEYBOARD_CONTROL_KEY_OUTLINED = "keyboard_control_key_outlined"
    KEYBOARD_DOUBLE_ARROW_DOWN = "keyboard_double_arrow_down"
    KEYBOARD_DOUBLE_ARROW_DOWN_SHARP = "keyboard_double_arrow_down_sharp"
    KEYBOARD_DOUBLE_ARROW_DOWN_ROUNDED = "keyboard_double_arrow_down_rounded"
    KEYBOARD_DOUBLE_ARROW_DOWN_OUTLINED = "keyboard_double_arrow_down_outlined"
    KEYBOARD_DOUBLE_ARROW_LEFT = "keyboard_double_arrow_left"
    KEYBOARD_DOUBLE_ARROW_LEFT_SHARP = "keyboard_double_arrow_left_sharp"
    KEYBOARD_DOUBLE_ARROW_LEFT_ROUNDED = "keyboard_double_arrow_left_rounded"
    KEYBOARD_DOUBLE_ARROW_LEFT_OUTLINED = "keyboard_double_arrow_left_outlined"
    KEYBOARD_DOUBLE_ARROW_RIGHT = "keyboard_double_arrow_right"
    KEYBOARD_DOUBLE_ARROW_RIGHT_SHARP = "keyboard_double_arrow_right_sharp"
    KEYBOARD_DOUBLE_ARROW_RIGHT_ROUNDED = "keyboard_double_arrow_right_rounded"
    KEYBOARD_DOUBLE_ARROW_RIGHT_OUTLINED = "keyboard_double_arrow_right_outlined"
    KEYBOARD_DOUBLE_ARROW_UP = "keyboard_double_arrow_up"
    KEYBOARD_DOUBLE_ARROW_UP_SHARP = "keyboard_double_arrow_up_sharp"
    KEYBOARD_DOUBLE_ARROW_UP_ROUNDED = "keyboard_double_arrow_up_rounded"
    KEYBOARD_DOUBLE_ARROW_UP_OUTLINED = "keyboard_double_arrow_up_outlined"
    KEYBOARD_HIDE = "keyboard_hide"
    KEYBOARD_HIDE_SHARP = "keyboard_hide_sharp"
    KEYBOARD_HIDE_ROUNDED = "keyboard_hide_rounded"
    KEYBOARD_HIDE_OUTLINED = "keyboard_hide_outlined"
    KEYBOARD_OPTION_KEY = "keyboard_option_key"
    KEYBOARD_OPTION_KEY_SHARP = "keyboard_option_key_sharp"
    KEYBOARD_OPTION_KEY_ROUNDED = "keyboard_option_key_rounded"
    KEYBOARD_OPTION_KEY_OUTLINED = "keyboard_option_key_outlined"
    KEYBOARD_RETURN = "keyboard_return"
    KEYBOARD_RETURN_SHARP = "keyboard_return_sharp"
    KEYBOARD_RETURN_ROUNDED = "keyboard_return_rounded"
    KEYBOARD_RETURN_OUTLINED = "keyboard_return_outlined"
    KEYBOARD_TAB = "keyboard_tab"
    KEYBOARD_TAB_SHARP = "keyboard_tab_sharp"
    KEYBOARD_TAB_ROUNDED = "keyboard_tab_rounded"
    KEYBOARD_TAB_OUTLINED = "keyboard_tab_outlined"
    KEYBOARD_VOICE = "keyboard_voice"
    KEYBOARD_VOICE_SHARP = "keyboard_voice_sharp"
    KEYBOARD_VOICE_ROUNDED = "keyboard_voice_rounded"
    KEYBOARD_VOICE_OUTLINED = "keyboard_voice_outlined"
    KING_BED = "king_bed"
    KING_BED_SHARP = "king_bed_sharp"
    KING_BED_ROUNDED = "king_bed_rounded"
    KING_BED_OUTLINED = "king_bed_outlined"
    KITCHEN = "kitchen"
    KITCHEN_SHARP = "kitchen_sharp"
    KITCHEN_ROUNDED = "kitchen_rounded"
    KITCHEN_OUTLINED = "kitchen_outlined"
    KITESURFING = "kitesurfing"
    KITESURFING_SHARP = "kitesurfing_sharp"
    KITESURFING_ROUNDED = "kitesurfing_rounded"
    KITESURFING_OUTLINED = "kitesurfing_outlined"
    LABEL = "label"
    LABEL_SHARP = "label_sharp"
    LABEL_ROUNDED = "label_rounded"
    LABEL_OUTLINED = "label_outlined"
    LABEL_IMPORTANT = "label_important"
    LABEL_IMPORTANT_SHARP = "label_important_sharp"
    LABEL_IMPORTANT_ROUNDED = "label_important_rounded"
    LABEL_IMPORTANT_OUTLINED = "label_important_outlined"
    LABEL_IMPORTANT_OUTLINE = "label_important_outline"
    LABEL_IMPORTANT_OUTLINE_SHARP = "label_important_outline_sharp"
    LABEL_IMPORTANT_OUTLINE_ROUNDED = "label_important_outline_rounded"
    LABEL_OFF = "label_off"
    LABEL_OFF_SHARP = "label_off_sharp"
    LABEL_OFF_ROUNDED = "label_off_rounded"
    LABEL_OFF_OUTLINED = "label_off_outlined"
    LABEL_OUTLINE = "label_outline"
    LABEL_OUTLINE_SHARP = "label_outline_sharp"
    LABEL_OUTLINE_ROUNDED = "label_outline_rounded"
    LAN = "lan"
    LAN_SHARP = "lan_sharp"
    LAN_ROUNDED = "lan_rounded"
    LAN_OUTLINED = "lan_outlined"
    LANDSCAPE = "landscape"
    LANDSCAPE_SHARP = "landscape_sharp"
    LANDSCAPE_ROUNDED = "landscape_rounded"
    LANDSCAPE_OUTLINED = "landscape_outlined"
    LANDSLIDE = "landslide"
    LANDSLIDE_SHARP = "landslide_sharp"
    LANDSLIDE_ROUNDED = "landslide_rounded"
    LANDSLIDE_OUTLINED = "landslide_outlined"
    LANGUAGE = "language"
    LANGUAGE_SHARP = "language_sharp"
    LANGUAGE_ROUNDED = "language_rounded"
    LANGUAGE_OUTLINED = "language_outlined"
    LAPTOP = "laptop"
    LAPTOP_SHARP = "laptop_sharp"
    LAPTOP_ROUNDED = "laptop_rounded"
    LAPTOP_OUTLINED = "laptop_outlined"
    LAPTOP_CHROMEBOOK = "laptop_chromebook"
    LAPTOP_CHROMEBOOK_SHARP = "laptop_chromebook_sharp"
    LAPTOP_CHROMEBOOK_ROUNDED = "laptop_chromebook_rounded"
    LAPTOP_CHROMEBOOK_OUTLINED = "laptop_chromebook_outlined"
    LAPTOP_MAC = "laptop_mac"
    LAPTOP_MAC_SHARP = "laptop_mac_sharp"
    LAPTOP_MAC_ROUNDED = "laptop_mac_rounded"
    LAPTOP_MAC_OUTLINED = "laptop_mac_outlined"
    LAPTOP_WINDOWS = "laptop_windows"
    LAPTOP_WINDOWS_SHARP = "laptop_windows_sharp"
    LAPTOP_WINDOWS_ROUNDED = "laptop_windows_rounded"
    LAPTOP_WINDOWS_OUTLINED = "laptop_windows_outlined"
    LAST_PAGE = "last_page"
    LAST_PAGE_SHARP = "last_page_sharp"
    LAST_PAGE_ROUNDED = "last_page_rounded"
    LAST_PAGE_OUTLINED = "last_page_outlined"
    LAUNCH = "launch"
    LAUNCH_SHARP = "launch_sharp"
    LAUNCH_ROUNDED = "launch_rounded"
    LAUNCH_OUTLINED = "launch_outlined"
    LAYERS = "layers"
    LAYERS_SHARP = "layers_sharp"
    LAYERS_ROUNDED = "layers_rounded"
    LAYERS_OUTLINED = "layers_outlined"
    LAYERS_CLEAR = "layers_clear"
    LAYERS_CLEAR_SHARP = "layers_clear_sharp"
    LAYERS_CLEAR_ROUNDED = "layers_clear_rounded"
    LAYERS_CLEAR_OUTLINED = "layers_clear_outlined"
    LEADERBOARD = "leaderboard"
    LEADERBOARD_SHARP = "leaderboard_sharp"
    LEADERBOARD_ROUNDED = "leaderboard_rounded"
    LEADERBOARD_OUTLINED = "leaderboard_outlined"
    LEAK_ADD = "leak_add"
    LEAK_ADD_SHARP = "leak_add_sharp"
    LEAK_ADD_ROUNDED = "leak_add_rounded"
    LEAK_ADD_OUTLINED = "leak_add_outlined"
    LEAK_REMOVE = "leak_remove"
    LEAK_REMOVE_SHARP = "leak_remove_sharp"
    LEAK_REMOVE_ROUNDED = "leak_remove_rounded"
    LEAK_REMOVE_OUTLINED = "leak_remove_outlined"
    LEAVE_BAGS_AT_HOME = "leave_bags_at_home"
    LEAVE_BAGS_AT_HOME_SHARP = "leave_bags_at_home_sharp"
    LEAVE_BAGS_AT_HOME_ROUNDED = "leave_bags_at_home_rounded"
    LEAVE_BAGS_AT_HOME_OUTLINED = "leave_bags_at_home_outlined"
    LEGEND_TOGGLE = "legend_toggle"
    LEGEND_TOGGLE_SHARP = "legend_toggle_sharp"
    LEGEND_TOGGLE_ROUNDED = "legend_toggle_rounded"
    LEGEND_TOGGLE_OUTLINED = "legend_toggle_outlined"
    LENS = "lens"
    LENS_SHARP = "lens_sharp"
    LENS_ROUNDED = "lens_rounded"
    LENS_OUTLINED = "lens_outlined"
    LENS_BLUR = "lens_blur"
    LENS_BLUR_SHARP = "lens_blur_sharp"
    LENS_BLUR_ROUNDED = "lens_blur_rounded"
    LENS_BLUR_OUTLINED = "lens_blur_outlined"
    LIBRARY_ADD = "library_add"
    LIBRARY_ADD_SHARP = "library_add_sharp"
    LIBRARY_ADD_ROUNDED = "library_add_rounded"
    LIBRARY_ADD_OUTLINED = "library_add_outlined"
    LIBRARY_ADD_CHECK = "library_add_check"
    LIBRARY_ADD_CHECK_SHARP = "library_add_check_sharp"
    LIBRARY_ADD_CHECK_ROUNDED = "library_add_check_rounded"
    LIBRARY_ADD_CHECK_OUTLINED = "library_add_check_outlined"
    LIBRARY_BOOKS = "library_books"
    LIBRARY_BOOKS_SHARP = "library_books_sharp"
    LIBRARY_BOOKS_ROUNDED = "library_books_rounded"
    LIBRARY_BOOKS_OUTLINED = "library_books_outlined"
    LIBRARY_MUSIC = "library_music"
    LIBRARY_MUSIC_SHARP = "library_music_sharp"
    LIBRARY_MUSIC_ROUNDED = "library_music_rounded"
    LIBRARY_MUSIC_OUTLINED = "library_music_outlined"
    LIGHT = "light"
    LIGHT_SHARP = "light_sharp"
    LIGHT_ROUNDED = "light_rounded"
    LIGHT_OUTLINED = "light_outlined"
    LIGHT_MODE = "light_mode"
    LIGHT_MODE_SHARP = "light_mode_sharp"
    LIGHT_MODE_ROUNDED = "light_mode_rounded"
    LIGHT_MODE_OUTLINED = "light_mode_outlined"
    LIGHTBULB = "lightbulb"
    LIGHTBULB_SHARP = "lightbulb_sharp"
    LIGHTBULB_ROUNDED = "lightbulb_rounded"
    LIGHTBULB_OUTLINED = "lightbulb_outlined"
    LIGHTBULB_CIRCLE = "lightbulb_circle"
    LIGHTBULB_CIRCLE_SHARP = "lightbulb_circle_sharp"
    LIGHTBULB_CIRCLE_ROUNDED = "lightbulb_circle_rounded"
    LIGHTBULB_CIRCLE_OUTLINED = "lightbulb_circle_outlined"
    LIGHTBULB_OUTLINE = "lightbulb_outline"
    LIGHTBULB_OUTLINE_SHARP = "lightbulb_outline_sharp"
    LIGHTBULB_OUTLINE_ROUNDED = "lightbulb_outline_rounded"
    LINE_AXIS = "line_axis"
    LINE_AXIS_SHARP = "line_axis_sharp"
    LINE_AXIS_ROUNDED = "line_axis_rounded"
    LINE_AXIS_OUTLINED = "line_axis_outlined"
    LINE_STYLE = "line_style"
    LINE_STYLE_SHARP = "line_style_sharp"
    LINE_STYLE_ROUNDED = "line_style_rounded"
    LINE_STYLE_OUTLINED = "line_style_outlined"
    LINE_WEIGHT = "line_weight"
    LINE_WEIGHT_SHARP = "line_weight_sharp"
    LINE_WEIGHT_ROUNDED = "line_weight_rounded"
    LINE_WEIGHT_OUTLINED = "line_weight_outlined"
    LINEAR_SCALE = "linear_scale"
    LINEAR_SCALE_SHARP = "linear_scale_sharp"
    LINEAR_SCALE_ROUNDED = "linear_scale_rounded"
    LINEAR_SCALE_OUTLINED = "linear_scale_outlined"
    LINK = "link"
    LINK_SHARP = "link_sharp"
    LINK_ROUNDED = "link_rounded"
    LINK_OUTLINED = "link_outlined"
    LINK_OFF = "link_off"
    LINK_OFF_SHARP = "link_off_sharp"
    LINK_OFF_ROUNDED = "link_off_rounded"
    LINK_OFF_OUTLINED = "link_off_outlined"
    LINKED_CAMERA = "linked_camera"
    LINKED_CAMERA_SHARP = "linked_camera_sharp"
    LINKED_CAMERA_ROUNDED = "linked_camera_rounded"
    LINKED_CAMERA_OUTLINED = "linked_camera_outlined"
    LIQUOR = "liquor"
    LIQUOR_SHARP = "liquor_sharp"
    LIQUOR_ROUNDED = "liquor_rounded"
    LIQUOR_OUTLINED = "liquor_outlined"
    LIST = "list"
    LIST_SHARP = "list_sharp"
    LIST_ROUNDED = "list_rounded"
    LIST_OUTLINED = "list_outlined"
    LIST_ALT = "list_alt"
    LIST_ALT_SHARP = "list_alt_sharp"
    LIST_ALT_ROUNDED = "list_alt_rounded"
    LIST_ALT_OUTLINED = "list_alt_outlined"
    LIVE_HELP = "live_help"
    LIVE_HELP_SHARP = "live_help_sharp"
    LIVE_HELP_ROUNDED = "live_help_rounded"
    LIVE_HELP_OUTLINED = "live_help_outlined"
    LIVE_TV = "live_tv"
    LIVE_TV_SHARP = "live_tv_sharp"
    LIVE_TV_ROUNDED = "live_tv_rounded"
    LIVE_TV_OUTLINED = "live_tv_outlined"
    LIVING = "living"
    LIVING_SHARP = "living_sharp"
    LIVING_ROUNDED = "living_rounded"
    LIVING_OUTLINED = "living_outlined"
    LOCAL_ACTIVITY = "local_activity"
    LOCAL_ACTIVITY_SHARP = "local_activity_sharp"
    LOCAL_ACTIVITY_ROUNDED = "local_activity_rounded"
    LOCAL_ACTIVITY_OUTLINED = "local_activity_outlined"
    LOCAL_AIRPORT = "local_airport"
    LOCAL_AIRPORT_SHARP = "local_airport_sharp"
    LOCAL_AIRPORT_ROUNDED = "local_airport_rounded"
    LOCAL_AIRPORT_OUTLINED = "local_airport_outlined"
    LOCAL_ATM = "local_atm"
    LOCAL_ATM_SHARP = "local_atm_sharp"
    LOCAL_ATM_ROUNDED = "local_atm_rounded"
    LOCAL_ATM_OUTLINED = "local_atm_outlined"
    LOCAL_ATTRACTION = "local_attraction"
    LOCAL_ATTRACTION_SHARP = "local_attraction_sharp"
    LOCAL_ATTRACTION_ROUNDED = "local_attraction_rounded"
    LOCAL_ATTRACTION_OUTLINED = "local_attraction_outlined"
    LOCAL_BAR = "local_bar"
    LOCAL_BAR_SHARP = "local_bar_sharp"
    LOCAL_BAR_ROUNDED = "local_bar_rounded"
    LOCAL_BAR_OUTLINED = "local_bar_outlined"
    LOCAL_CAFE = "local_cafe"
    LOCAL_CAFE_SHARP = "local_cafe_sharp"
    LOCAL_CAFE_ROUNDED = "local_cafe_rounded"
    LOCAL_CAFE_OUTLINED = "local_cafe_outlined"
    LOCAL_CAR_WASH = "local_car_wash"
    LOCAL_CAR_WASH_SHARP = "local_car_wash_sharp"
    LOCAL_CAR_WASH_ROUNDED = "local_car_wash_rounded"
    LOCAL_CAR_WASH_OUTLINED = "local_car_wash_outlined"
    LOCAL_CONVENIENCE_STORE = "local_convenience_store"
    LOCAL_CONVENIENCE_STORE_SHARP = "local_convenience_store_sharp"
    LOCAL_CONVENIENCE_STORE_ROUNDED = "local_convenience_store_rounded"
    LOCAL_CONVENIENCE_STORE_OUTLINED = "local_convenience_store_outlined"
    LOCAL_DINING = "local_dining"
    LOCAL_DINING_SHARP = "local_dining_sharp"
    LOCAL_DINING_ROUNDED = "local_dining_rounded"
    LOCAL_DINING_OUTLINED = "local_dining_outlined"
    LOCAL_DRINK = "local_drink"
    LOCAL_DRINK_SHARP = "local_drink_sharp"
    LOCAL_DRINK_ROUNDED = "local_drink_rounded"
    LOCAL_DRINK_OUTLINED = "local_drink_outlined"
    LOCAL_FIRE_DEPARTMENT = "local_fire_department"
    LOCAL_FIRE_DEPARTMENT_SHARP = "local_fire_department_sharp"
    LOCAL_FIRE_DEPARTMENT_ROUNDED = "local_fire_department_rounded"
    LOCAL_FIRE_DEPARTMENT_OUTLINED = "local_fire_department_outlined"
    LOCAL_FLORIST = "local_florist"
    LOCAL_FLORIST_SHARP = "local_florist_sharp"
    LOCAL_FLORIST_ROUNDED = "local_florist_rounded"
    LOCAL_FLORIST_OUTLINED = "local_florist_outlined"
    LOCAL_GAS_STATION = "local_gas_station"
    LOCAL_GAS_STATION_SHARP = "local_gas_station_sharp"
    LOCAL_GAS_STATION_ROUNDED = "local_gas_station_rounded"
    LOCAL_GAS_STATION_OUTLINED = "local_gas_station_outlined"
    LOCAL_GROCERY_STORE = "local_grocery_store"
    LOCAL_GROCERY_STORE_SHARP = "local_grocery_store_sharp"
    LOCAL_GROCERY_STORE_ROUNDED = "local_grocery_store_rounded"
    LOCAL_GROCERY_STORE_OUTLINED = "local_grocery_store_outlined"
    LOCAL_HOSPITAL = "local_hospital"
    LOCAL_HOSPITAL_SHARP = "local_hospital_sharp"
    LOCAL_HOSPITAL_ROUNDED = "local_hospital_rounded"
    LOCAL_HOSPITAL_OUTLINED = "local_hospital_outlined"
    LOCAL_HOTEL = "local_hotel"
    LOCAL_HOTEL_SHARP = "local_hotel_sharp"
    LOCAL_HOTEL_ROUNDED = "local_hotel_rounded"
    LOCAL_HOTEL_OUTLINED = "local_hotel_outlined"
    LOCAL_LAUNDRY_SERVICE = "local_laundry_service"
    LOCAL_LAUNDRY_SERVICE_SHARP = "local_laundry_service_sharp"
    LOCAL_LAUNDRY_SERVICE_ROUNDED = "local_laundry_service_rounded"
    LOCAL_LAUNDRY_SERVICE_OUTLINED = "local_laundry_service_outlined"
    LOCAL_LIBRARY = "local_library"
    LOCAL_LIBRARY_SHARP = "local_library_sharp"
    LOCAL_LIBRARY_ROUNDED = "local_library_rounded"
    LOCAL_LIBRARY_OUTLINED = "local_library_outlined"
    LOCAL_MALL = "local_mall"
    LOCAL_MALL_SHARP = "local_mall_sharp"
    LOCAL_MALL_ROUNDED = "local_mall_rounded"
    LOCAL_MALL_OUTLINED = "local_mall_outlined"
    LOCAL_MOVIES = "local_movies"
    LOCAL_MOVIES_SHARP = "local_movies_sharp"
    LOCAL_MOVIES_ROUNDED = "local_movies_rounded"
    LOCAL_MOVIES_OUTLINED = "local_movies_outlined"
    LOCAL_OFFER = "local_offer"
    LOCAL_OFFER_SHARP = "local_offer_sharp"
    LOCAL_OFFER_ROUNDED = "local_offer_rounded"
    LOCAL_OFFER_OUTLINED = "local_offer_outlined"
    LOCAL_PARKING = "local_parking"
    LOCAL_PARKING_SHARP = "local_parking_sharp"
    LOCAL_PARKING_ROUNDED = "local_parking_rounded"
    LOCAL_PARKING_OUTLINED = "local_parking_outlined"
    LOCAL_PHARMACY = "local_pharmacy"
    LOCAL_PHARMACY_SHARP = "local_pharmacy_sharp"
    LOCAL_PHARMACY_ROUNDED = "local_pharmacy_rounded"
    LOCAL_PHARMACY_OUTLINED = "local_pharmacy_outlined"
    LOCAL_PHONE = "local_phone"
    LOCAL_PHONE_SHARP = "local_phone_sharp"
    LOCAL_PHONE_ROUNDED = "local_phone_rounded"
    LOCAL_PHONE_OUTLINED = "local_phone_outlined"
    LOCAL_PIZZA = "local_pizza"
    LOCAL_PIZZA_SHARP = "local_pizza_sharp"
    LOCAL_PIZZA_ROUNDED = "local_pizza_rounded"
    LOCAL_PIZZA_OUTLINED = "local_pizza_outlined"
    LOCAL_PLAY = "local_play"
    LOCAL_PLAY_SHARP = "local_play_sharp"
    LOCAL_PLAY_ROUNDED = "local_play_rounded"
    LOCAL_PLAY_OUTLINED = "local_play_outlined"
    LOCAL_POLICE = "local_police"
    LOCAL_POLICE_SHARP = "local_police_sharp"
    LOCAL_POLICE_ROUNDED = "local_police_rounded"
    LOCAL_POLICE_OUTLINED = "local_police_outlined"
    LOCAL_POST_OFFICE = "local_post_office"
    LOCAL_POST_OFFICE_SHARP = "local_post_office_sharp"
    LOCAL_POST_OFFICE_ROUNDED = "local_post_office_rounded"
    LOCAL_POST_OFFICE_OUTLINED = "local_post_office_outlined"
    LOCAL_PRINT_SHOP = "local_print_shop"
    LOCAL_PRINT_SHOP_SHARP = "local_print_shop_sharp"
    LOCAL_PRINT_SHOP_ROUNDED = "local_print_shop_rounded"
    LOCAL_PRINT_SHOP_OUTLINED = "local_print_shop_outlined"
    LOCAL_PRINTSHOP = "local_printshop"
    LOCAL_PRINTSHOP_SHARP = "local_printshop_sharp"
    LOCAL_PRINTSHOP_ROUNDED = "local_printshop_rounded"
    LOCAL_PRINTSHOP_OUTLINED = "local_printshop_outlined"
    LOCAL_RESTAURANT = "local_restaurant"
    LOCAL_RESTAURANT_SHARP = "local_restaurant_sharp"
    LOCAL_RESTAURANT_ROUNDED = "local_restaurant_rounded"
    LOCAL_RESTAURANT_OUTLINED = "local_restaurant_outlined"
    LOCAL_SEE = "local_see"
    LOCAL_SEE_SHARP = "local_see_sharp"
    LOCAL_SEE_ROUNDED = "local_see_rounded"
    LOCAL_SEE_OUTLINED = "local_see_outlined"
    LOCAL_SHIPPING = "local_shipping"
    LOCAL_SHIPPING_SHARP = "local_shipping_sharp"
    LOCAL_SHIPPING_ROUNDED = "local_shipping_rounded"
    LOCAL_SHIPPING_OUTLINED = "local_shipping_outlined"
    LOCAL_TAXI = "local_taxi"
    LOCAL_TAXI_SHARP = "local_taxi_sharp"
    LOCAL_TAXI_ROUNDED = "local_taxi_rounded"
    LOCAL_TAXI_OUTLINED = "local_taxi_outlined"
    LOCATION_CITY = "location_city"
    LOCATION_CITY_SHARP = "location_city_sharp"
    LOCATION_CITY_ROUNDED = "location_city_rounded"
    LOCATION_CITY_OUTLINED = "location_city_outlined"
    LOCATION_DISABLED = "location_disabled"
    LOCATION_DISABLED_SHARP = "location_disabled_sharp"
    LOCATION_DISABLED_ROUNDED = "location_disabled_rounded"
    LOCATION_DISABLED_OUTLINED = "location_disabled_outlined"
    LOCATION_HISTORY = "location_history"
    LOCATION_HISTORY_SHARP = "location_history_sharp"
    LOCATION_HISTORY_ROUNDED = "location_history_rounded"
    LOCATION_HISTORY_OUTLINED = "location_history_outlined"
    LOCATION_OFF = "location_off"
    LOCATION_OFF_SHARP = "location_off_sharp"
    LOCATION_OFF_ROUNDED = "location_off_rounded"
    LOCATION_OFF_OUTLINED = "location_off_outlined"
    LOCATION_ON = "location_on"
    LOCATION_ON_SHARP = "location_on_sharp"
    LOCATION_ON_ROUNDED = "location_on_rounded"
    LOCATION_ON_OUTLINED = "location_on_outlined"
    LOCATION_PIN = "location_pin"
    LOCATION_SEARCHING = "location_searching"
    LOCATION_SEARCHING_SHARP = "location_searching_sharp"
    LOCATION_SEARCHING_ROUNDED = "location_searching_rounded"
    LOCATION_SEARCHING_OUTLINED = "location_searching_outlined"
    LOCK = "lock"
    LOCK_SHARP = "lock_sharp"
    LOCK_ROUNDED = "lock_rounded"
    LOCK_OUTLINED = "lock_outlined"
    LOCK_CLOCK = "lock_clock"
    LOCK_CLOCK_SHARP = "lock_clock_sharp"
    LOCK_CLOCK_ROUNDED = "lock_clock_rounded"
    LOCK_CLOCK_OUTLINED = "lock_clock_outlined"
    LOCK_OPEN = "lock_open"
    LOCK_OPEN_SHARP = "lock_open_sharp"
    LOCK_OPEN_ROUNDED = "lock_open_rounded"
    LOCK_OPEN_OUTLINED = "lock_open_outlined"
    LOCK_OUTLINE = "lock_outline"
    LOCK_OUTLINE_SHARP = "lock_outline_sharp"
    LOCK_OUTLINE_ROUNDED = "lock_outline_rounded"
    LOCK_PERSON = "lock_person"
    LOCK_PERSON_SHARP = "lock_person_sharp"
    LOCK_PERSON_ROUNDED = "lock_person_rounded"
    LOCK_PERSON_OUTLINED = "lock_person_outlined"
    LOCK_RESET = "lock_reset"
    LOCK_RESET_SHARP = "lock_reset_sharp"
    LOCK_RESET_ROUNDED = "lock_reset_rounded"
    LOCK_RESET_OUTLINED = "lock_reset_outlined"
    LOGIN = "login"
    LOGIN_SHARP = "login_sharp"
    LOGIN_ROUNDED = "login_rounded"
    LOGIN_OUTLINED = "login_outlined"
    LOGO_DEV = "logo_dev"
    LOGO_DEV_SHARP = "logo_dev_sharp"
    LOGO_DEV_ROUNDED = "logo_dev_rounded"
    LOGO_DEV_OUTLINED = "logo_dev_outlined"
    LOGOUT = "logout"
    LOGOUT_SHARP = "logout_sharp"
    LOGOUT_ROUNDED = "logout_rounded"
    LOGOUT_OUTLINED = "logout_outlined"
    LOOKS = "looks"
    LOOKS_SHARP = "looks_sharp"
    LOOKS_ROUNDED = "looks_rounded"
    LOOKS_OUTLINED = "looks_outlined"
    LOOKS_3 = "looks_3"
    LOOKS_3_SHARP = "looks_3_sharp"
    LOOKS_3_ROUNDED = "looks_3_rounded"
    LOOKS_3_OUTLINED = "looks_3_outlined"
    LOOKS_4 = "looks_4"
    LOOKS_4_SHARP = "looks_4_sharp"
    LOOKS_4_ROUNDED = "looks_4_rounded"
    LOOKS_4_OUTLINED = "looks_4_outlined"
    LOOKS_5 = "looks_5"
    LOOKS_5_SHARP = "looks_5_sharp"
    LOOKS_5_ROUNDED = "looks_5_rounded"
    LOOKS_5_OUTLINED = "looks_5_outlined"
    LOOKS_6 = "looks_6"
    LOOKS_6_SHARP = "looks_6_sharp"
    LOOKS_6_ROUNDED = "looks_6_rounded"
    LOOKS_6_OUTLINED = "looks_6_outlined"
    LOOKS_ONE = "looks_one"
    LOOKS_ONE_SHARP = "looks_one_sharp"
    LOOKS_ONE_ROUNDED = "looks_one_rounded"
    LOOKS_ONE_OUTLINED = "looks_one_outlined"
    LOOKS_TWO = "looks_two"
    LOOKS_TWO_SHARP = "looks_two_sharp"
    LOOKS_TWO_ROUNDED = "looks_two_rounded"
    LOOKS_TWO_OUTLINED = "looks_two_outlined"
    LOOP = "loop"
    LOOP_SHARP = "loop_sharp"
    LOOP_ROUNDED = "loop_rounded"
    LOOP_OUTLINED = "loop_outlined"
    LOUPE = "loupe"
    LOUPE_SHARP = "loupe_sharp"
    LOUPE_ROUNDED = "loupe_rounded"
    LOUPE_OUTLINED = "loupe_outlined"
    LOW_PRIORITY = "low_priority"
    LOW_PRIORITY_SHARP = "low_priority_sharp"
    LOW_PRIORITY_ROUNDED = "low_priority_rounded"
    LOW_PRIORITY_OUTLINED = "low_priority_outlined"
    LOYALTY = "loyalty"
    LOYALTY_SHARP = "loyalty_sharp"
    LOYALTY_ROUNDED = "loyalty_rounded"
    LOYALTY_OUTLINED = "loyalty_outlined"
    LTE_MOBILEDATA = "lte_mobiledata"
    LTE_MOBILEDATA_SHARP = "lte_mobiledata_sharp"
    LTE_MOBILEDATA_ROUNDED = "lte_mobiledata_rounded"
    LTE_MOBILEDATA_OUTLINED = "lte_mobiledata_outlined"
    LTE_PLUS_MOBILEDATA = "lte_plus_mobiledata"
    LTE_PLUS_MOBILEDATA_SHARP = "lte_plus_mobiledata_sharp"
    LTE_PLUS_MOBILEDATA_ROUNDED = "lte_plus_mobiledata_rounded"
    LTE_PLUS_MOBILEDATA_OUTLINED = "lte_plus_mobiledata_outlined"
    LUGGAGE = "luggage"
    LUGGAGE_SHARP = "luggage_sharp"
    LUGGAGE_ROUNDED = "luggage_rounded"
    LUGGAGE_OUTLINED = "luggage_outlined"
    LUNCH_DINING = "lunch_dining"
    LUNCH_DINING_SHARP = "lunch_dining_sharp"
    LUNCH_DINING_ROUNDED = "lunch_dining_rounded"
    LUNCH_DINING_OUTLINED = "lunch_dining_outlined"
    LYRICS = "lyrics"
    LYRICS_SHARP = "lyrics_sharp"
    LYRICS_ROUNDED = "lyrics_rounded"
    LYRICS_OUTLINED = "lyrics_outlined"
    MACRO_OFF = "macro_off"
    MACRO_OFF_SHARP = "macro_off_sharp"
    MACRO_OFF_ROUNDED = "macro_off_rounded"
    MACRO_OFF_OUTLINED = "macro_off_outlined"
    MAIL = "mail"
    MAIL_SHARP = "mail_sharp"
    MAIL_ROUNDED = "mail_rounded"
    MAIL_OUTLINED = "mail_outlined"
    MAIL_LOCK = "mail_lock"
    MAIL_LOCK_SHARP = "mail_lock_sharp"
    MAIL_LOCK_ROUNDED = "mail_lock_rounded"
    MAIL_LOCK_OUTLINED = "mail_lock_outlined"
    MAIL_OUTLINE = "mail_outline"
    MAIL_OUTLINE_SHARP = "mail_outline_sharp"
    MAIL_OUTLINE_ROUNDED = "mail_outline_rounded"
    MAIL_OUTLINE_OUTLINED = "mail_outline_outlined"
    MALE = "male"
    MALE_SHARP = "male_sharp"
    MALE_ROUNDED = "male_rounded"
    MALE_OUTLINED = "male_outlined"
    MAN = "man"
    MAN_SHARP = "man_sharp"
    MAN_ROUNDED = "man_rounded"
    MAN_OUTLINED = "man_outlined"
    MAN_2 = "man_2"
    MAN_2_SHARP = "man_2_sharp"
    MAN_2_ROUNDED = "man_2_rounded"
    MAN_2_OUTLINED = "man_2_outlined"
    MAN_3 = "man_3"
    MAN_3_SHARP = "man_3_sharp"
    MAN_3_ROUNDED = "man_3_rounded"
    MAN_3_OUTLINED = "man_3_outlined"
    MAN_4 = "man_4"
    MAN_4_SHARP = "man_4_sharp"
    MAN_4_ROUNDED = "man_4_rounded"
    MAN_4_OUTLINED = "man_4_outlined"
    MANAGE_ACCOUNTS = "manage_accounts"
    MANAGE_ACCOUNTS_SHARP = "manage_accounts_sharp"
    MANAGE_ACCOUNTS_ROUNDED = "manage_accounts_rounded"
    MANAGE_ACCOUNTS_OUTLINED = "manage_accounts_outlined"
    MANAGE_HISTORY = "manage_history"
    MANAGE_HISTORY_SHARP = "manage_history_sharp"
    MANAGE_HISTORY_ROUNDED = "manage_history_rounded"
    MANAGE_HISTORY_OUTLINED = "manage_history_outlined"
    MANAGE_SEARCH = "manage_search"
    MANAGE_SEARCH_SHARP = "manage_search_sharp"
    MANAGE_SEARCH_ROUNDED = "manage_search_rounded"
    MANAGE_SEARCH_OUTLINED = "manage_search_outlined"
    MAP = "map"
    MAP_SHARP = "map_sharp"
    MAP_ROUNDED = "map_rounded"
    MAP_OUTLINED = "map_outlined"
    MAPS_HOME_WORK = "maps_home_work"
    MAPS_HOME_WORK_SHARP = "maps_home_work_sharp"
    MAPS_HOME_WORK_ROUNDED = "maps_home_work_rounded"
    MAPS_HOME_WORK_OUTLINED = "maps_home_work_outlined"
    MAPS_UGC = "maps_ugc"
    MAPS_UGC_SHARP = "maps_ugc_sharp"
    MAPS_UGC_ROUNDED = "maps_ugc_rounded"
    MAPS_UGC_OUTLINED = "maps_ugc_outlined"
    MARGIN = "margin"
    MARGIN_SHARP = "margin_sharp"
    MARGIN_ROUNDED = "margin_rounded"
    MARGIN_OUTLINED = "margin_outlined"
    MARK_AS_UNREAD = "mark_as_unread"
    MARK_AS_UNREAD_SHARP = "mark_as_unread_sharp"
    MARK_AS_UNREAD_ROUNDED = "mark_as_unread_rounded"
    MARK_AS_UNREAD_OUTLINED = "mark_as_unread_outlined"
    MARK_CHAT_READ = "mark_chat_read"
    MARK_CHAT_READ_SHARP = "mark_chat_read_sharp"
    MARK_CHAT_READ_ROUNDED = "mark_chat_read_rounded"
    MARK_CHAT_READ_OUTLINED = "mark_chat_read_outlined"
    MARK_CHAT_UNREAD = "mark_chat_unread"
    MARK_CHAT_UNREAD_SHARP = "mark_chat_unread_sharp"
    MARK_CHAT_UNREAD_ROUNDED = "mark_chat_unread_rounded"
    MARK_CHAT_UNREAD_OUTLINED = "mark_chat_unread_outlined"
    MARK_EMAIL_READ = "mark_email_read"
    MARK_EMAIL_READ_SHARP = "mark_email_read_sharp"
    MARK_EMAIL_READ_ROUNDED = "mark_email_read_rounded"
    MARK_EMAIL_READ_OUTLINED = "mark_email_read_outlined"
    MARK_EMAIL_UNREAD = "mark_email_unread"
    MARK_EMAIL_UNREAD_SHARP = "mark_email_unread_sharp"
    MARK_EMAIL_UNREAD_ROUNDED = "mark_email_unread_rounded"
    MARK_EMAIL_UNREAD_OUTLINED = "mark_email_unread_outlined"
    MARK_UNREAD_CHAT_ALT = "mark_unread_chat_alt"
    MARK_UNREAD_CHAT_ALT_SHARP = "mark_unread_chat_alt_sharp"
    MARK_UNREAD_CHAT_ALT_ROUNDED = "mark_unread_chat_alt_rounded"
    MARK_UNREAD_CHAT_ALT_OUTLINED = "mark_unread_chat_alt_outlined"
    MARKUNREAD = "markunread"
    MARKUNREAD_SHARP = "markunread_sharp"
    MARKUNREAD_ROUNDED = "markunread_rounded"
    MARKUNREAD_OUTLINED = "markunread_outlined"
    MARKUNREAD_MAILBOX = "markunread_mailbox"
    MARKUNREAD_MAILBOX_SHARP = "markunread_mailbox_sharp"
    MARKUNREAD_MAILBOX_ROUNDED = "markunread_mailbox_rounded"
    MARKUNREAD_MAILBOX_OUTLINED = "markunread_mailbox_outlined"
    MASKS = "masks"
    MASKS_SHARP = "masks_sharp"
    MASKS_ROUNDED = "masks_rounded"
    MASKS_OUTLINED = "masks_outlined"
    MAXIMIZE = "maximize"
    MAXIMIZE_SHARP = "maximize_sharp"
    MAXIMIZE_ROUNDED = "maximize_rounded"
    MAXIMIZE_OUTLINED = "maximize_outlined"
    MEDIA_BLUETOOTH_OFF = "media_bluetooth_off"
    MEDIA_BLUETOOTH_OFF_SHARP = "media_bluetooth_off_sharp"
    MEDIA_BLUETOOTH_OFF_ROUNDED = "media_bluetooth_off_rounded"
    MEDIA_BLUETOOTH_OFF_OUTLINED = "media_bluetooth_off_outlined"
    MEDIA_BLUETOOTH_ON = "media_bluetooth_on"
    MEDIA_BLUETOOTH_ON_SHARP = "media_bluetooth_on_sharp"
    MEDIA_BLUETOOTH_ON_ROUNDED = "media_bluetooth_on_rounded"
    MEDIA_BLUETOOTH_ON_OUTLINED = "media_bluetooth_on_outlined"
    MEDIATION = "mediation"
    MEDIATION_SHARP = "mediation_sharp"
    MEDIATION_ROUNDED = "mediation_rounded"
    MEDIATION_OUTLINED = "mediation_outlined"
    MEDICAL_INFORMATION = "medical_information"
    MEDICAL_INFORMATION_SHARP = "medical_information_sharp"
    MEDICAL_INFORMATION_ROUNDED = "medical_information_rounded"
    MEDICAL_INFORMATION_OUTLINED = "medical_information_outlined"
    MEDICAL_SERVICES = "medical_services"
    MEDICAL_SERVICES_SHARP = "medical_services_sharp"
    MEDICAL_SERVICES_ROUNDED = "medical_services_rounded"
    MEDICAL_SERVICES_OUTLINED = "medical_services_outlined"
    MEDICATION = "medication"
    MEDICATION_SHARP = "medication_sharp"
    MEDICATION_ROUNDED = "medication_rounded"
    MEDICATION_OUTLINED = "medication_outlined"
    MEDICATION_LIQUID = "medication_liquid"
    MEDICATION_LIQUID_SHARP = "medication_liquid_sharp"
    MEDICATION_LIQUID_ROUNDED = "medication_liquid_rounded"
    MEDICATION_LIQUID_OUTLINED = "medication_liquid_outlined"
    MEETING_ROOM = "meeting_room"
    MEETING_ROOM_SHARP = "meeting_room_sharp"
    MEETING_ROOM_ROUNDED = "meeting_room_rounded"
    MEETING_ROOM_OUTLINED = "meeting_room_outlined"
    MEMORY = "memory"
    MEMORY_SHARP = "memory_sharp"
    MEMORY_ROUNDED = "memory_rounded"
    MEMORY_OUTLINED = "memory_outlined"
    MENU = "menu"
    MENU_SHARP = "menu_sharp"
    MENU_ROUNDED = "menu_rounded"
    MENU_OUTLINED = "menu_outlined"
    MENU_BOOK = "menu_book"
    MENU_BOOK_SHARP = "menu_book_sharp"
    MENU_BOOK_ROUNDED = "menu_book_rounded"
    MENU_BOOK_OUTLINED = "menu_book_outlined"
    MENU_OPEN = "menu_open"
    MENU_OPEN_SHARP = "menu_open_sharp"
    MENU_OPEN_ROUNDED = "menu_open_rounded"
    MENU_OPEN_OUTLINED = "menu_open_outlined"
    MERGE = "merge"
    MERGE_SHARP = "merge_sharp"
    MERGE_ROUNDED = "merge_rounded"
    MERGE_OUTLINED = "merge_outlined"
    MERGE_TYPE = "merge_type"
    MERGE_TYPE_SHARP = "merge_type_sharp"
    MERGE_TYPE_ROUNDED = "merge_type_rounded"
    MERGE_TYPE_OUTLINED = "merge_type_outlined"
    MESSAGE = "message"
    MESSAGE_SHARP = "message_sharp"
    MESSAGE_ROUNDED = "message_rounded"
    MESSAGE_OUTLINED = "message_outlined"
    MESSENGER = "messenger"
    MESSENGER_SHARP = "messenger_sharp"
    MESSENGER_ROUNDED = "messenger_rounded"
    MESSENGER_OUTLINED = "messenger_outlined"
    MESSENGER_OUTLINE = "messenger_outline"
    MESSENGER_OUTLINE_SHARP = "messenger_outline_sharp"
    MESSENGER_OUTLINE_ROUNDED = "messenger_outline_rounded"
    MESSENGER_OUTLINE_OUTLINED = "messenger_outline_outlined"
    MIC = "mic"
    MIC_SHARP = "mic_sharp"
    MIC_ROUNDED = "mic_rounded"
    MIC_OUTLINED = "mic_outlined"
    MIC_EXTERNAL_OFF = "mic_external_off"
    MIC_EXTERNAL_OFF_SHARP = "mic_external_off_sharp"
    MIC_EXTERNAL_OFF_ROUNDED = "mic_external_off_rounded"
    MIC_EXTERNAL_OFF_OUTLINED = "mic_external_off_outlined"
    MIC_EXTERNAL_ON = "mic_external_on"
    MIC_EXTERNAL_ON_SHARP = "mic_external_on_sharp"
    MIC_EXTERNAL_ON_ROUNDED = "mic_external_on_rounded"
    MIC_EXTERNAL_ON_OUTLINED = "mic_external_on_outlined"
    MIC_NONE = "mic_none"
    MIC_NONE_SHARP = "mic_none_sharp"
    MIC_NONE_ROUNDED = "mic_none_rounded"
    MIC_NONE_OUTLINED = "mic_none_outlined"
    MIC_OFF = "mic_off"
    MIC_OFF_SHARP = "mic_off_sharp"
    MIC_OFF_ROUNDED = "mic_off_rounded"
    MIC_OFF_OUTLINED = "mic_off_outlined"
    MICROWAVE = "microwave"
    MICROWAVE_SHARP = "microwave_sharp"
    MICROWAVE_ROUNDED = "microwave_rounded"
    MICROWAVE_OUTLINED = "microwave_outlined"
    MILITARY_TECH = "military_tech"
    MILITARY_TECH_SHARP = "military_tech_sharp"
    MILITARY_TECH_ROUNDED = "military_tech_rounded"
    MILITARY_TECH_OUTLINED = "military_tech_outlined"
    MINIMIZE = "minimize"
    MINIMIZE_SHARP = "minimize_sharp"
    MINIMIZE_ROUNDED = "minimize_rounded"
    MINIMIZE_OUTLINED = "minimize_outlined"
    MINOR_CRASH = "minor_crash"
    MINOR_CRASH_SHARP = "minor_crash_sharp"
    MINOR_CRASH_ROUNDED = "minor_crash_rounded"
    MINOR_CRASH_OUTLINED = "minor_crash_outlined"
    MISCELLANEOUS_SERVICES = "miscellaneous_services"
    MISCELLANEOUS_SERVICES_SHARP = "miscellaneous_services_sharp"
    MISCELLANEOUS_SERVICES_ROUNDED = "miscellaneous_services_rounded"
    MISCELLANEOUS_SERVICES_OUTLINED = "miscellaneous_services_outlined"
    MISSED_VIDEO_CALL = "missed_video_call"
    MISSED_VIDEO_CALL_SHARP = "missed_video_call_sharp"
    MISSED_VIDEO_CALL_ROUNDED = "missed_video_call_rounded"
    MISSED_VIDEO_CALL_OUTLINED = "missed_video_call_outlined"
    MMS = "mms"
    MMS_SHARP = "mms_sharp"
    MMS_ROUNDED = "mms_rounded"
    MMS_OUTLINED = "mms_outlined"
    MOBILE_FRIENDLY = "mobile_friendly"
    MOBILE_FRIENDLY_SHARP = "mobile_friendly_sharp"
    MOBILE_FRIENDLY_ROUNDED = "mobile_friendly_rounded"
    MOBILE_FRIENDLY_OUTLINED = "mobile_friendly_outlined"
    MOBILE_OFF = "mobile_off"
    MOBILE_OFF_SHARP = "mobile_off_sharp"
    MOBILE_OFF_ROUNDED = "mobile_off_rounded"
    MOBILE_OFF_OUTLINED = "mobile_off_outlined"
    MOBILE_SCREEN_SHARE = "mobile_screen_share"
    MOBILE_SCREEN_SHARE_SHARP = "mobile_screen_share_sharp"
    MOBILE_SCREEN_SHARE_ROUNDED = "mobile_screen_share_rounded"
    MOBILE_SCREEN_SHARE_OUTLINED = "mobile_screen_share_outlined"
    MOBILEDATA_OFF = "mobiledata_off"
    MOBILEDATA_OFF_SHARP = "mobiledata_off_sharp"
    MOBILEDATA_OFF_ROUNDED = "mobiledata_off_rounded"
    MOBILEDATA_OFF_OUTLINED = "mobiledata_off_outlined"
    MODE = "mode"
    MODE_SHARP = "mode_sharp"
    MODE_ROUNDED = "mode_rounded"
    MODE_OUTLINED = "mode_outlined"
    MODE_COMMENT = "mode_comment"
    MODE_COMMENT_SHARP = "mode_comment_sharp"
    MODE_COMMENT_ROUNDED = "mode_comment_rounded"
    MODE_COMMENT_OUTLINED = "mode_comment_outlined"
    MODE_EDIT = "mode_edit"
    MODE_EDIT_SHARP = "mode_edit_sharp"
    MODE_EDIT_ROUNDED = "mode_edit_rounded"
    MODE_EDIT_OUTLINED = "mode_edit_outlined"
    MODE_EDIT_OUTLINE = "mode_edit_outline"
    MODE_EDIT_OUTLINE_SHARP = "mode_edit_outline_sharp"
    MODE_EDIT_OUTLINE_ROUNDED = "mode_edit_outline_rounded"
    MODE_EDIT_OUTLINE_OUTLINED = "mode_edit_outline_outlined"
    MODE_FAN_OFF = "mode_fan_off"
    MODE_FAN_OFF_SHARP = "mode_fan_off_sharp"
    MODE_FAN_OFF_ROUNDED = "mode_fan_off_rounded"
    MODE_FAN_OFF_OUTLINED = "mode_fan_off_outlined"
    MODE_NIGHT = "mode_night"
    MODE_NIGHT_SHARP = "mode_night_sharp"
    MODE_NIGHT_ROUNDED = "mode_night_rounded"
    MODE_NIGHT_OUTLINED = "mode_night_outlined"
    MODE_OF_TRAVEL = "mode_of_travel"
    MODE_OF_TRAVEL_SHARP = "mode_of_travel_sharp"
    MODE_OF_TRAVEL_ROUNDED = "mode_of_travel_rounded"
    MODE_OF_TRAVEL_OUTLINED = "mode_of_travel_outlined"
    MODE_STANDBY = "mode_standby"
    MODE_STANDBY_SHARP = "mode_standby_sharp"
    MODE_STANDBY_ROUNDED = "mode_standby_rounded"
    MODE_STANDBY_OUTLINED = "mode_standby_outlined"
    MODEL_TRAINING = "model_training"
    MODEL_TRAINING_SHARP = "model_training_sharp"
    MODEL_TRAINING_ROUNDED = "model_training_rounded"
    MODEL_TRAINING_OUTLINED = "model_training_outlined"
    MONETIZATION_ON = "monetization_on"
    MONETIZATION_ON_SHARP = "monetization_on_sharp"
    MONETIZATION_ON_ROUNDED = "monetization_on_rounded"
    MONETIZATION_ON_OUTLINED = "monetization_on_outlined"
    MONEY = "money"
    MONEY_SHARP = "money_sharp"
    MONEY_ROUNDED = "money_rounded"
    MONEY_OUTLINED = "money_outlined"
    MONEY_OFF = "money_off"
    MONEY_OFF_SHARP = "money_off_sharp"
    MONEY_OFF_ROUNDED = "money_off_rounded"
    MONEY_OFF_OUTLINED = "money_off_outlined"
    MONEY_OFF_CSRED = "money_off_csred"
    MONEY_OFF_CSRED_SHARP = "money_off_csred_sharp"
    MONEY_OFF_CSRED_ROUNDED = "money_off_csred_rounded"
    MONEY_OFF_CSRED_OUTLINED = "money_off_csred_outlined"
    MONITOR = "monitor"
    MONITOR_SHARP = "monitor_sharp"
    MONITOR_ROUNDED = "monitor_rounded"
    MONITOR_OUTLINED = "monitor_outlined"
    MONITOR_HEART = "monitor_heart"
    MONITOR_HEART_SHARP = "monitor_heart_sharp"
    MONITOR_HEART_ROUNDED = "monitor_heart_rounded"
    MONITOR_HEART_OUTLINED = "monitor_heart_outlined"
    MONITOR_WEIGHT = "monitor_weight"
    MONITOR_WEIGHT_SHARP = "monitor_weight_sharp"
    MONITOR_WEIGHT_ROUNDED = "monitor_weight_rounded"
    MONITOR_WEIGHT_OUTLINED = "monitor_weight_outlined"
    MONOCHROME_PHOTOS = "monochrome_photos"
    MONOCHROME_PHOTOS_SHARP = "monochrome_photos_sharp"
    MONOCHROME_PHOTOS_ROUNDED = "monochrome_photos_rounded"
    MONOCHROME_PHOTOS_OUTLINED = "monochrome_photos_outlined"
    MOOD = "mood"
    MOOD_SHARP = "mood_sharp"
    MOOD_ROUNDED = "mood_rounded"
    MOOD_OUTLINED = "mood_outlined"
    MOOD_BAD = "mood_bad"
    MOOD_BAD_SHARP = "mood_bad_sharp"
    MOOD_BAD_ROUNDED = "mood_bad_rounded"
    MOOD_BAD_OUTLINED = "mood_bad_outlined"
    MOPED = "moped"
    MOPED_SHARP = "moped_sharp"
    MOPED_ROUNDED = "moped_rounded"
    MOPED_OUTLINED = "moped_outlined"
    MORE = "more"
    MORE_SHARP = "more_sharp"
    MORE_ROUNDED = "more_rounded"
    MORE_OUTLINED = "more_outlined"
    MORE_HORIZ = "more_horiz"
    MORE_HORIZ_SHARP = "more_horiz_sharp"
    MORE_HORIZ_ROUNDED = "more_horiz_rounded"
    MORE_HORIZ_OUTLINED = "more_horiz_outlined"
    MORE_TIME = "more_time"
    MORE_TIME_SHARP = "more_time_sharp"
    MORE_TIME_ROUNDED = "more_time_rounded"
    MORE_TIME_OUTLINED = "more_time_outlined"
    MORE_VERT = "more_vert"
    MORE_VERT_SHARP = "more_vert_sharp"
    MORE_VERT_ROUNDED = "more_vert_rounded"
    MORE_VERT_OUTLINED = "more_vert_outlined"
    MOSQUE = "mosque"
    MOSQUE_SHARP = "mosque_sharp"
    MOSQUE_ROUNDED = "mosque_rounded"
    MOSQUE_OUTLINED = "mosque_outlined"
    MOTION_PHOTOS_AUTO = "motion_photos_auto"
    MOTION_PHOTOS_AUTO_SHARP = "motion_photos_auto_sharp"
    MOTION_PHOTOS_AUTO_ROUNDED = "motion_photos_auto_rounded"
    MOTION_PHOTOS_AUTO_OUTLINED = "motion_photos_auto_outlined"
    MOTION_PHOTOS_OFF = "motion_photos_off"
    MOTION_PHOTOS_OFF_SHARP = "motion_photos_off_sharp"
    MOTION_PHOTOS_OFF_ROUNDED = "motion_photos_off_rounded"
    MOTION_PHOTOS_OFF_OUTLINED = "motion_photos_off_outlined"
    MOTION_PHOTOS_ON = "motion_photos_on"
    MOTION_PHOTOS_ON_SHARP = "motion_photos_on_sharp"
    MOTION_PHOTOS_ON_ROUNDED = "motion_photos_on_rounded"
    MOTION_PHOTOS_ON_OUTLINED = "motion_photos_on_outlined"
    MOTION_PHOTOS_PAUSE = "motion_photos_pause"
    MOTION_PHOTOS_PAUSE_SHARP = "motion_photos_pause_sharp"
    MOTION_PHOTOS_PAUSE_ROUNDED = "motion_photos_pause_rounded"
    MOTION_PHOTOS_PAUSE_OUTLINED = "motion_photos_pause_outlined"
    MOTION_PHOTOS_PAUSED = "motion_photos_paused"
    MOTION_PHOTOS_PAUSED_SHARP = "motion_photos_paused_sharp"
    MOTION_PHOTOS_PAUSED_ROUNDED = "motion_photos_paused_rounded"
    MOTION_PHOTOS_PAUSED_OUTLINED = "motion_photos_paused_outlined"
    MOTORCYCLE = "motorcycle"
    MOTORCYCLE_SHARP = "motorcycle_sharp"
    MOTORCYCLE_ROUNDED = "motorcycle_rounded"
    MOTORCYCLE_OUTLINED = "motorcycle_outlined"
    MOUSE = "mouse"
    MOUSE_SHARP = "mouse_sharp"
    MOUSE_ROUNDED = "mouse_rounded"
    MOUSE_OUTLINED = "mouse_outlined"
    MOVE_DOWN = "move_down"
    MOVE_DOWN_SHARP = "move_down_sharp"
    MOVE_DOWN_ROUNDED = "move_down_rounded"
    MOVE_DOWN_OUTLINED = "move_down_outlined"
    MOVE_TO_INBOX = "move_to_inbox"
    MOVE_TO_INBOX_SHARP = "move_to_inbox_sharp"
    MOVE_TO_INBOX_ROUNDED = "move_to_inbox_rounded"
    MOVE_TO_INBOX_OUTLINED = "move_to_inbox_outlined"
    MOVE_UP = "move_up"
    MOVE_UP_SHARP = "move_up_sharp"
    MOVE_UP_ROUNDED = "move_up_rounded"
    MOVE_UP_OUTLINED = "move_up_outlined"
    MOVIE = "movie"
    MOVIE_SHARP = "movie_sharp"
    MOVIE_ROUNDED = "movie_rounded"
    MOVIE_OUTLINED = "movie_outlined"
    MOVIE_CREATION = "movie_creation"
    MOVIE_CREATION_SHARP = "movie_creation_sharp"
    MOVIE_CREATION_ROUNDED = "movie_creation_rounded"
    MOVIE_CREATION_OUTLINED = "movie_creation_outlined"
    MOVIE_EDIT = "movie_edit"
    MOVIE_FILTER = "movie_filter"
    MOVIE_FILTER_SHARP = "movie_filter_sharp"
    MOVIE_FILTER_ROUNDED = "movie_filter_rounded"
    MOVIE_FILTER_OUTLINED = "movie_filter_outlined"
    MOVING = "moving"
    MOVING_SHARP = "moving_sharp"
    MOVING_ROUNDED = "moving_rounded"
    MOVING_OUTLINED = "moving_outlined"
    MP = "mp"
    MP_SHARP = "mp_sharp"
    MP_ROUNDED = "mp_rounded"
    MP_OUTLINED = "mp_outlined"
    MULTILINE_CHART = "multiline_chart"
    MULTILINE_CHART_SHARP = "multiline_chart_sharp"
    MULTILINE_CHART_ROUNDED = "multiline_chart_rounded"
    MULTILINE_CHART_OUTLINED = "multiline_chart_outlined"
    MULTIPLE_STOP = "multiple_stop"
    MULTIPLE_STOP_SHARP = "multiple_stop_sharp"
    MULTIPLE_STOP_ROUNDED = "multiple_stop_rounded"
    MULTIPLE_STOP_OUTLINED = "multiple_stop_outlined"
    MULTITRACK_AUDIO = "multitrack_audio"
    MULTITRACK_AUDIO_SHARP = "multitrack_audio_sharp"
    MULTITRACK_AUDIO_ROUNDED = "multitrack_audio_rounded"
    MULTITRACK_AUDIO_OUTLINED = "multitrack_audio_outlined"
    MUSEUM = "museum"
    MUSEUM_SHARP = "museum_sharp"
    MUSEUM_ROUNDED = "museum_rounded"
    MUSEUM_OUTLINED = "museum_outlined"
    MUSIC_NOTE = "music_note"
    MUSIC_NOTE_SHARP = "music_note_sharp"
    MUSIC_NOTE_ROUNDED = "music_note_rounded"
    MUSIC_NOTE_OUTLINED = "music_note_outlined"
    MUSIC_OFF = "music_off"
    MUSIC_OFF_SHARP = "music_off_sharp"
    MUSIC_OFF_ROUNDED = "music_off_rounded"
    MUSIC_OFF_OUTLINED = "music_off_outlined"
    MUSIC_VIDEO = "music_video"
    MUSIC_VIDEO_SHARP = "music_video_sharp"
    MUSIC_VIDEO_ROUNDED = "music_video_rounded"
    MUSIC_VIDEO_OUTLINED = "music_video_outlined"
    MY_LIBRARY_ADD = "my_library_add"
    MY_LIBRARY_ADD_SHARP = "my_library_add_sharp"
    MY_LIBRARY_ADD_ROUNDED = "my_library_add_rounded"
    MY_LIBRARY_ADD_OUTLINED = "my_library_add_outlined"
    MY_LIBRARY_BOOKS = "my_library_books"
    MY_LIBRARY_BOOKS_SHARP = "my_library_books_sharp"
    MY_LIBRARY_BOOKS_ROUNDED = "my_library_books_rounded"
    MY_LIBRARY_BOOKS_OUTLINED = "my_library_books_outlined"
    MY_LIBRARY_MUSIC = "my_library_music"
    MY_LIBRARY_MUSIC_SHARP = "my_library_music_sharp"
    MY_LIBRARY_MUSIC_ROUNDED = "my_library_music_rounded"
    MY_LIBRARY_MUSIC_OUTLINED = "my_library_music_outlined"
    MY_LOCATION = "my_location"
    MY_LOCATION_SHARP = "my_location_sharp"
    MY_LOCATION_ROUNDED = "my_location_rounded"
    MY_LOCATION_OUTLINED = "my_location_outlined"
    NAT = "nat"
    NAT_SHARP = "nat_sharp"
    NAT_ROUNDED = "nat_rounded"
    NAT_OUTLINED = "nat_outlined"
    NATURE = "nature"
    NATURE_SHARP = "nature_sharp"
    NATURE_ROUNDED = "nature_rounded"
    NATURE_OUTLINED = "nature_outlined"
    NATURE_PEOPLE = "nature_people"
    NATURE_PEOPLE_SHARP = "nature_people_sharp"
    NATURE_PEOPLE_ROUNDED = "nature_people_rounded"
    NATURE_PEOPLE_OUTLINED = "nature_people_outlined"
    NAVIGATE_BEFORE = "navigate_before"
    NAVIGATE_BEFORE_SHARP = "navigate_before_sharp"
    NAVIGATE_BEFORE_ROUNDED = "navigate_before_rounded"
    NAVIGATE_BEFORE_OUTLINED = "navigate_before_outlined"
    NAVIGATE_NEXT = "navigate_next"
    NAVIGATE_NEXT_SHARP = "navigate_next_sharp"
    NAVIGATE_NEXT_ROUNDED = "navigate_next_rounded"
    NAVIGATE_NEXT_OUTLINED = "navigate_next_outlined"
    NAVIGATION = "navigation"
    NAVIGATION_SHARP = "navigation_sharp"
    NAVIGATION_ROUNDED = "navigation_rounded"
    NAVIGATION_OUTLINED = "navigation_outlined"
    NEAR_ME = "near_me"
    NEAR_ME_SHARP = "near_me_sharp"
    NEAR_ME_ROUNDED = "near_me_rounded"
    NEAR_ME_OUTLINED = "near_me_outlined"
    NEAR_ME_DISABLED = "near_me_disabled"
    NEAR_ME_DISABLED_SHARP = "near_me_disabled_sharp"
    NEAR_ME_DISABLED_ROUNDED = "near_me_disabled_rounded"
    NEAR_ME_DISABLED_OUTLINED = "near_me_disabled_outlined"
    NEARBY_ERROR = "nearby_error"
    NEARBY_ERROR_SHARP = "nearby_error_sharp"
    NEARBY_ERROR_ROUNDED = "nearby_error_rounded"
    NEARBY_ERROR_OUTLINED = "nearby_error_outlined"
    NEARBY_OFF = "nearby_off"
    NEARBY_OFF_SHARP = "nearby_off_sharp"
    NEARBY_OFF_ROUNDED = "nearby_off_rounded"
    NEARBY_OFF_OUTLINED = "nearby_off_outlined"
    NEST_CAM_WIRED_STAND = "nest_cam_wired_stand"
    NEST_CAM_WIRED_STAND_SHARP = "nest_cam_wired_stand_sharp"
    NEST_CAM_WIRED_STAND_ROUNDED = "nest_cam_wired_stand_rounded"
    NEST_CAM_WIRED_STAND_OUTLINED = "nest_cam_wired_stand_outlined"
    NETWORK_CELL = "network_cell"
    NETWORK_CELL_SHARP = "network_cell_sharp"
    NETWORK_CELL_ROUNDED = "network_cell_rounded"
    NETWORK_CELL_OUTLINED = "network_cell_outlined"
    NETWORK_CHECK = "network_check"
    NETWORK_CHECK_SHARP = "network_check_sharp"
    NETWORK_CHECK_ROUNDED = "network_check_rounded"
    NETWORK_CHECK_OUTLINED = "network_check_outlined"
    NETWORK_LOCKED = "network_locked"
    NETWORK_LOCKED_SHARP = "network_locked_sharp"
    NETWORK_LOCKED_ROUNDED = "network_locked_rounded"
    NETWORK_LOCKED_OUTLINED = "network_locked_outlined"
    NETWORK_PING = "network_ping"
    NETWORK_PING_SHARP = "network_ping_sharp"
    NETWORK_PING_ROUNDED = "network_ping_rounded"
    NETWORK_PING_OUTLINED = "network_ping_outlined"
    NETWORK_WIFI = "network_wifi"
    NETWORK_WIFI_SHARP = "network_wifi_sharp"
    NETWORK_WIFI_ROUNDED = "network_wifi_rounded"
    NETWORK_WIFI_OUTLINED = "network_wifi_outlined"
    NETWORK_WIFI_1_BAR = "network_wifi_1_bar"
    NETWORK_WIFI_1_BAR_SHARP = "network_wifi_1_bar_sharp"
    NETWORK_WIFI_1_BAR_ROUNDED = "network_wifi_1_bar_rounded"
    NETWORK_WIFI_1_BAR_OUTLINED = "network_wifi_1_bar_outlined"
    NETWORK_WIFI_2_BAR = "network_wifi_2_bar"
    NETWORK_WIFI_2_BAR_SHARP = "network_wifi_2_bar_sharp"
    NETWORK_WIFI_2_BAR_ROUNDED = "network_wifi_2_bar_rounded"
    NETWORK_WIFI_2_BAR_OUTLINED = "network_wifi_2_bar_outlined"
    NETWORK_WIFI_3_BAR = "network_wifi_3_bar"
    NETWORK_WIFI_3_BAR_SHARP = "network_wifi_3_bar_sharp"
    NETWORK_WIFI_3_BAR_ROUNDED = "network_wifi_3_bar_rounded"
    NETWORK_WIFI_3_BAR_OUTLINED = "network_wifi_3_bar_outlined"
    NEW_LABEL = "new_label"
    NEW_LABEL_SHARP = "new_label_sharp"
    NEW_LABEL_ROUNDED = "new_label_rounded"
    NEW_LABEL_OUTLINED = "new_label_outlined"
    NEW_RELEASES = "new_releases"
    NEW_RELEASES_SHARP = "new_releases_sharp"
    NEW_RELEASES_ROUNDED = "new_releases_rounded"
    NEW_RELEASES_OUTLINED = "new_releases_outlined"
    NEWSPAPER = "newspaper"
    NEWSPAPER_SHARP = "newspaper_sharp"
    NEWSPAPER_ROUNDED = "newspaper_rounded"
    NEWSPAPER_OUTLINED = "newspaper_outlined"
    NEXT_PLAN = "next_plan"
    NEXT_PLAN_SHARP = "next_plan_sharp"
    NEXT_PLAN_ROUNDED = "next_plan_rounded"
    NEXT_PLAN_OUTLINED = "next_plan_outlined"
    NEXT_WEEK = "next_week"
    NEXT_WEEK_SHARP = "next_week_sharp"
    NEXT_WEEK_ROUNDED = "next_week_rounded"
    NEXT_WEEK_OUTLINED = "next_week_outlined"
    NFC = "nfc"
    NFC_SHARP = "nfc_sharp"
    NFC_ROUNDED = "nfc_rounded"
    NFC_OUTLINED = "nfc_outlined"
    NIGHT_SHELTER = "night_shelter"
    NIGHT_SHELTER_SHARP = "night_shelter_sharp"
    NIGHT_SHELTER_ROUNDED = "night_shelter_rounded"
    NIGHT_SHELTER_OUTLINED = "night_shelter_outlined"
    NIGHTLIFE = "nightlife"
    NIGHTLIFE_SHARP = "nightlife_sharp"
    NIGHTLIFE_ROUNDED = "nightlife_rounded"
    NIGHTLIFE_OUTLINED = "nightlife_outlined"
    NIGHTLIGHT = "nightlight"
    NIGHTLIGHT_SHARP = "nightlight_sharp"
    NIGHTLIGHT_ROUNDED = "nightlight_rounded"
    NIGHTLIGHT_OUTLINED = "nightlight_outlined"
    NIGHTLIGHT_ROUND = "nightlight_round"
    NIGHTLIGHT_ROUND_SHARP = "nightlight_round_sharp"
    NIGHTLIGHT_ROUND_ROUNDED = "nightlight_round_rounded"
    NIGHTLIGHT_ROUND_OUTLINED = "nightlight_round_outlined"
    NIGHTS_STAY = "nights_stay"
    NIGHTS_STAY_SHARP = "nights_stay_sharp"
    NIGHTS_STAY_ROUNDED = "nights_stay_rounded"
    NIGHTS_STAY_OUTLINED = "nights_stay_outlined"
    NO_ACCOUNTS = "no_accounts"
    NO_ACCOUNTS_SHARP = "no_accounts_sharp"
    NO_ACCOUNTS_ROUNDED = "no_accounts_rounded"
    NO_ACCOUNTS_OUTLINED = "no_accounts_outlined"
    NO_ADULT_CONTENT = "no_adult_content"
    NO_ADULT_CONTENT_SHARP = "no_adult_content_sharp"
    NO_ADULT_CONTENT_ROUNDED = "no_adult_content_rounded"
    NO_ADULT_CONTENT_OUTLINED = "no_adult_content_outlined"
    NO_BACKPACK = "no_backpack"
    NO_BACKPACK_SHARP = "no_backpack_sharp"
    NO_BACKPACK_ROUNDED = "no_backpack_rounded"
    NO_BACKPACK_OUTLINED = "no_backpack_outlined"
    NO_CELL = "no_cell"
    NO_CELL_SHARP = "no_cell_sharp"
    NO_CELL_ROUNDED = "no_cell_rounded"
    NO_CELL_OUTLINED = "no_cell_outlined"
    NO_CRASH = "no_crash"
    NO_CRASH_SHARP = "no_crash_sharp"
    NO_CRASH_ROUNDED = "no_crash_rounded"
    NO_CRASH_OUTLINED = "no_crash_outlined"
    NO_DRINKS = "no_drinks"
    NO_DRINKS_SHARP = "no_drinks_sharp"
    NO_DRINKS_ROUNDED = "no_drinks_rounded"
    NO_DRINKS_OUTLINED = "no_drinks_outlined"
    NO_ENCRYPTION = "no_encryption"
    NO_ENCRYPTION_SHARP = "no_encryption_sharp"
    NO_ENCRYPTION_ROUNDED = "no_encryption_rounded"
    NO_ENCRYPTION_OUTLINED = "no_encryption_outlined"
    NO_ENCRYPTION_GMAILERRORRED = "no_encryption_gmailerrorred"
    NO_ENCRYPTION_GMAILERRORRED_SHARP = "no_encryption_gmailerrorred_sharp"
    NO_ENCRYPTION_GMAILERRORRED_ROUNDED = "no_encryption_gmailerrorred_rounded"
    NO_ENCRYPTION_GMAILERRORRED_OUTLINED = "no_encryption_gmailerrorred_outlined"
    NO_FLASH = "no_flash"
    NO_FLASH_SHARP = "no_flash_sharp"
    NO_FLASH_ROUNDED = "no_flash_rounded"
    NO_FLASH_OUTLINED = "no_flash_outlined"
    NO_FOOD = "no_food"
    NO_FOOD_SHARP = "no_food_sharp"
    NO_FOOD_ROUNDED = "no_food_rounded"
    NO_FOOD_OUTLINED = "no_food_outlined"
    NO_LUGGAGE = "no_luggage"
    NO_LUGGAGE_SHARP = "no_luggage_sharp"
    NO_LUGGAGE_ROUNDED = "no_luggage_rounded"
    NO_LUGGAGE_OUTLINED = "no_luggage_outlined"
    NO_MEALS = "no_meals"
    NO_MEALS_SHARP = "no_meals_sharp"
    NO_MEALS_ROUNDED = "no_meals_rounded"
    NO_MEALS_OUTLINED = "no_meals_outlined"
    NO_MEALS_OULINE = "no_meals_ouline"
    NO_MEETING_ROOM = "no_meeting_room"
    NO_MEETING_ROOM_SHARP = "no_meeting_room_sharp"
    NO_MEETING_ROOM_ROUNDED = "no_meeting_room_rounded"
    NO_MEETING_ROOM_OUTLINED = "no_meeting_room_outlined"
    NO_PHOTOGRAPHY = "no_photography"
    NO_PHOTOGRAPHY_SHARP = "no_photography_sharp"
    NO_PHOTOGRAPHY_ROUNDED = "no_photography_rounded"
    NO_PHOTOGRAPHY_OUTLINED = "no_photography_outlined"
    NO_SIM = "no_sim"
    NO_SIM_SHARP = "no_sim_sharp"
    NO_SIM_ROUNDED = "no_sim_rounded"
    NO_SIM_OUTLINED = "no_sim_outlined"
    NO_STROLLER = "no_stroller"
    NO_STROLLER_SHARP = "no_stroller_sharp"
    NO_STROLLER_ROUNDED = "no_stroller_rounded"
    NO_STROLLER_OUTLINED = "no_stroller_outlined"
    NO_TRANSFER = "no_transfer"
    NO_TRANSFER_SHARP = "no_transfer_sharp"
    NO_TRANSFER_ROUNDED = "no_transfer_rounded"
    NO_TRANSFER_OUTLINED = "no_transfer_outlined"
    NOISE_AWARE = "noise_aware"
    NOISE_AWARE_SHARP = "noise_aware_sharp"
    NOISE_AWARE_ROUNDED = "noise_aware_rounded"
    NOISE_AWARE_OUTLINED = "noise_aware_outlined"
    NOISE_CONTROL_OFF = "noise_control_off"
    NOISE_CONTROL_OFF_SHARP = "noise_control_off_sharp"
    NOISE_CONTROL_OFF_ROUNDED = "noise_control_off_rounded"
    NOISE_CONTROL_OFF_OUTLINED = "noise_control_off_outlined"
    NORDIC_WALKING = "nordic_walking"
    NORDIC_WALKING_SHARP = "nordic_walking_sharp"
    NORDIC_WALKING_ROUNDED = "nordic_walking_rounded"
    NORDIC_WALKING_OUTLINED = "nordic_walking_outlined"
    NORTH = "north"
    NORTH_SHARP = "north_sharp"
    NORTH_ROUNDED = "north_rounded"
    NORTH_OUTLINED = "north_outlined"
    NORTH_EAST = "north_east"
    NORTH_EAST_SHARP = "north_east_sharp"
    NORTH_EAST_ROUNDED = "north_east_rounded"
    NORTH_EAST_OUTLINED = "north_east_outlined"
    NORTH_WEST = "north_west"
    NORTH_WEST_SHARP = "north_west_sharp"
    NORTH_WEST_ROUNDED = "north_west_rounded"
    NORTH_WEST_OUTLINED = "north_west_outlined"
    NOT_ACCESSIBLE = "not_accessible"
    NOT_ACCESSIBLE_SHARP = "not_accessible_sharp"
    NOT_ACCESSIBLE_ROUNDED = "not_accessible_rounded"
    NOT_ACCESSIBLE_OUTLINED = "not_accessible_outlined"
    NOT_INTERESTED = "not_interested"
    NOT_INTERESTED_SHARP = "not_interested_sharp"
    NOT_INTERESTED_ROUNDED = "not_interested_rounded"
    NOT_INTERESTED_OUTLINED = "not_interested_outlined"
    NOT_LISTED_LOCATION = "not_listed_location"
    NOT_LISTED_LOCATION_SHARP = "not_listed_location_sharp"
    NOT_LISTED_LOCATION_ROUNDED = "not_listed_location_rounded"
    NOT_LISTED_LOCATION_OUTLINED = "not_listed_location_outlined"
    NOT_STARTED = "not_started"
    NOT_STARTED_SHARP = "not_started_sharp"
    NOT_STARTED_ROUNDED = "not_started_rounded"
    NOT_STARTED_OUTLINED = "not_started_outlined"
    NOTE = "note"
    NOTE_SHARP = "note_sharp"
    NOTE_ROUNDED = "note_rounded"
    NOTE_OUTLINED = "note_outlined"
    NOTE_ADD = "note_add"
    NOTE_ADD_SHARP = "note_add_sharp"
    NOTE_ADD_ROUNDED = "note_add_rounded"
    NOTE_ADD_OUTLINED = "note_add_outlined"
    NOTE_ALT = "note_alt"
    NOTE_ALT_SHARP = "note_alt_sharp"
    NOTE_ALT_ROUNDED = "note_alt_rounded"
    NOTE_ALT_OUTLINED = "note_alt_outlined"
    NOTES = "notes"
    NOTES_SHARP = "notes_sharp"
    NOTES_ROUNDED = "notes_rounded"
    NOTES_OUTLINED = "notes_outlined"
    NOTIFICATION_ADD = "notification_add"
    NOTIFICATION_ADD_SHARP = "notification_add_sharp"
    NOTIFICATION_ADD_ROUNDED = "notification_add_rounded"
    NOTIFICATION_ADD_OUTLINED = "notification_add_outlined"
    NOTIFICATION_IMPORTANT = "notification_important"
    NOTIFICATION_IMPORTANT_SHARP = "notification_important_sharp"
    NOTIFICATION_IMPORTANT_ROUNDED = "notification_important_rounded"
    NOTIFICATION_IMPORTANT_OUTLINED = "notification_important_outlined"
    NOTIFICATIONS = "notifications"
    NOTIFICATIONS_SHARP = "notifications_sharp"
    NOTIFICATIONS_ROUNDED = "notifications_rounded"
    NOTIFICATIONS_OUTLINED = "notifications_outlined"
    NOTIFICATIONS_ACTIVE = "notifications_active"
    NOTIFICATIONS_ACTIVE_SHARP = "notifications_active_sharp"
    NOTIFICATIONS_ACTIVE_ROUNDED = "notifications_active_rounded"
    NOTIFICATIONS_ACTIVE_OUTLINED = "notifications_active_outlined"
    NOTIFICATIONS_NONE = "notifications_none"
    NOTIFICATIONS_NONE_SHARP = "notifications_none_sharp"
    NOTIFICATIONS_NONE_ROUNDED = "notifications_none_rounded"
    NOTIFICATIONS_NONE_OUTLINED = "notifications_none_outlined"
    NOTIFICATIONS_OFF = "notifications_off"
    NOTIFICATIONS_OFF_SHARP = "notifications_off_sharp"
    NOTIFICATIONS_OFF_ROUNDED = "notifications_off_rounded"
    NOTIFICATIONS_OFF_OUTLINED = "notifications_off_outlined"
    NOTIFICATIONS_ON = "notifications_on"
    NOTIFICATIONS_ON_SHARP = "notifications_on_sharp"
    NOTIFICATIONS_ON_ROUNDED = "notifications_on_rounded"
    NOTIFICATIONS_ON_OUTLINED = "notifications_on_outlined"
    NOTIFICATIONS_PAUSED = "notifications_paused"
    NOTIFICATIONS_PAUSED_SHARP = "notifications_paused_sharp"
    NOTIFICATIONS_PAUSED_ROUNDED = "notifications_paused_rounded"
    NOTIFICATIONS_PAUSED_OUTLINED = "notifications_paused_outlined"
    NOW_WALLPAPER = "now_wallpaper"
    NOW_WALLPAPER_SHARP = "now_wallpaper_sharp"
    NOW_WALLPAPER_ROUNDED = "now_wallpaper_rounded"
    NOW_WALLPAPER_OUTLINED = "now_wallpaper_outlined"
    NOW_WIDGETS = "now_widgets"
    NOW_WIDGETS_SHARP = "now_widgets_sharp"
    NOW_WIDGETS_ROUNDED = "now_widgets_rounded"
    NOW_WIDGETS_OUTLINED = "now_widgets_outlined"
    NUMBERS = "numbers"
    NUMBERS_SHARP = "numbers_sharp"
    NUMBERS_ROUNDED = "numbers_rounded"
    NUMBERS_OUTLINED = "numbers_outlined"
    OFFLINE_BOLT = "offline_bolt"
    OFFLINE_BOLT_SHARP = "offline_bolt_sharp"
    OFFLINE_BOLT_ROUNDED = "offline_bolt_rounded"
    OFFLINE_BOLT_OUTLINED = "offline_bolt_outlined"
    OFFLINE_PIN = "offline_pin"
    OFFLINE_PIN_SHARP = "offline_pin_sharp"
    OFFLINE_PIN_ROUNDED = "offline_pin_rounded"
    OFFLINE_PIN_OUTLINED = "offline_pin_outlined"
    OFFLINE_SHARE = "offline_share"
    OFFLINE_SHARE_SHARP = "offline_share_sharp"
    OFFLINE_SHARE_ROUNDED = "offline_share_rounded"
    OFFLINE_SHARE_OUTLINED = "offline_share_outlined"
    OIL_BARREL = "oil_barrel"
    OIL_BARREL_SHARP = "oil_barrel_sharp"
    OIL_BARREL_ROUNDED = "oil_barrel_rounded"
    OIL_BARREL_OUTLINED = "oil_barrel_outlined"
    ON_DEVICE_TRAINING = "on_device_training"
    ON_DEVICE_TRAINING_SHARP = "on_device_training_sharp"
    ON_DEVICE_TRAINING_ROUNDED = "on_device_training_rounded"
    ON_DEVICE_TRAINING_OUTLINED = "on_device_training_outlined"
    ONDEMAND_VIDEO = "ondemand_video"
    ONDEMAND_VIDEO_SHARP = "ondemand_video_sharp"
    ONDEMAND_VIDEO_ROUNDED = "ondemand_video_rounded"
    ONDEMAND_VIDEO_OUTLINED = "ondemand_video_outlined"
    ONLINE_PREDICTION = "online_prediction"
    ONLINE_PREDICTION_SHARP = "online_prediction_sharp"
    ONLINE_PREDICTION_ROUNDED = "online_prediction_rounded"
    ONLINE_PREDICTION_OUTLINED = "online_prediction_outlined"
    OPACITY = "opacity"
    OPACITY_SHARP = "opacity_sharp"
    OPACITY_ROUNDED = "opacity_rounded"
    OPACITY_OUTLINED = "opacity_outlined"
    OPEN_IN_BROWSER = "open_in_browser"
    OPEN_IN_BROWSER_SHARP = "open_in_browser_sharp"
    OPEN_IN_BROWSER_ROUNDED = "open_in_browser_rounded"
    OPEN_IN_BROWSER_OUTLINED = "open_in_browser_outlined"
    OPEN_IN_FULL = "open_in_full"
    OPEN_IN_FULL_SHARP = "open_in_full_sharp"
    OPEN_IN_FULL_ROUNDED = "open_in_full_rounded"
    OPEN_IN_FULL_OUTLINED = "open_in_full_outlined"
    OPEN_IN_NEW = "open_in_new"
    OPEN_IN_NEW_SHARP = "open_in_new_sharp"
    OPEN_IN_NEW_ROUNDED = "open_in_new_rounded"
    OPEN_IN_NEW_OUTLINED = "open_in_new_outlined"
    OPEN_IN_NEW_OFF = "open_in_new_off"
    OPEN_IN_NEW_OFF_SHARP = "open_in_new_off_sharp"
    OPEN_IN_NEW_OFF_ROUNDED = "open_in_new_off_rounded"
    OPEN_IN_NEW_OFF_OUTLINED = "open_in_new_off_outlined"
    OPEN_WITH = "open_with"
    OPEN_WITH_SHARP = "open_with_sharp"
    OPEN_WITH_ROUNDED = "open_with_rounded"
    OPEN_WITH_OUTLINED = "open_with_outlined"
    OTHER_HOUSES = "other_houses"
    OTHER_HOUSES_SHARP = "other_houses_sharp"
    OTHER_HOUSES_ROUNDED = "other_houses_rounded"
    OTHER_HOUSES_OUTLINED = "other_houses_outlined"
    OUTBOND = "outbond"
    OUTBOND_SHARP = "outbond_sharp"
    OUTBOND_ROUNDED = "outbond_rounded"
    OUTBOND_OUTLINED = "outbond_outlined"
    OUTBOUND = "outbound"
    OUTBOUND_SHARP = "outbound_sharp"
    OUTBOUND_ROUNDED = "outbound_rounded"
    OUTBOUND_OUTLINED = "outbound_outlined"
    OUTBOX = "outbox"
    OUTBOX_SHARP = "outbox_sharp"
    OUTBOX_ROUNDED = "outbox_rounded"
    OUTBOX_OUTLINED = "outbox_outlined"
    OUTDOOR_GRILL = "outdoor_grill"
    OUTDOOR_GRILL_SHARP = "outdoor_grill_sharp"
    OUTDOOR_GRILL_ROUNDED = "outdoor_grill_rounded"
    OUTDOOR_GRILL_OUTLINED = "outdoor_grill_outlined"
    OUTGOING_MAIL = "outgoing_mail"
    OUTLET = "outlet"
    OUTLET_SHARP = "outlet_sharp"
    OUTLET_ROUNDED = "outlet_rounded"
    OUTLET_OUTLINED = "outlet_outlined"
    OUTLINED_FLAG = "outlined_flag"
    OUTLINED_FLAG_SHARP = "outlined_flag_sharp"
    OUTLINED_FLAG_ROUNDED = "outlined_flag_rounded"
    OUTLINED_FLAG_OUTLINED = "outlined_flag_outlined"
    OUTPUT = "output"
    OUTPUT_SHARP = "output_sharp"
    OUTPUT_ROUNDED = "output_rounded"
    OUTPUT_OUTLINED = "output_outlined"
    PADDING = "padding"
    PADDING_SHARP = "padding_sharp"
    PADDING_ROUNDED = "padding_rounded"
    PADDING_OUTLINED = "padding_outlined"
    PAGES = "pages"
    PAGES_SHARP = "pages_sharp"
    PAGES_ROUNDED = "pages_rounded"
    PAGES_OUTLINED = "pages_outlined"
    PAGEVIEW = "pageview"
    PAGEVIEW_SHARP = "pageview_sharp"
    PAGEVIEW_ROUNDED = "pageview_rounded"
    PAGEVIEW_OUTLINED = "pageview_outlined"
    PAID = "paid"
    PAID_SHARP = "paid_sharp"
    PAID_ROUNDED = "paid_rounded"
    PAID_OUTLINED = "paid_outlined"
    PALETTE = "palette"
    PALETTE_SHARP = "palette_sharp"
    PALETTE_ROUNDED = "palette_rounded"
    PALETTE_OUTLINED = "palette_outlined"
    PALLET = "pallet"
    PAN_TOOL = "pan_tool"
    PAN_TOOL_SHARP = "pan_tool_sharp"
    PAN_TOOL_ROUNDED = "pan_tool_rounded"
    PAN_TOOL_OUTLINED = "pan_tool_outlined"
    PAN_TOOL_ALT = "pan_tool_alt"
    PAN_TOOL_ALT_SHARP = "pan_tool_alt_sharp"
    PAN_TOOL_ALT_ROUNDED = "pan_tool_alt_rounded"
    PAN_TOOL_ALT_OUTLINED = "pan_tool_alt_outlined"
    PANORAMA = "panorama"
    PANORAMA_SHARP = "panorama_sharp"
    PANORAMA_ROUNDED = "panorama_rounded"
    PANORAMA_OUTLINED = "panorama_outlined"
    PANORAMA_FISH_EYE = "panorama_fish_eye"
    PANORAMA_FISH_EYE_SHARP = "panorama_fish_eye_sharp"
    PANORAMA_FISH_EYE_ROUNDED = "panorama_fish_eye_rounded"
    PANORAMA_FISH_EYE_OUTLINED = "panorama_fish_eye_outlined"
    PANORAMA_FISHEYE = "panorama_fisheye"
    PANORAMA_FISHEYE_SHARP = "panorama_fisheye_sharp"
    PANORAMA_FISHEYE_ROUNDED = "panorama_fisheye_rounded"
    PANORAMA_FISHEYE_OUTLINED = "panorama_fisheye_outlined"
    PANORAMA_HORIZONTAL = "panorama_horizontal"
    PANORAMA_HORIZONTAL_SHARP = "panorama_horizontal_sharp"
    PANORAMA_HORIZONTAL_ROUNDED = "panorama_horizontal_rounded"
    PANORAMA_HORIZONTAL_OUTLINED = "panorama_horizontal_outlined"
    PANORAMA_HORIZONTAL_SELECT = "panorama_horizontal_select"
    PANORAMA_HORIZONTAL_SELECT_SHARP = "panorama_horizontal_select_sharp"
    PANORAMA_HORIZONTAL_SELECT_ROUNDED = "panorama_horizontal_select_rounded"
    PANORAMA_HORIZONTAL_SELECT_OUTLINED = "panorama_horizontal_select_outlined"
    PANORAMA_PHOTOSPHERE = "panorama_photosphere"
    PANORAMA_PHOTOSPHERE_SHARP = "panorama_photosphere_sharp"
    PANORAMA_PHOTOSPHERE_ROUNDED = "panorama_photosphere_rounded"
    PANORAMA_PHOTOSPHERE_OUTLINED = "panorama_photosphere_outlined"
    PANORAMA_PHOTOSPHERE_SELECT = "panorama_photosphere_select"
    PANORAMA_PHOTOSPHERE_SELECT_SHARP = "panorama_photosphere_select_sharp"
    PANORAMA_PHOTOSPHERE_SELECT_ROUNDED = "panorama_photosphere_select_rounded"
    PANORAMA_PHOTOSPHERE_SELECT_OUTLINED = "panorama_photosphere_select_outlined"
    PANORAMA_VERTICAL = "panorama_vertical"
    PANORAMA_VERTICAL_SHARP = "panorama_vertical_sharp"
    PANORAMA_VERTICAL_ROUNDED = "panorama_vertical_rounded"
    PANORAMA_VERTICAL_OUTLINED = "panorama_vertical_outlined"
    PANORAMA_VERTICAL_SELECT = "panorama_vertical_select"
    PANORAMA_VERTICAL_SELECT_SHARP = "panorama_vertical_select_sharp"
    PANORAMA_VERTICAL_SELECT_ROUNDED = "panorama_vertical_select_rounded"
    PANORAMA_VERTICAL_SELECT_OUTLINED = "panorama_vertical_select_outlined"
    PANORAMA_WIDE_ANGLE = "panorama_wide_angle"
    PANORAMA_WIDE_ANGLE_SHARP = "panorama_wide_angle_sharp"
    PANORAMA_WIDE_ANGLE_ROUNDED = "panorama_wide_angle_rounded"
    PANORAMA_WIDE_ANGLE_OUTLINED = "panorama_wide_angle_outlined"
    PANORAMA_WIDE_ANGLE_SELECT = "panorama_wide_angle_select"
    PANORAMA_WIDE_ANGLE_SELECT_SHARP = "panorama_wide_angle_select_sharp"
    PANORAMA_WIDE_ANGLE_SELECT_ROUNDED = "panorama_wide_angle_select_rounded"
    PANORAMA_WIDE_ANGLE_SELECT_OUTLINED = "panorama_wide_angle_select_outlined"
    PARAGLIDING = "paragliding"
    PARAGLIDING_SHARP = "paragliding_sharp"
    PARAGLIDING_ROUNDED = "paragliding_rounded"
    PARAGLIDING_OUTLINED = "paragliding_outlined"
    PARK = "park"
    PARK_SHARP = "park_sharp"
    PARK_ROUNDED = "park_rounded"
    PARK_OUTLINED = "park_outlined"
    PARTY_MODE = "party_mode"
    PARTY_MODE_SHARP = "party_mode_sharp"
    PARTY_MODE_ROUNDED = "party_mode_rounded"
    PARTY_MODE_OUTLINED = "party_mode_outlined"
    PASSWORD = "password"
    PASSWORD_SHARP = "password_sharp"
    PASSWORD_ROUNDED = "password_rounded"
    PASSWORD_OUTLINED = "password_outlined"
    PASTE = "paste"
    PASTE_SHARP = "paste_sharp"
    PASTE_ROUNDED = "paste_rounded"
    PASTE_OUTLINED = "paste_outlined"
    PATTERN = "pattern"
    PATTERN_SHARP = "pattern_sharp"
    PATTERN_ROUNDED = "pattern_rounded"
    PATTERN_OUTLINED = "pattern_outlined"
    PAUSE = "pause"
    PAUSE_SHARP = "pause_sharp"
    PAUSE_ROUNDED = "pause_rounded"
    PAUSE_OUTLINED = "pause_outlined"
    PAUSE_CIRCLE = "pause_circle"
    PAUSE_CIRCLE_SHARP = "pause_circle_sharp"
    PAUSE_CIRCLE_ROUNDED = "pause_circle_rounded"
    PAUSE_CIRCLE_OUTLINED = "pause_circle_outlined"
    PAUSE_CIRCLE_FILLED = "pause_circle_filled"
    PAUSE_CIRCLE_FILLED_SHARP = "pause_circle_filled_sharp"
    PAUSE_CIRCLE_FILLED_ROUNDED = "pause_circle_filled_rounded"
    PAUSE_CIRCLE_FILLED_OUTLINED = "pause_circle_filled_outlined"
    PAUSE_CIRCLE_OUTLINE = "pause_circle_outline"
    PAUSE_CIRCLE_OUTLINE_SHARP = "pause_circle_outline_sharp"
    PAUSE_CIRCLE_OUTLINE_ROUNDED = "pause_circle_outline_rounded"
    PAUSE_CIRCLE_OUTLINE_OUTLINED = "pause_circle_outline_outlined"
    PAUSE_PRESENTATION = "pause_presentation"
    PAUSE_PRESENTATION_SHARP = "pause_presentation_sharp"
    PAUSE_PRESENTATION_ROUNDED = "pause_presentation_rounded"
    PAUSE_PRESENTATION_OUTLINED = "pause_presentation_outlined"
    PAYMENT = "payment"
    PAYMENT_SHARP = "payment_sharp"
    PAYMENT_ROUNDED = "payment_rounded"
    PAYMENT_OUTLINED = "payment_outlined"
    PAYMENTS = "payments"
    PAYMENTS_SHARP = "payments_sharp"
    PAYMENTS_ROUNDED = "payments_rounded"
    PAYMENTS_OUTLINED = "payments_outlined"
    PAYPAL = "paypal"
    PAYPAL_SHARP = "paypal_sharp"
    PAYPAL_ROUNDED = "paypal_rounded"
    PAYPAL_OUTLINED = "paypal_outlined"
    PEDAL_BIKE = "pedal_bike"
    PEDAL_BIKE_SHARP = "pedal_bike_sharp"
    PEDAL_BIKE_ROUNDED = "pedal_bike_rounded"
    PEDAL_BIKE_OUTLINED = "pedal_bike_outlined"
    PENDING = "pending"
    PENDING_SHARP = "pending_sharp"
    PENDING_ROUNDED = "pending_rounded"
    PENDING_OUTLINED = "pending_outlined"
    PENDING_ACTIONS = "pending_actions"
    PENDING_ACTIONS_SHARP = "pending_actions_sharp"
    PENDING_ACTIONS_ROUNDED = "pending_actions_rounded"
    PENDING_ACTIONS_OUTLINED = "pending_actions_outlined"
    PENTAGON = "pentagon"
    PENTAGON_SHARP = "pentagon_sharp"
    PENTAGON_ROUNDED = "pentagon_rounded"
    PENTAGON_OUTLINED = "pentagon_outlined"
    PEOPLE = "people"
    PEOPLE_SHARP = "people_sharp"
    PEOPLE_ROUNDED = "people_rounded"
    PEOPLE_OUTLINED = "people_outlined"
    PEOPLE_ALT = "people_alt"
    PEOPLE_ALT_SHARP = "people_alt_sharp"
    PEOPLE_ALT_ROUNDED = "people_alt_rounded"
    PEOPLE_ALT_OUTLINED = "people_alt_outlined"
    PEOPLE_OUTLINE = "people_outline"
    PEOPLE_OUTLINE_SHARP = "people_outline_sharp"
    PEOPLE_OUTLINE_ROUNDED = "people_outline_rounded"
    PEOPLE_OUTLINE_OUTLINED = "people_outline_outlined"
    PERCENT = "percent"
    PERCENT_SHARP = "percent_sharp"
    PERCENT_ROUNDED = "percent_rounded"
    PERCENT_OUTLINED = "percent_outlined"
    PERM_CAMERA_MIC = "perm_camera_mic"
    PERM_CAMERA_MIC_SHARP = "perm_camera_mic_sharp"
    PERM_CAMERA_MIC_ROUNDED = "perm_camera_mic_rounded"
    PERM_CAMERA_MIC_OUTLINED = "perm_camera_mic_outlined"
    PERM_CONTACT_CAL = "perm_contact_cal"
    PERM_CONTACT_CAL_SHARP = "perm_contact_cal_sharp"
    PERM_CONTACT_CAL_ROUNDED = "perm_contact_cal_rounded"
    PERM_CONTACT_CAL_OUTLINED = "perm_contact_cal_outlined"
    PERM_CONTACT_CALENDAR = "perm_contact_calendar"
    PERM_CONTACT_CALENDAR_SHARP = "perm_contact_calendar_sharp"
    PERM_CONTACT_CALENDAR_ROUNDED = "perm_contact_calendar_rounded"
    PERM_CONTACT_CALENDAR_OUTLINED = "perm_contact_calendar_outlined"
    PERM_DATA_SETTING = "perm_data_setting"
    PERM_DATA_SETTING_SHARP = "perm_data_setting_sharp"
    PERM_DATA_SETTING_ROUNDED = "perm_data_setting_rounded"
    PERM_DATA_SETTING_OUTLINED = "perm_data_setting_outlined"
    PERM_DEVICE_INFO = "perm_device_info"
    PERM_DEVICE_INFO_SHARP = "perm_device_info_sharp"
    PERM_DEVICE_INFO_ROUNDED = "perm_device_info_rounded"
    PERM_DEVICE_INFO_OUTLINED = "perm_device_info_outlined"
    PERM_DEVICE_INFORMATION = "perm_device_information"
    PERM_DEVICE_INFORMATION_SHARP = "perm_device_information_sharp"
    PERM_DEVICE_INFORMATION_ROUNDED = "perm_device_information_rounded"
    PERM_DEVICE_INFORMATION_OUTLINED = "perm_device_information_outlined"
    PERM_IDENTITY = "perm_identity"
    PERM_IDENTITY_SHARP = "perm_identity_sharp"
    PERM_IDENTITY_ROUNDED = "perm_identity_rounded"
    PERM_IDENTITY_OUTLINED = "perm_identity_outlined"
    PERM_MEDIA = "perm_media"
    PERM_MEDIA_SHARP = "perm_media_sharp"
    PERM_MEDIA_ROUNDED = "perm_media_rounded"
    PERM_MEDIA_OUTLINED = "perm_media_outlined"
    PERM_PHONE_MSG = "perm_phone_msg"
    PERM_PHONE_MSG_SHARP = "perm_phone_msg_sharp"
    PERM_PHONE_MSG_ROUNDED = "perm_phone_msg_rounded"
    PERM_PHONE_MSG_OUTLINED = "perm_phone_msg_outlined"
    PERM_SCAN_WIFI = "perm_scan_wifi"
    PERM_SCAN_WIFI_SHARP = "perm_scan_wifi_sharp"
    PERM_SCAN_WIFI_ROUNDED = "perm_scan_wifi_rounded"
    PERM_SCAN_WIFI_OUTLINED = "perm_scan_wifi_outlined"
    PERSON = "person"
    PERSON_SHARP = "person_sharp"
    PERSON_ROUNDED = "person_rounded"
    PERSON_OUTLINED = "person_outlined"
    PERSON_2 = "person_2"
    PERSON_2_SHARP = "person_2_sharp"
    PERSON_2_ROUNDED = "person_2_rounded"
    PERSON_2_OUTLINED = "person_2_outlined"
    PERSON_3 = "person_3"
    PERSON_3_SHARP = "person_3_sharp"
    PERSON_3_ROUNDED = "person_3_rounded"
    PERSON_3_OUTLINED = "person_3_outlined"
    PERSON_4 = "person_4"
    PERSON_4_SHARP = "person_4_sharp"
    PERSON_4_ROUNDED = "person_4_rounded"
    PERSON_4_OUTLINED = "person_4_outlined"
    PERSON_ADD = "person_add"
    PERSON_ADD_SHARP = "person_add_sharp"
    PERSON_ADD_ROUNDED = "person_add_rounded"
    PERSON_ADD_OUTLINED = "person_add_outlined"
    PERSON_ADD_ALT = "person_add_alt"
    PERSON_ADD_ALT_SHARP = "person_add_alt_sharp"
    PERSON_ADD_ALT_ROUNDED = "person_add_alt_rounded"
    PERSON_ADD_ALT_OUTLINED = "person_add_alt_outlined"
    PERSON_ADD_ALT_1 = "person_add_alt_1"
    PERSON_ADD_ALT_1_SHARP = "person_add_alt_1_sharp"
    PERSON_ADD_ALT_1_ROUNDED = "person_add_alt_1_rounded"
    PERSON_ADD_ALT_1_OUTLINED = "person_add_alt_1_outlined"
    PERSON_ADD_DISABLED = "person_add_disabled"
    PERSON_ADD_DISABLED_SHARP = "person_add_disabled_sharp"
    PERSON_ADD_DISABLED_ROUNDED = "person_add_disabled_rounded"
    PERSON_ADD_DISABLED_OUTLINED = "person_add_disabled_outlined"
    PERSON_OFF = "person_off"
    PERSON_OFF_SHARP = "person_off_sharp"
    PERSON_OFF_ROUNDED = "person_off_rounded"
    PERSON_OFF_OUTLINED = "person_off_outlined"
    PERSON_OUTLINE = "person_outline"
    PERSON_OUTLINE_SHARP = "person_outline_sharp"
    PERSON_OUTLINE_ROUNDED = "person_outline_rounded"
    PERSON_OUTLINE_OUTLINED = "person_outline_outlined"
    PERSON_PIN = "person_pin"
    PERSON_PIN_SHARP = "person_pin_sharp"
    PERSON_PIN_ROUNDED = "person_pin_rounded"
    PERSON_PIN_OUTLINED = "person_pin_outlined"
    PERSON_PIN_CIRCLE = "person_pin_circle"
    PERSON_PIN_CIRCLE_SHARP = "person_pin_circle_sharp"
    PERSON_PIN_CIRCLE_ROUNDED = "person_pin_circle_rounded"
    PERSON_PIN_CIRCLE_OUTLINED = "person_pin_circle_outlined"
    PERSON_REMOVE = "person_remove"
    PERSON_REMOVE_SHARP = "person_remove_sharp"
    PERSON_REMOVE_ROUNDED = "person_remove_rounded"
    PERSON_REMOVE_OUTLINED = "person_remove_outlined"
    PERSON_REMOVE_ALT_1 = "person_remove_alt_1"
    PERSON_REMOVE_ALT_1_SHARP = "person_remove_alt_1_sharp"
    PERSON_REMOVE_ALT_1_ROUNDED = "person_remove_alt_1_rounded"
    PERSON_REMOVE_ALT_1_OUTLINED = "person_remove_alt_1_outlined"
    PERSON_SEARCH = "person_search"
    PERSON_SEARCH_SHARP = "person_search_sharp"
    PERSON_SEARCH_ROUNDED = "person_search_rounded"
    PERSON_SEARCH_OUTLINED = "person_search_outlined"
    PERSONAL_INJURY = "personal_injury"
    PERSONAL_INJURY_SHARP = "personal_injury_sharp"
    PERSONAL_INJURY_ROUNDED = "personal_injury_rounded"
    PERSONAL_INJURY_OUTLINED = "personal_injury_outlined"
    PERSONAL_VIDEO = "personal_video"
    PERSONAL_VIDEO_SHARP = "personal_video_sharp"
    PERSONAL_VIDEO_ROUNDED = "personal_video_rounded"
    PERSONAL_VIDEO_OUTLINED = "personal_video_outlined"
    PEST_CONTROL = "pest_control"
    PEST_CONTROL_SHARP = "pest_control_sharp"
    PEST_CONTROL_ROUNDED = "pest_control_rounded"
    PEST_CONTROL_OUTLINED = "pest_control_outlined"
    PEST_CONTROL_RODENT = "pest_control_rodent"
    PEST_CONTROL_RODENT_SHARP = "pest_control_rodent_sharp"
    PEST_CONTROL_RODENT_ROUNDED = "pest_control_rodent_rounded"
    PEST_CONTROL_RODENT_OUTLINED = "pest_control_rodent_outlined"
    PETS = "pets"
    PETS_SHARP = "pets_sharp"
    PETS_ROUNDED = "pets_rounded"
    PETS_OUTLINED = "pets_outlined"
    PHISHING = "phishing"
    PHISHING_SHARP = "phishing_sharp"
    PHISHING_ROUNDED = "phishing_rounded"
    PHISHING_OUTLINED = "phishing_outlined"
    PHONE = "phone"
    PHONE_SHARP = "phone_sharp"
    PHONE_ROUNDED = "phone_rounded"
    PHONE_OUTLINED = "phone_outlined"
    PHONE_ANDROID = "phone_android"
    PHONE_ANDROID_SHARP = "phone_android_sharp"
    PHONE_ANDROID_ROUNDED = "phone_android_rounded"
    PHONE_ANDROID_OUTLINED = "phone_android_outlined"
    PHONE_BLUETOOTH_SPEAKER = "phone_bluetooth_speaker"
    PHONE_BLUETOOTH_SPEAKER_SHARP = "phone_bluetooth_speaker_sharp"
    PHONE_BLUETOOTH_SPEAKER_ROUNDED = "phone_bluetooth_speaker_rounded"
    PHONE_BLUETOOTH_SPEAKER_OUTLINED = "phone_bluetooth_speaker_outlined"
    PHONE_CALLBACK = "phone_callback"
    PHONE_CALLBACK_SHARP = "phone_callback_sharp"
    PHONE_CALLBACK_ROUNDED = "phone_callback_rounded"
    PHONE_CALLBACK_OUTLINED = "phone_callback_outlined"
    PHONE_DISABLED = "phone_disabled"
    PHONE_DISABLED_SHARP = "phone_disabled_sharp"
    PHONE_DISABLED_ROUNDED = "phone_disabled_rounded"
    PHONE_DISABLED_OUTLINED = "phone_disabled_outlined"
    PHONE_ENABLED = "phone_enabled"
    PHONE_ENABLED_SHARP = "phone_enabled_sharp"
    PHONE_ENABLED_ROUNDED = "phone_enabled_rounded"
    PHONE_ENABLED_OUTLINED = "phone_enabled_outlined"
    PHONE_FORWARDED = "phone_forwarded"
    PHONE_FORWARDED_SHARP = "phone_forwarded_sharp"
    PHONE_FORWARDED_ROUNDED = "phone_forwarded_rounded"
    PHONE_FORWARDED_OUTLINED = "phone_forwarded_outlined"
    PHONE_IN_TALK = "phone_in_talk"
    PHONE_IN_TALK_SHARP = "phone_in_talk_sharp"
    PHONE_IN_TALK_ROUNDED = "phone_in_talk_rounded"
    PHONE_IN_TALK_OUTLINED = "phone_in_talk_outlined"
    PHONE_IPHONE = "phone_iphone"
    PHONE_IPHONE_SHARP = "phone_iphone_sharp"
    PHONE_IPHONE_ROUNDED = "phone_iphone_rounded"
    PHONE_IPHONE_OUTLINED = "phone_iphone_outlined"
    PHONE_LOCKED = "phone_locked"
    PHONE_LOCKED_SHARP = "phone_locked_sharp"
    PHONE_LOCKED_ROUNDED = "phone_locked_rounded"
    PHONE_LOCKED_OUTLINED = "phone_locked_outlined"
    PHONE_MISSED = "phone_missed"
    PHONE_MISSED_SHARP = "phone_missed_sharp"
    PHONE_MISSED_ROUNDED = "phone_missed_rounded"
    PHONE_MISSED_OUTLINED = "phone_missed_outlined"
    PHONE_PAUSED = "phone_paused"
    PHONE_PAUSED_SHARP = "phone_paused_sharp"
    PHONE_PAUSED_ROUNDED = "phone_paused_rounded"
    PHONE_PAUSED_OUTLINED = "phone_paused_outlined"
    PHONELINK = "phonelink"
    PHONELINK_SHARP = "phonelink_sharp"
    PHONELINK_ROUNDED = "phonelink_rounded"
    PHONELINK_OUTLINED = "phonelink_outlined"
    PHONELINK_ERASE = "phonelink_erase"
    PHONELINK_ERASE_SHARP = "phonelink_erase_sharp"
    PHONELINK_ERASE_ROUNDED = "phonelink_erase_rounded"
    PHONELINK_ERASE_OUTLINED = "phonelink_erase_outlined"
    PHONELINK_LOCK = "phonelink_lock"
    PHONELINK_LOCK_SHARP = "phonelink_lock_sharp"
    PHONELINK_LOCK_ROUNDED = "phonelink_lock_rounded"
    PHONELINK_LOCK_OUTLINED = "phonelink_lock_outlined"
    PHONELINK_OFF = "phonelink_off"
    PHONELINK_OFF_SHARP = "phonelink_off_sharp"
    PHONELINK_OFF_ROUNDED = "phonelink_off_rounded"
    PHONELINK_OFF_OUTLINED = "phonelink_off_outlined"
    PHONELINK_RING = "phonelink_ring"
    PHONELINK_RING_SHARP = "phonelink_ring_sharp"
    PHONELINK_RING_ROUNDED = "phonelink_ring_rounded"
    PHONELINK_RING_OUTLINED = "phonelink_ring_outlined"
    PHONELINK_SETUP = "phonelink_setup"
    PHONELINK_SETUP_SHARP = "phonelink_setup_sharp"
    PHONELINK_SETUP_ROUNDED = "phonelink_setup_rounded"
    PHONELINK_SETUP_OUTLINED = "phonelink_setup_outlined"
    PHOTO = "photo"
    PHOTO_SHARP = "photo_sharp"
    PHOTO_ROUNDED = "photo_rounded"
    PHOTO_OUTLINED = "photo_outlined"
    PHOTO_ALBUM = "photo_album"
    PHOTO_ALBUM_SHARP = "photo_album_sharp"
    PHOTO_ALBUM_ROUNDED = "photo_album_rounded"
    PHOTO_ALBUM_OUTLINED = "photo_album_outlined"
    PHOTO_CAMERA = "photo_camera"
    PHOTO_CAMERA_SHARP = "photo_camera_sharp"
    PHOTO_CAMERA_ROUNDED = "photo_camera_rounded"
    PHOTO_CAMERA_OUTLINED = "photo_camera_outlined"
    PHOTO_CAMERA_BACK = "photo_camera_back"
    PHOTO_CAMERA_BACK_SHARP = "photo_camera_back_sharp"
    PHOTO_CAMERA_BACK_ROUNDED = "photo_camera_back_rounded"
    PHOTO_CAMERA_BACK_OUTLINED = "photo_camera_back_outlined"
    PHOTO_CAMERA_FRONT = "photo_camera_front"
    PHOTO_CAMERA_FRONT_SHARP = "photo_camera_front_sharp"
    PHOTO_CAMERA_FRONT_ROUNDED = "photo_camera_front_rounded"
    PHOTO_CAMERA_FRONT_OUTLINED = "photo_camera_front_outlined"
    PHOTO_FILTER = "photo_filter"
    PHOTO_FILTER_SHARP = "photo_filter_sharp"
    PHOTO_FILTER_ROUNDED = "photo_filter_rounded"
    PHOTO_FILTER_OUTLINED = "photo_filter_outlined"
    PHOTO_LIBRARY = "photo_library"
    PHOTO_LIBRARY_SHARP = "photo_library_sharp"
    PHOTO_LIBRARY_ROUNDED = "photo_library_rounded"
    PHOTO_LIBRARY_OUTLINED = "photo_library_outlined"
    PHOTO_SIZE_SELECT_ACTUAL = "photo_size_select_actual"
    PHOTO_SIZE_SELECT_ACTUAL_SHARP = "photo_size_select_actual_sharp"
    PHOTO_SIZE_SELECT_ACTUAL_ROUNDED = "photo_size_select_actual_rounded"
    PHOTO_SIZE_SELECT_ACTUAL_OUTLINED = "photo_size_select_actual_outlined"
    PHOTO_SIZE_SELECT_LARGE = "photo_size_select_large"
    PHOTO_SIZE_SELECT_LARGE_SHARP = "photo_size_select_large_sharp"
    PHOTO_SIZE_SELECT_LARGE_ROUNDED = "photo_size_select_large_rounded"
    PHOTO_SIZE_SELECT_LARGE_OUTLINED = "photo_size_select_large_outlined"
    PHOTO_SIZE_SELECT_SMALL = "photo_size_select_small"
    PHOTO_SIZE_SELECT_SMALL_SHARP = "photo_size_select_small_sharp"
    PHOTO_SIZE_SELECT_SMALL_ROUNDED = "photo_size_select_small_rounded"
    PHOTO_SIZE_SELECT_SMALL_OUTLINED = "photo_size_select_small_outlined"
    PHP = "php"
    PHP_SHARP = "php_sharp"
    PHP_ROUNDED = "php_rounded"
    PHP_OUTLINED = "php_outlined"
    PIANO = "piano"
    PIANO_SHARP = "piano_sharp"
    PIANO_ROUNDED = "piano_rounded"
    PIANO_OUTLINED = "piano_outlined"
    PIANO_OFF = "piano_off"
    PIANO_OFF_SHARP = "piano_off_sharp"
    PIANO_OFF_ROUNDED = "piano_off_rounded"
    PIANO_OFF_OUTLINED = "piano_off_outlined"
    PICTURE_AS_PDF = "picture_as_pdf"
    PICTURE_AS_PDF_SHARP = "picture_as_pdf_sharp"
    PICTURE_AS_PDF_ROUNDED = "picture_as_pdf_rounded"
    PICTURE_AS_PDF_OUTLINED = "picture_as_pdf_outlined"
    PICTURE_IN_PICTURE = "picture_in_picture"
    PICTURE_IN_PICTURE_SHARP = "picture_in_picture_sharp"
    PICTURE_IN_PICTURE_ROUNDED = "picture_in_picture_rounded"
    PICTURE_IN_PICTURE_OUTLINED = "picture_in_picture_outlined"
    PICTURE_IN_PICTURE_ALT = "picture_in_picture_alt"
    PICTURE_IN_PICTURE_ALT_SHARP = "picture_in_picture_alt_sharp"
    PICTURE_IN_PICTURE_ALT_ROUNDED = "picture_in_picture_alt_rounded"
    PICTURE_IN_PICTURE_ALT_OUTLINED = "picture_in_picture_alt_outlined"
    PIE_CHART = "pie_chart"
    PIE_CHART_SHARP = "pie_chart_sharp"
    PIE_CHART_ROUNDED = "pie_chart_rounded"
    PIE_CHART_OUTLINE = "pie_chart_outline"
    PIE_CHART_OUTLINE_SHARP = "pie_chart_outline_sharp"
    PIE_CHART_OUTLINE_ROUNDED = "pie_chart_outline_rounded"
    PIE_CHART_OUTLINE_OUTLINED = "pie_chart_outline_outlined"
    PIN = "pin"
    PIN_SHARP = "pin_sharp"
    PIN_ROUNDED = "pin_rounded"
    PIN_OUTLINED = "pin_outlined"
    PIN_DROP = "pin_drop"
    PIN_DROP_SHARP = "pin_drop_sharp"
    PIN_DROP_ROUNDED = "pin_drop_rounded"
    PIN_DROP_OUTLINED = "pin_drop_outlined"
    PIN_END = "pin_end"
    PIN_END_SHARP = "pin_end_sharp"
    PIN_END_ROUNDED = "pin_end_rounded"
    PIN_END_OUTLINED = "pin_end_outlined"
    PIN_INVOKE = "pin_invoke"
    PIN_INVOKE_SHARP = "pin_invoke_sharp"
    PIN_INVOKE_ROUNDED = "pin_invoke_rounded"
    PIN_INVOKE_OUTLINED = "pin_invoke_outlined"
    PINCH = "pinch"
    PINCH_SHARP = "pinch_sharp"
    PINCH_ROUNDED = "pinch_rounded"
    PINCH_OUTLINED = "pinch_outlined"
    PIVOT_TABLE_CHART = "pivot_table_chart"
    PIVOT_TABLE_CHART_SHARP = "pivot_table_chart_sharp"
    PIVOT_TABLE_CHART_ROUNDED = "pivot_table_chart_rounded"
    PIVOT_TABLE_CHART_OUTLINED = "pivot_table_chart_outlined"
    PIX = "pix"
    PIX_SHARP = "pix_sharp"
    PIX_ROUNDED = "pix_rounded"
    PIX_OUTLINED = "pix_outlined"
    PLACE = "place"
    PLACE_SHARP = "place_sharp"
    PLACE_ROUNDED = "place_rounded"
    PLACE_OUTLINED = "place_outlined"
    PLAGIARISM = "plagiarism"
    PLAGIARISM_SHARP = "plagiarism_sharp"
    PLAGIARISM_ROUNDED = "plagiarism_rounded"
    PLAGIARISM_OUTLINED = "plagiarism_outlined"
    PLAY_ARROW = "play_arrow"
    PLAY_ARROW_SHARP = "play_arrow_sharp"
    PLAY_ARROW_ROUNDED = "play_arrow_rounded"
    PLAY_ARROW_OUTLINED = "play_arrow_outlined"
    PLAY_CIRCLE = "play_circle"
    PLAY_CIRCLE_SHARP = "play_circle_sharp"
    PLAY_CIRCLE_ROUNDED = "play_circle_rounded"
    PLAY_CIRCLE_OUTLINED = "play_circle_outlined"
    PLAY_CIRCLE_FILL = "play_circle_fill"
    PLAY_CIRCLE_FILL_SHARP = "play_circle_fill_sharp"
    PLAY_CIRCLE_FILL_ROUNDED = "play_circle_fill_rounded"
    PLAY_CIRCLE_FILL_OUTLINED = "play_circle_fill_outlined"
    PLAY_CIRCLE_FILLED = "play_circle_filled"
    PLAY_CIRCLE_FILLED_SHARP = "play_circle_filled_sharp"
    PLAY_CIRCLE_FILLED_ROUNDED = "play_circle_filled_rounded"
    PLAY_CIRCLE_FILLED_OUTLINED = "play_circle_filled_outlined"
    PLAY_CIRCLE_OUTLINE = "play_circle_outline"
    PLAY_CIRCLE_OUTLINE_SHARP = "play_circle_outline_sharp"
    PLAY_CIRCLE_OUTLINE_ROUNDED = "play_circle_outline_rounded"
    PLAY_CIRCLE_OUTLINE_OUTLINED = "play_circle_outline_outlined"
    PLAY_DISABLED = "play_disabled"
    PLAY_DISABLED_SHARP = "play_disabled_sharp"
    PLAY_DISABLED_ROUNDED = "play_disabled_rounded"
    PLAY_DISABLED_OUTLINED = "play_disabled_outlined"
    PLAY_FOR_WORK = "play_for_work"
    PLAY_FOR_WORK_SHARP = "play_for_work_sharp"
    PLAY_FOR_WORK_ROUNDED = "play_for_work_rounded"
    PLAY_FOR_WORK_OUTLINED = "play_for_work_outlined"
    PLAY_LESSON = "play_lesson"
    PLAY_LESSON_SHARP = "play_lesson_sharp"
    PLAY_LESSON_ROUNDED = "play_lesson_rounded"
    PLAY_LESSON_OUTLINED = "play_lesson_outlined"
    PLAYLIST_ADD = "playlist_add"
    PLAYLIST_ADD_SHARP = "playlist_add_sharp"
    PLAYLIST_ADD_ROUNDED = "playlist_add_rounded"
    PLAYLIST_ADD_OUTLINED = "playlist_add_outlined"
    PLAYLIST_ADD_CHECK = "playlist_add_check"
    PLAYLIST_ADD_CHECK_SHARP = "playlist_add_check_sharp"
    PLAYLIST_ADD_CHECK_ROUNDED = "playlist_add_check_rounded"
    PLAYLIST_ADD_CHECK_OUTLINED = "playlist_add_check_outlined"
    PLAYLIST_ADD_CHECK_CIRCLE = "playlist_add_check_circle"
    PLAYLIST_ADD_CHECK_CIRCLE_SHARP = "playlist_add_check_circle_sharp"
    PLAYLIST_ADD_CHECK_CIRCLE_ROUNDED = "playlist_add_check_circle_rounded"
    PLAYLIST_ADD_CHECK_CIRCLE_OUTLINED = "playlist_add_check_circle_outlined"
    PLAYLIST_ADD_CIRCLE = "playlist_add_circle"
    PLAYLIST_ADD_CIRCLE_SHARP = "playlist_add_circle_sharp"
    PLAYLIST_ADD_CIRCLE_ROUNDED = "playlist_add_circle_rounded"
    PLAYLIST_ADD_CIRCLE_OUTLINED = "playlist_add_circle_outlined"
    PLAYLIST_PLAY = "playlist_play"
    PLAYLIST_PLAY_SHARP = "playlist_play_sharp"
    PLAYLIST_PLAY_ROUNDED = "playlist_play_rounded"
    PLAYLIST_PLAY_OUTLINED = "playlist_play_outlined"
    PLAYLIST_REMOVE = "playlist_remove"
    PLAYLIST_REMOVE_SHARP = "playlist_remove_sharp"
    PLAYLIST_REMOVE_ROUNDED = "playlist_remove_rounded"
    PLAYLIST_REMOVE_OUTLINED = "playlist_remove_outlined"
    PLUMBING = "plumbing"
    PLUMBING_SHARP = "plumbing_sharp"
    PLUMBING_ROUNDED = "plumbing_rounded"
    PLUMBING_OUTLINED = "plumbing_outlined"
    PLUS_ONE = "plus_one"
    PLUS_ONE_SHARP = "plus_one_sharp"
    PLUS_ONE_ROUNDED = "plus_one_rounded"
    PLUS_ONE_OUTLINED = "plus_one_outlined"
    PODCASTS = "podcasts"
    PODCASTS_SHARP = "podcasts_sharp"
    PODCASTS_ROUNDED = "podcasts_rounded"
    PODCASTS_OUTLINED = "podcasts_outlined"
    POINT_OF_SALE = "point_of_sale"
    POINT_OF_SALE_SHARP = "point_of_sale_sharp"
    POINT_OF_SALE_ROUNDED = "point_of_sale_rounded"
    POINT_OF_SALE_OUTLINED = "point_of_sale_outlined"
    POLICY = "policy"
    POLICY_SHARP = "policy_sharp"
    POLICY_ROUNDED = "policy_rounded"
    POLICY_OUTLINED = "policy_outlined"
    POLL = "poll"
    POLL_SHARP = "poll_sharp"
    POLL_ROUNDED = "poll_rounded"
    POLL_OUTLINED = "poll_outlined"
    POLYLINE = "polyline"
    POLYLINE_SHARP = "polyline_sharp"
    POLYLINE_ROUNDED = "polyline_rounded"
    POLYLINE_OUTLINED = "polyline_outlined"
    POLYMER = "polymer"
    POLYMER_SHARP = "polymer_sharp"
    POLYMER_ROUNDED = "polymer_rounded"
    POLYMER_OUTLINED = "polymer_outlined"
    POOL = "pool"
    POOL_SHARP = "pool_sharp"
    POOL_ROUNDED = "pool_rounded"
    POOL_OUTLINED = "pool_outlined"
    PORTABLE_WIFI_OFF = "portable_wifi_off"
    PORTABLE_WIFI_OFF_SHARP = "portable_wifi_off_sharp"
    PORTABLE_WIFI_OFF_ROUNDED = "portable_wifi_off_rounded"
    PORTABLE_WIFI_OFF_OUTLINED = "portable_wifi_off_outlined"
    PORTRAIT = "portrait"
    PORTRAIT_SHARP = "portrait_sharp"
    PORTRAIT_ROUNDED = "portrait_rounded"
    PORTRAIT_OUTLINED = "portrait_outlined"
    POST_ADD = "post_add"
    POST_ADD_SHARP = "post_add_sharp"
    POST_ADD_ROUNDED = "post_add_rounded"
    POST_ADD_OUTLINED = "post_add_outlined"
    POWER = "power"
    POWER_SHARP = "power_sharp"
    POWER_ROUNDED = "power_rounded"
    POWER_OUTLINED = "power_outlined"
    POWER_INPUT = "power_input"
    POWER_INPUT_SHARP = "power_input_sharp"
    POWER_INPUT_ROUNDED = "power_input_rounded"
    POWER_INPUT_OUTLINED = "power_input_outlined"
    POWER_OFF = "power_off"
    POWER_OFF_SHARP = "power_off_sharp"
    POWER_OFF_ROUNDED = "power_off_rounded"
    POWER_OFF_OUTLINED = "power_off_outlined"
    POWER_SETTINGS_NEW = "power_settings_new"
    POWER_SETTINGS_NEW_SHARP = "power_settings_new_sharp"
    POWER_SETTINGS_NEW_ROUNDED = "power_settings_new_rounded"
    POWER_SETTINGS_NEW_OUTLINED = "power_settings_new_outlined"
    PRECISION_MANUFACTURING = "precision_manufacturing"
    PRECISION_MANUFACTURING_SHARP = "precision_manufacturing_sharp"
    PRECISION_MANUFACTURING_ROUNDED = "precision_manufacturing_rounded"
    PRECISION_MANUFACTURING_OUTLINED = "precision_manufacturing_outlined"
    PREGNANT_WOMAN = "pregnant_woman"
    PREGNANT_WOMAN_SHARP = "pregnant_woman_sharp"
    PREGNANT_WOMAN_ROUNDED = "pregnant_woman_rounded"
    PREGNANT_WOMAN_OUTLINED = "pregnant_woman_outlined"
    PRESENT_TO_ALL = "present_to_all"
    PRESENT_TO_ALL_SHARP = "present_to_all_sharp"
    PRESENT_TO_ALL_ROUNDED = "present_to_all_rounded"
    PRESENT_TO_ALL_OUTLINED = "present_to_all_outlined"
    PREVIEW = "preview"
    PREVIEW_SHARP = "preview_sharp"
    PREVIEW_ROUNDED = "preview_rounded"
    PREVIEW_OUTLINED = "preview_outlined"
    PRICE_CHANGE = "price_change"
    PRICE_CHANGE_SHARP = "price_change_sharp"
    PRICE_CHANGE_ROUNDED = "price_change_rounded"
    PRICE_CHANGE_OUTLINED = "price_change_outlined"
    PRICE_CHECK = "price_check"
    PRICE_CHECK_SHARP = "price_check_sharp"
    PRICE_CHECK_ROUNDED = "price_check_rounded"
    PRICE_CHECK_OUTLINED = "price_check_outlined"
    PRINT = "print"
    PRINT_SHARP = "print_sharp"
    PRINT_ROUNDED = "print_rounded"
    PRINT_OUTLINED = "print_outlined"
    PRINT_DISABLED = "print_disabled"
    PRINT_DISABLED_SHARP = "print_disabled_sharp"
    PRINT_DISABLED_ROUNDED = "print_disabled_rounded"
    PRINT_DISABLED_OUTLINED = "print_disabled_outlined"
    PRIORITY_HIGH = "priority_high"
    PRIORITY_HIGH_SHARP = "priority_high_sharp"
    PRIORITY_HIGH_ROUNDED = "priority_high_rounded"
    PRIORITY_HIGH_OUTLINED = "priority_high_outlined"
    PRIVACY_TIP = "privacy_tip"
    PRIVACY_TIP_SHARP = "privacy_tip_sharp"
    PRIVACY_TIP_ROUNDED = "privacy_tip_rounded"
    PRIVACY_TIP_OUTLINED = "privacy_tip_outlined"
    PRIVATE_CONNECTIVITY = "private_connectivity"
    PRIVATE_CONNECTIVITY_SHARP = "private_connectivity_sharp"
    PRIVATE_CONNECTIVITY_ROUNDED = "private_connectivity_rounded"
    PRIVATE_CONNECTIVITY_OUTLINED = "private_connectivity_outlined"
    PRODUCTION_QUANTITY_LIMITS = "production_quantity_limits"
    PRODUCTION_QUANTITY_LIMITS_SHARP = "production_quantity_limits_sharp"
    PRODUCTION_QUANTITY_LIMITS_ROUNDED = "production_quantity_limits_rounded"
    PRODUCTION_QUANTITY_LIMITS_OUTLINED = "production_quantity_limits_outlined"
    PROPANE = "propane"
    PROPANE_SHARP = "propane_sharp"
    PROPANE_ROUNDED = "propane_rounded"
    PROPANE_OUTLINED = "propane_outlined"
    PROPANE_TANK = "propane_tank"
    PROPANE_TANK_SHARP = "propane_tank_sharp"
    PROPANE_TANK_ROUNDED = "propane_tank_rounded"
    PROPANE_TANK_OUTLINED = "propane_tank_outlined"
    PSYCHOLOGY = "psychology"
    PSYCHOLOGY_SHARP = "psychology_sharp"
    PSYCHOLOGY_ROUNDED = "psychology_rounded"
    PSYCHOLOGY_OUTLINED = "psychology_outlined"
    PSYCHOLOGY_ALT = "psychology_alt"
    PSYCHOLOGY_ALT_SHARP = "psychology_alt_sharp"
    PSYCHOLOGY_ALT_ROUNDED = "psychology_alt_rounded"
    PSYCHOLOGY_ALT_OUTLINED = "psychology_alt_outlined"
    PUBLIC = "public"
    PUBLIC_SHARP = "public_sharp"
    PUBLIC_ROUNDED = "public_rounded"
    PUBLIC_OUTLINED = "public_outlined"
    PUBLIC_OFF = "public_off"
    PUBLIC_OFF_SHARP = "public_off_sharp"
    PUBLIC_OFF_ROUNDED = "public_off_rounded"
    PUBLIC_OFF_OUTLINED = "public_off_outlined"
    PUBLISH = "publish"
    PUBLISH_SHARP = "publish_sharp"
    PUBLISH_ROUNDED = "publish_rounded"
    PUBLISH_OUTLINED = "publish_outlined"
    PUBLISHED_WITH_CHANGES = "published_with_changes"
    PUBLISHED_WITH_CHANGES_SHARP = "published_with_changes_sharp"
    PUBLISHED_WITH_CHANGES_ROUNDED = "published_with_changes_rounded"
    PUBLISHED_WITH_CHANGES_OUTLINED = "published_with_changes_outlined"
    PUNCH_CLOCK = "punch_clock"
    PUNCH_CLOCK_SHARP = "punch_clock_sharp"
    PUNCH_CLOCK_ROUNDED = "punch_clock_rounded"
    PUNCH_CLOCK_OUTLINED = "punch_clock_outlined"
    PUSH_PIN = "push_pin"
    PUSH_PIN_SHARP = "push_pin_sharp"
    PUSH_PIN_ROUNDED = "push_pin_rounded"
    PUSH_PIN_OUTLINED = "push_pin_outlined"
    QR_CODE = "qr_code"
    QR_CODE_SHARP = "qr_code_sharp"
    QR_CODE_ROUNDED = "qr_code_rounded"
    QR_CODE_OUTLINED = "qr_code_outlined"
    QR_CODE_2 = "qr_code_2"
    QR_CODE_2_SHARP = "qr_code_2_sharp"
    QR_CODE_2_ROUNDED = "qr_code_2_rounded"
    QR_CODE_2_OUTLINED = "qr_code_2_outlined"
    QR_CODE_SCANNER = "qr_code_scanner"
    QR_CODE_SCANNER_SHARP = "qr_code_scanner_sharp"
    QR_CODE_SCANNER_ROUNDED = "qr_code_scanner_rounded"
    QR_CODE_SCANNER_OUTLINED = "qr_code_scanner_outlined"
    QUERY_BUILDER = "query_builder"
    QUERY_BUILDER_SHARP = "query_builder_sharp"
    QUERY_BUILDER_ROUNDED = "query_builder_rounded"
    QUERY_BUILDER_OUTLINED = "query_builder_outlined"
    QUERY_STATS = "query_stats"
    QUERY_STATS_SHARP = "query_stats_sharp"
    QUERY_STATS_ROUNDED = "query_stats_rounded"
    QUERY_STATS_OUTLINED = "query_stats_outlined"
    QUESTION_ANSWER = "question_answer"
    QUESTION_ANSWER_SHARP = "question_answer_sharp"
    QUESTION_ANSWER_ROUNDED = "question_answer_rounded"
    QUESTION_ANSWER_OUTLINED = "question_answer_outlined"
    QUESTION_MARK = "question_mark"
    QUESTION_MARK_SHARP = "question_mark_sharp"
    QUESTION_MARK_ROUNDED = "question_mark_rounded"
    QUESTION_MARK_OUTLINED = "question_mark_outlined"
    QUEUE = "queue"
    QUEUE_SHARP = "queue_sharp"
    QUEUE_ROUNDED = "queue_rounded"
    QUEUE_OUTLINED = "queue_outlined"
    QUEUE_MUSIC = "queue_music"
    QUEUE_MUSIC_SHARP = "queue_music_sharp"
    QUEUE_MUSIC_ROUNDED = "queue_music_rounded"
    QUEUE_MUSIC_OUTLINED = "queue_music_outlined"
    QUEUE_PLAY_NEXT = "queue_play_next"
    QUEUE_PLAY_NEXT_SHARP = "queue_play_next_sharp"
    QUEUE_PLAY_NEXT_ROUNDED = "queue_play_next_rounded"
    QUEUE_PLAY_NEXT_OUTLINED = "queue_play_next_outlined"
    QUICK_CONTACTS_DIALER = "quick_contacts_dialer"
    QUICK_CONTACTS_DIALER_SHARP = "quick_contacts_dialer_sharp"
    QUICK_CONTACTS_DIALER_ROUNDED = "quick_contacts_dialer_rounded"
    QUICK_CONTACTS_DIALER_OUTLINED = "quick_contacts_dialer_outlined"
    QUICK_CONTACTS_MAIL = "quick_contacts_mail"
    QUICK_CONTACTS_MAIL_SHARP = "quick_contacts_mail_sharp"
    QUICK_CONTACTS_MAIL_ROUNDED = "quick_contacts_mail_rounded"
    QUICK_CONTACTS_MAIL_OUTLINED = "quick_contacts_mail_outlined"
    QUICKREPLY = "quickreply"
    QUICKREPLY_SHARP = "quickreply_sharp"
    QUICKREPLY_ROUNDED = "quickreply_rounded"
    QUICKREPLY_OUTLINED = "quickreply_outlined"
    QUIZ = "quiz"
    QUIZ_SHARP = "quiz_sharp"
    QUIZ_ROUNDED = "quiz_rounded"
    QUIZ_OUTLINED = "quiz_outlined"
    QUORA = "quora"
    QUORA_SHARP = "quora_sharp"
    QUORA_ROUNDED = "quora_rounded"
    QUORA_OUTLINED = "quora_outlined"
    R_MOBILEDATA = "r_mobiledata"
    R_MOBILEDATA_SHARP = "r_mobiledata_sharp"
    R_MOBILEDATA_ROUNDED = "r_mobiledata_rounded"
    R_MOBILEDATA_OUTLINED = "r_mobiledata_outlined"
    RADAR = "radar"
    RADAR_SHARP = "radar_sharp"
    RADAR_ROUNDED = "radar_rounded"
    RADAR_OUTLINED = "radar_outlined"
    RADIO = "radio"
    RADIO_SHARP = "radio_sharp"
    RADIO_ROUNDED = "radio_rounded"
    RADIO_OUTLINED = "radio_outlined"
    RADIO_BUTTON_CHECKED = "radio_button_checked"
    RADIO_BUTTON_CHECKED_SHARP = "radio_button_checked_sharp"
    RADIO_BUTTON_CHECKED_ROUNDED = "radio_button_checked_rounded"
    RADIO_BUTTON_CHECKED_OUTLINED = "radio_button_checked_outlined"
    RADIO_BUTTON_OFF = "radio_button_off"
    RADIO_BUTTON_OFF_SHARP = "radio_button_off_sharp"
    RADIO_BUTTON_OFF_ROUNDED = "radio_button_off_rounded"
    RADIO_BUTTON_OFF_OUTLINED = "radio_button_off_outlined"
    RADIO_BUTTON_ON = "radio_button_on"
    RADIO_BUTTON_ON_SHARP = "radio_button_on_sharp"
    RADIO_BUTTON_ON_ROUNDED = "radio_button_on_rounded"
    RADIO_BUTTON_ON_OUTLINED = "radio_button_on_outlined"
    RADIO_BUTTON_UNCHECKED = "radio_button_unchecked"
    RADIO_BUTTON_UNCHECKED_SHARP = "radio_button_unchecked_sharp"
    RADIO_BUTTON_UNCHECKED_ROUNDED = "radio_button_unchecked_rounded"
    RADIO_BUTTON_UNCHECKED_OUTLINED = "radio_button_unchecked_outlined"
    RAILWAY_ALERT = "railway_alert"
    RAILWAY_ALERT_SHARP = "railway_alert_sharp"
    RAILWAY_ALERT_ROUNDED = "railway_alert_rounded"
    RAILWAY_ALERT_OUTLINED = "railway_alert_outlined"
    RAMEN_DINING = "ramen_dining"
    RAMEN_DINING_SHARP = "ramen_dining_sharp"
    RAMEN_DINING_ROUNDED = "ramen_dining_rounded"
    RAMEN_DINING_OUTLINED = "ramen_dining_outlined"
    RAMP_LEFT = "ramp_left"
    RAMP_LEFT_SHARP = "ramp_left_sharp"
    RAMP_LEFT_ROUNDED = "ramp_left_rounded"
    RAMP_LEFT_OUTLINED = "ramp_left_outlined"
    RAMP_RIGHT = "ramp_right"
    RAMP_RIGHT_SHARP = "ramp_right_sharp"
    RAMP_RIGHT_ROUNDED = "ramp_right_rounded"
    RAMP_RIGHT_OUTLINED = "ramp_right_outlined"
    RATE_REVIEW = "rate_review"
    RATE_REVIEW_SHARP = "rate_review_sharp"
    RATE_REVIEW_ROUNDED = "rate_review_rounded"
    RATE_REVIEW_OUTLINED = "rate_review_outlined"
    RAW_OFF = "raw_off"
    RAW_OFF_SHARP = "raw_off_sharp"
    RAW_OFF_ROUNDED = "raw_off_rounded"
    RAW_OFF_OUTLINED = "raw_off_outlined"
    RAW_ON = "raw_on"
    RAW_ON_SHARP = "raw_on_sharp"
    RAW_ON_ROUNDED = "raw_on_rounded"
    RAW_ON_OUTLINED = "raw_on_outlined"
    READ_MORE = "read_more"
    READ_MORE_SHARP = "read_more_sharp"
    READ_MORE_ROUNDED = "read_more_rounded"
    READ_MORE_OUTLINED = "read_more_outlined"
    REAL_ESTATE_AGENT = "real_estate_agent"
    REAL_ESTATE_AGENT_SHARP = "real_estate_agent_sharp"
    REAL_ESTATE_AGENT_ROUNDED = "real_estate_agent_rounded"
    REAL_ESTATE_AGENT_OUTLINED = "real_estate_agent_outlined"
    REBASE_EDIT = "rebase_edit"
    RECEIPT = "receipt"
    RECEIPT_SHARP = "receipt_sharp"
    RECEIPT_ROUNDED = "receipt_rounded"
    RECEIPT_OUTLINED = "receipt_outlined"
    RECEIPT_LONG = "receipt_long"
    RECEIPT_LONG_SHARP = "receipt_long_sharp"
    RECEIPT_LONG_ROUNDED = "receipt_long_rounded"
    RECEIPT_LONG_OUTLINED = "receipt_long_outlined"
    RECENT_ACTORS = "recent_actors"
    RECENT_ACTORS_SHARP = "recent_actors_sharp"
    RECENT_ACTORS_ROUNDED = "recent_actors_rounded"
    RECENT_ACTORS_OUTLINED = "recent_actors_outlined"
    RECOMMEND = "recommend"
    RECOMMEND_SHARP = "recommend_sharp"
    RECOMMEND_ROUNDED = "recommend_rounded"
    RECOMMEND_OUTLINED = "recommend_outlined"
    RECORD_VOICE_OVER = "record_voice_over"
    RECORD_VOICE_OVER_SHARP = "record_voice_over_sharp"
    RECORD_VOICE_OVER_ROUNDED = "record_voice_over_rounded"
    RECORD_VOICE_OVER_OUTLINED = "record_voice_over_outlined"
    RECTANGLE = "rectangle"
    RECTANGLE_SHARP = "rectangle_sharp"
    RECTANGLE_ROUNDED = "rectangle_rounded"
    RECTANGLE_OUTLINED = "rectangle_outlined"
    RECYCLING = "recycling"
    RECYCLING_SHARP = "recycling_sharp"
    RECYCLING_ROUNDED = "recycling_rounded"
    RECYCLING_OUTLINED = "recycling_outlined"
    REDDIT = "reddit"
    REDDIT_SHARP = "reddit_sharp"
    REDDIT_ROUNDED = "reddit_rounded"
    REDDIT_OUTLINED = "reddit_outlined"
    REDEEM = "redeem"
    REDEEM_SHARP = "redeem_sharp"
    REDEEM_ROUNDED = "redeem_rounded"
    REDEEM_OUTLINED = "redeem_outlined"
    REDO = "redo"
    REDO_SHARP = "redo_sharp"
    REDO_ROUNDED = "redo_rounded"
    REDO_OUTLINED = "redo_outlined"
    REDUCE_CAPACITY = "reduce_capacity"
    REDUCE_CAPACITY_SHARP = "reduce_capacity_sharp"
    REDUCE_CAPACITY_ROUNDED = "reduce_capacity_rounded"
    REDUCE_CAPACITY_OUTLINED = "reduce_capacity_outlined"
    REFRESH = "refresh"
    REFRESH_SHARP = "refresh_sharp"
    REFRESH_ROUNDED = "refresh_rounded"
    REFRESH_OUTLINED = "refresh_outlined"
    REMEMBER_ME = "remember_me"
    REMEMBER_ME_SHARP = "remember_me_sharp"
    REMEMBER_ME_ROUNDED = "remember_me_rounded"
    REMEMBER_ME_OUTLINED = "remember_me_outlined"
    REMOVE = "remove"
    REMOVE_SHARP = "remove_sharp"
    REMOVE_ROUNDED = "remove_rounded"
    REMOVE_OUTLINED = "remove_outlined"
    REMOVE_CIRCLE = "remove_circle"
    REMOVE_CIRCLE_SHARP = "remove_circle_sharp"
    REMOVE_CIRCLE_ROUNDED = "remove_circle_rounded"
    REMOVE_CIRCLE_OUTLINED = "remove_circle_outlined"
    REMOVE_CIRCLE_OUTLINE = "remove_circle_outline"
    REMOVE_CIRCLE_OUTLINE_SHARP = "remove_circle_outline_sharp"
    REMOVE_CIRCLE_OUTLINE_ROUNDED = "remove_circle_outline_rounded"
    REMOVE_CIRCLE_OUTLINE_OUTLINED = "remove_circle_outline_outlined"
    REMOVE_DONE = "remove_done"
    REMOVE_DONE_SHARP = "remove_done_sharp"
    REMOVE_DONE_ROUNDED = "remove_done_rounded"
    REMOVE_DONE_OUTLINED = "remove_done_outlined"
    REMOVE_FROM_QUEUE = "remove_from_queue"
    REMOVE_FROM_QUEUE_SHARP = "remove_from_queue_sharp"
    REMOVE_FROM_QUEUE_ROUNDED = "remove_from_queue_rounded"
    REMOVE_FROM_QUEUE_OUTLINED = "remove_from_queue_outlined"
    REMOVE_MODERATOR = "remove_moderator"
    REMOVE_MODERATOR_SHARP = "remove_moderator_sharp"
    REMOVE_MODERATOR_ROUNDED = "remove_moderator_rounded"
    REMOVE_MODERATOR_OUTLINED = "remove_moderator_outlined"
    REMOVE_RED_EYE = "remove_red_eye"
    REMOVE_RED_EYE_SHARP = "remove_red_eye_sharp"
    REMOVE_RED_EYE_ROUNDED = "remove_red_eye_rounded"
    REMOVE_RED_EYE_OUTLINED = "remove_red_eye_outlined"
    REMOVE_ROAD = "remove_road"
    REMOVE_ROAD_SHARP = "remove_road_sharp"
    REMOVE_ROAD_ROUNDED = "remove_road_rounded"
    REMOVE_ROAD_OUTLINED = "remove_road_outlined"
    REMOVE_SHOPPING_CART = "remove_shopping_cart"
    REMOVE_SHOPPING_CART_SHARP = "remove_shopping_cart_sharp"
    REMOVE_SHOPPING_CART_ROUNDED = "remove_shopping_cart_rounded"
    REMOVE_SHOPPING_CART_OUTLINED = "remove_shopping_cart_outlined"
    REORDER = "reorder"
    REORDER_SHARP = "reorder_sharp"
    REORDER_ROUNDED = "reorder_rounded"
    REORDER_OUTLINED = "reorder_outlined"
    REPARTITION = "repartition"
    REPARTITION_SHARP = "repartition_sharp"
    REPARTITION_ROUNDED = "repartition_rounded"
    REPARTITION_OUTLINED = "repartition_outlined"
    REPEAT = "repeat"
    REPEAT_SHARP = "repeat_sharp"
    REPEAT_ROUNDED = "repeat_rounded"
    REPEAT_OUTLINED = "repeat_outlined"
    REPEAT_ON = "repeat_on"
    REPEAT_ON_SHARP = "repeat_on_sharp"
    REPEAT_ON_ROUNDED = "repeat_on_rounded"
    REPEAT_ON_OUTLINED = "repeat_on_outlined"
    REPEAT_ONE = "repeat_one"
    REPEAT_ONE_SHARP = "repeat_one_sharp"
    REPEAT_ONE_ROUNDED = "repeat_one_rounded"
    REPEAT_ONE_OUTLINED = "repeat_one_outlined"
    REPEAT_ONE_ON = "repeat_one_on"
    REPEAT_ONE_ON_SHARP = "repeat_one_on_sharp"
    REPEAT_ONE_ON_ROUNDED = "repeat_one_on_rounded"
    REPEAT_ONE_ON_OUTLINED = "repeat_one_on_outlined"
    REPLAY = "replay"
    REPLAY_SHARP = "replay_sharp"
    REPLAY_ROUNDED = "replay_rounded"
    REPLAY_OUTLINED = "replay_outlined"
    REPLAY_10 = "replay_10"
    REPLAY_10_SHARP = "replay_10_sharp"
    REPLAY_10_ROUNDED = "replay_10_rounded"
    REPLAY_10_OUTLINED = "replay_10_outlined"
    REPLAY_30 = "replay_30"
    REPLAY_30_SHARP = "replay_30_sharp"
    REPLAY_30_ROUNDED = "replay_30_rounded"
    REPLAY_30_OUTLINED = "replay_30_outlined"
    REPLAY_5 = "replay_5"
    REPLAY_5_SHARP = "replay_5_sharp"
    REPLAY_5_ROUNDED = "replay_5_rounded"
    REPLAY_5_OUTLINED = "replay_5_outlined"
    REPLAY_CIRCLE_FILLED = "replay_circle_filled"
    REPLAY_CIRCLE_FILLED_SHARP = "replay_circle_filled_sharp"
    REPLAY_CIRCLE_FILLED_ROUNDED = "replay_circle_filled_rounded"
    REPLAY_CIRCLE_FILLED_OUTLINED = "replay_circle_filled_outlined"
    REPLY = "reply"
    REPLY_SHARP = "reply_sharp"
    REPLY_ROUNDED = "reply_rounded"
    REPLY_OUTLINED = "reply_outlined"
    REPLY_ALL = "reply_all"
    REPLY_ALL_SHARP = "reply_all_sharp"
    REPLY_ALL_ROUNDED = "reply_all_rounded"
    REPLY_ALL_OUTLINED = "reply_all_outlined"
    REPORT = "report"
    REPORT_SHARP = "report_sharp"
    REPORT_ROUNDED = "report_rounded"
    REPORT_OUTLINED = "report_outlined"
    REPORT_GMAILERRORRED = "report_gmailerrorred"
    REPORT_GMAILERRORRED_SHARP = "report_gmailerrorred_sharp"
    REPORT_GMAILERRORRED_ROUNDED = "report_gmailerrorred_rounded"
    REPORT_GMAILERRORRED_OUTLINED = "report_gmailerrorred_outlined"
    REPORT_OFF = "report_off"
    REPORT_OFF_SHARP = "report_off_sharp"
    REPORT_OFF_ROUNDED = "report_off_rounded"
    REPORT_OFF_OUTLINED = "report_off_outlined"
    REPORT_PROBLEM = "report_problem"
    REPORT_PROBLEM_SHARP = "report_problem_sharp"
    REPORT_PROBLEM_ROUNDED = "report_problem_rounded"
    REPORT_PROBLEM_OUTLINED = "report_problem_outlined"
    REQUEST_PAGE = "request_page"
    REQUEST_PAGE_SHARP = "request_page_sharp"
    REQUEST_PAGE_ROUNDED = "request_page_rounded"
    REQUEST_PAGE_OUTLINED = "request_page_outlined"
    REQUEST_QUOTE = "request_quote"
    REQUEST_QUOTE_SHARP = "request_quote_sharp"
    REQUEST_QUOTE_ROUNDED = "request_quote_rounded"
    REQUEST_QUOTE_OUTLINED = "request_quote_outlined"
    RESET_TV = "reset_tv"
    RESET_TV_SHARP = "reset_tv_sharp"
    RESET_TV_ROUNDED = "reset_tv_rounded"
    RESET_TV_OUTLINED = "reset_tv_outlined"
    RESTART_ALT = "restart_alt"
    RESTART_ALT_SHARP = "restart_alt_sharp"
    RESTART_ALT_ROUNDED = "restart_alt_rounded"
    RESTART_ALT_OUTLINED = "restart_alt_outlined"
    RESTAURANT = "restaurant"
    RESTAURANT_SHARP = "restaurant_sharp"
    RESTAURANT_ROUNDED = "restaurant_rounded"
    RESTAURANT_OUTLINED = "restaurant_outlined"
    RESTAURANT_MENU = "restaurant_menu"
    RESTAURANT_MENU_SHARP = "restaurant_menu_sharp"
    RESTAURANT_MENU_ROUNDED = "restaurant_menu_rounded"
    RESTAURANT_MENU_OUTLINED = "restaurant_menu_outlined"
    RESTORE = "restore"
    RESTORE_SHARP = "restore_sharp"
    RESTORE_ROUNDED = "restore_rounded"
    RESTORE_OUTLINED = "restore_outlined"
    RESTORE_FROM_TRASH = "restore_from_trash"
    RESTORE_FROM_TRASH_SHARP = "restore_from_trash_sharp"
    RESTORE_FROM_TRASH_ROUNDED = "restore_from_trash_rounded"
    RESTORE_FROM_TRASH_OUTLINED = "restore_from_trash_outlined"
    RESTORE_PAGE = "restore_page"
    RESTORE_PAGE_SHARP = "restore_page_sharp"
    RESTORE_PAGE_ROUNDED = "restore_page_rounded"
    RESTORE_PAGE_OUTLINED = "restore_page_outlined"
    REVIEWS = "reviews"
    REVIEWS_SHARP = "reviews_sharp"
    REVIEWS_ROUNDED = "reviews_rounded"
    REVIEWS_OUTLINED = "reviews_outlined"
    RICE_BOWL = "rice_bowl"
    RICE_BOWL_SHARP = "rice_bowl_sharp"
    RICE_BOWL_ROUNDED = "rice_bowl_rounded"
    RICE_BOWL_OUTLINED = "rice_bowl_outlined"
    RING_VOLUME = "ring_volume"
    RING_VOLUME_SHARP = "ring_volume_sharp"
    RING_VOLUME_ROUNDED = "ring_volume_rounded"
    RING_VOLUME_OUTLINED = "ring_volume_outlined"
    ROCKET = "rocket"
    ROCKET_SHARP = "rocket_sharp"
    ROCKET_ROUNDED = "rocket_rounded"
    ROCKET_OUTLINED = "rocket_outlined"
    ROCKET_LAUNCH = "rocket_launch"
    ROCKET_LAUNCH_SHARP = "rocket_launch_sharp"
    ROCKET_LAUNCH_ROUNDED = "rocket_launch_rounded"
    ROCKET_LAUNCH_OUTLINED = "rocket_launch_outlined"
    ROLLER_SHADES = "roller_shades"
    ROLLER_SHADES_SHARP = "roller_shades_sharp"
    ROLLER_SHADES_ROUNDED = "roller_shades_rounded"
    ROLLER_SHADES_OUTLINED = "roller_shades_outlined"
    ROLLER_SHADES_CLOSED = "roller_shades_closed"
    ROLLER_SHADES_CLOSED_SHARP = "roller_shades_closed_sharp"
    ROLLER_SHADES_CLOSED_ROUNDED = "roller_shades_closed_rounded"
    ROLLER_SHADES_CLOSED_OUTLINED = "roller_shades_closed_outlined"
    ROLLER_SKATING = "roller_skating"
    ROLLER_SKATING_SHARP = "roller_skating_sharp"
    ROLLER_SKATING_ROUNDED = "roller_skating_rounded"
    ROLLER_SKATING_OUTLINED = "roller_skating_outlined"
    ROOFING = "roofing"
    ROOFING_SHARP = "roofing_sharp"
    ROOFING_ROUNDED = "roofing_rounded"
    ROOFING_OUTLINED = "roofing_outlined"
    ROOM = "room"
    ROOM_SHARP = "room_sharp"
    ROOM_ROUNDED = "room_rounded"
    ROOM_OUTLINED = "room_outlined"
    ROOM_PREFERENCES = "room_preferences"
    ROOM_PREFERENCES_SHARP = "room_preferences_sharp"
    ROOM_PREFERENCES_ROUNDED = "room_preferences_rounded"
    ROOM_PREFERENCES_OUTLINED = "room_preferences_outlined"
    ROOM_SERVICE = "room_service"
    ROOM_SERVICE_SHARP = "room_service_sharp"
    ROOM_SERVICE_ROUNDED = "room_service_rounded"
    ROOM_SERVICE_OUTLINED = "room_service_outlined"
    ROTATE_90_DEGREES_CCW = "rotate_90_degrees_ccw"
    ROTATE_90_DEGREES_CCW_SHARP = "rotate_90_degrees_ccw_sharp"
    ROTATE_90_DEGREES_CCW_ROUNDED = "rotate_90_degrees_ccw_rounded"
    ROTATE_90_DEGREES_CCW_OUTLINED = "rotate_90_degrees_ccw_outlined"
    ROTATE_90_DEGREES_CW = "rotate_90_degrees_cw"
    ROTATE_90_DEGREES_CW_SHARP = "rotate_90_degrees_cw_sharp"
    ROTATE_90_DEGREES_CW_ROUNDED = "rotate_90_degrees_cw_rounded"
    ROTATE_90_DEGREES_CW_OUTLINED = "rotate_90_degrees_cw_outlined"
    ROTATE_LEFT = "rotate_left"
    ROTATE_LEFT_SHARP = "rotate_left_sharp"
    ROTATE_LEFT_ROUNDED = "rotate_left_rounded"
    ROTATE_LEFT_OUTLINED = "rotate_left_outlined"
    ROTATE_RIGHT = "rotate_right"
    ROTATE_RIGHT_SHARP = "rotate_right_sharp"
    ROTATE_RIGHT_ROUNDED = "rotate_right_rounded"
    ROTATE_RIGHT_OUTLINED = "rotate_right_outlined"
    ROUNDABOUT_LEFT = "roundabout_left"
    ROUNDABOUT_LEFT_SHARP = "roundabout_left_sharp"
    ROUNDABOUT_LEFT_ROUNDED = "roundabout_left_rounded"
    ROUNDABOUT_LEFT_OUTLINED = "roundabout_left_outlined"
    ROUNDABOUT_RIGHT = "roundabout_right"
    ROUNDABOUT_RIGHT_SHARP = "roundabout_right_sharp"
    ROUNDABOUT_RIGHT_ROUNDED = "roundabout_right_rounded"
    ROUNDABOUT_RIGHT_OUTLINED = "roundabout_right_outlined"
    ROUNDED_CORNER = "rounded_corner"
    ROUNDED_CORNER_SHARP = "rounded_corner_sharp"
    ROUNDED_CORNER_ROUNDED = "rounded_corner_rounded"
    ROUNDED_CORNER_OUTLINED = "rounded_corner_outlined"
    ROUTE = "route"
    ROUTE_SHARP = "route_sharp"
    ROUTE_ROUNDED = "route_rounded"
    ROUTE_OUTLINED = "route_outlined"
    ROUTER = "router"
    ROUTER_SHARP = "router_sharp"
    ROUTER_ROUNDED = "router_rounded"
    ROUTER_OUTLINED = "router_outlined"
    ROWING = "rowing"
    ROWING_SHARP = "rowing_sharp"
    ROWING_ROUNDED = "rowing_rounded"
    ROWING_OUTLINED = "rowing_outlined"
    RSS_FEED = "rss_feed"
    RSS_FEED_SHARP = "rss_feed_sharp"
    RSS_FEED_ROUNDED = "rss_feed_rounded"
    RSS_FEED_OUTLINED = "rss_feed_outlined"
    RSVP = "rsvp"
    RSVP_SHARP = "rsvp_sharp"
    RSVP_ROUNDED = "rsvp_rounded"
    RSVP_OUTLINED = "rsvp_outlined"
    RTT = "rtt"
    RTT_SHARP = "rtt_sharp"
    RTT_ROUNDED = "rtt_rounded"
    RTT_OUTLINED = "rtt_outlined"
    RULE = "rule"
    RULE_SHARP = "rule_sharp"
    RULE_ROUNDED = "rule_rounded"
    RULE_OUTLINED = "rule_outlined"
    RULE_FOLDER = "rule_folder"
    RULE_FOLDER_SHARP = "rule_folder_sharp"
    RULE_FOLDER_ROUNDED = "rule_folder_rounded"
    RULE_FOLDER_OUTLINED = "rule_folder_outlined"
    RUN_CIRCLE = "run_circle"
    RUN_CIRCLE_SHARP = "run_circle_sharp"
    RUN_CIRCLE_ROUNDED = "run_circle_rounded"
    RUN_CIRCLE_OUTLINED = "run_circle_outlined"
    RUNNING_WITH_ERRORS = "running_with_errors"
    RUNNING_WITH_ERRORS_SHARP = "running_with_errors_sharp"
    RUNNING_WITH_ERRORS_ROUNDED = "running_with_errors_rounded"
    RUNNING_WITH_ERRORS_OUTLINED = "running_with_errors_outlined"
    RV_HOOKUP = "rv_hookup"
    RV_HOOKUP_SHARP = "rv_hookup_sharp"
    RV_HOOKUP_ROUNDED = "rv_hookup_rounded"
    RV_HOOKUP_OUTLINED = "rv_hookup_outlined"
    SAFETY_CHECK = "safety_check"
    SAFETY_CHECK_SHARP = "safety_check_sharp"
    SAFETY_CHECK_ROUNDED = "safety_check_rounded"
    SAFETY_CHECK_OUTLINED = "safety_check_outlined"
    SAFETY_DIVIDER = "safety_divider"
    SAFETY_DIVIDER_SHARP = "safety_divider_sharp"
    SAFETY_DIVIDER_ROUNDED = "safety_divider_rounded"
    SAFETY_DIVIDER_OUTLINED = "safety_divider_outlined"
    SAILING = "sailing"
    SAILING_SHARP = "sailing_sharp"
    SAILING_ROUNDED = "sailing_rounded"
    SAILING_OUTLINED = "sailing_outlined"
    SANITIZER = "sanitizer"
    SANITIZER_SHARP = "sanitizer_sharp"
    SANITIZER_ROUNDED = "sanitizer_rounded"
    SANITIZER_OUTLINED = "sanitizer_outlined"
    SATELLITE = "satellite"
    SATELLITE_SHARP = "satellite_sharp"
    SATELLITE_ROUNDED = "satellite_rounded"
    SATELLITE_OUTLINED = "satellite_outlined"
    SATELLITE_ALT = "satellite_alt"
    SATELLITE_ALT_SHARP = "satellite_alt_sharp"
    SATELLITE_ALT_ROUNDED = "satellite_alt_rounded"
    SATELLITE_ALT_OUTLINED = "satellite_alt_outlined"
    SAVE = "save"
    SAVE_SHARP = "save_sharp"
    SAVE_ROUNDED = "save_rounded"
    SAVE_OUTLINED = "save_outlined"
    SAVE_ALT = "save_alt"
    SAVE_ALT_SHARP = "save_alt_sharp"
    SAVE_ALT_ROUNDED = "save_alt_rounded"
    SAVE_ALT_OUTLINED = "save_alt_outlined"
    SAVE_AS = "save_as"
    SAVE_AS_SHARP = "save_as_sharp"
    SAVE_AS_ROUNDED = "save_as_rounded"
    SAVE_AS_OUTLINED = "save_as_outlined"
    SAVED_SEARCH = "saved_search"
    SAVED_SEARCH_SHARP = "saved_search_sharp"
    SAVED_SEARCH_ROUNDED = "saved_search_rounded"
    SAVED_SEARCH_OUTLINED = "saved_search_outlined"
    SAVINGS = "savings"
    SAVINGS_SHARP = "savings_sharp"
    SAVINGS_ROUNDED = "savings_rounded"
    SAVINGS_OUTLINED = "savings_outlined"
    SCALE = "scale"
    SCALE_SHARP = "scale_sharp"
    SCALE_ROUNDED = "scale_rounded"
    SCALE_OUTLINED = "scale_outlined"
    SCANNER = "scanner"
    SCANNER_SHARP = "scanner_sharp"
    SCANNER_ROUNDED = "scanner_rounded"
    SCANNER_OUTLINED = "scanner_outlined"
    SCATTER_PLOT = "scatter_plot"
    SCATTER_PLOT_SHARP = "scatter_plot_sharp"
    SCATTER_PLOT_ROUNDED = "scatter_plot_rounded"
    SCATTER_PLOT_OUTLINED = "scatter_plot_outlined"
    SCHEDULE = "schedule"
    SCHEDULE_SHARP = "schedule_sharp"
    SCHEDULE_ROUNDED = "schedule_rounded"
    SCHEDULE_OUTLINED = "schedule_outlined"
    SCHEDULE_SEND = "schedule_send"
    SCHEDULE_SEND_SHARP = "schedule_send_sharp"
    SCHEDULE_SEND_ROUNDED = "schedule_send_rounded"
    SCHEDULE_SEND_OUTLINED = "schedule_send_outlined"
    SCHEMA = "schema"
    SCHEMA_SHARP = "schema_sharp"
    SCHEMA_ROUNDED = "schema_rounded"
    SCHEMA_OUTLINED = "schema_outlined"
    SCHOOL = "school"
    SCHOOL_SHARP = "school_sharp"
    SCHOOL_ROUNDED = "school_rounded"
    SCHOOL_OUTLINED = "school_outlined"
    SCIENCE = "science"
    SCIENCE_SHARP = "science_sharp"
    SCIENCE_ROUNDED = "science_rounded"
    SCIENCE_OUTLINED = "science_outlined"
    SCORE = "score"
    SCORE_SHARP = "score_sharp"
    SCORE_ROUNDED = "score_rounded"
    SCORE_OUTLINED = "score_outlined"
    SCOREBOARD = "scoreboard"
    SCOREBOARD_SHARP = "scoreboard_sharp"
    SCOREBOARD_ROUNDED = "scoreboard_rounded"
    SCOREBOARD_OUTLINED = "scoreboard_outlined"
    SCREEN_LOCK_LANDSCAPE = "screen_lock_landscape"
    SCREEN_LOCK_LANDSCAPE_SHARP = "screen_lock_landscape_sharp"
    SCREEN_LOCK_LANDSCAPE_ROUNDED = "screen_lock_landscape_rounded"
    SCREEN_LOCK_LANDSCAPE_OUTLINED = "screen_lock_landscape_outlined"
    SCREEN_LOCK_PORTRAIT = "screen_lock_portrait"
    SCREEN_LOCK_PORTRAIT_SHARP = "screen_lock_portrait_sharp"
    SCREEN_LOCK_PORTRAIT_ROUNDED = "screen_lock_portrait_rounded"
    SCREEN_LOCK_PORTRAIT_OUTLINED = "screen_lock_portrait_outlined"
    SCREEN_LOCK_ROTATION = "screen_lock_rotation"
    SCREEN_LOCK_ROTATION_SHARP = "screen_lock_rotation_sharp"
    SCREEN_LOCK_ROTATION_ROUNDED = "screen_lock_rotation_rounded"
    SCREEN_LOCK_ROTATION_OUTLINED = "screen_lock_rotation_outlined"
    SCREEN_ROTATION = "screen_rotation"
    SCREEN_ROTATION_SHARP = "screen_rotation_sharp"
    SCREEN_ROTATION_ROUNDED = "screen_rotation_rounded"
    SCREEN_ROTATION_OUTLINED = "screen_rotation_outlined"
    SCREEN_ROTATION_ALT = "screen_rotation_alt"
    SCREEN_ROTATION_ALT_SHARP = "screen_rotation_alt_sharp"
    SCREEN_ROTATION_ALT_ROUNDED = "screen_rotation_alt_rounded"
    SCREEN_ROTATION_ALT_OUTLINED = "screen_rotation_alt_outlined"
    SCREEN_SEARCH_DESKTOP = "screen_search_desktop"
    SCREEN_SEARCH_DESKTOP_SHARP = "screen_search_desktop_sharp"
    SCREEN_SEARCH_DESKTOP_ROUNDED = "screen_search_desktop_rounded"
    SCREEN_SEARCH_DESKTOP_OUTLINED = "screen_search_desktop_outlined"
    SCREEN_SHARE = "screen_share"
    SCREEN_SHARE_SHARP = "screen_share_sharp"
    SCREEN_SHARE_ROUNDED = "screen_share_rounded"
    SCREEN_SHARE_OUTLINED = "screen_share_outlined"
    SCREENSHOT = "screenshot"
    SCREENSHOT_SHARP = "screenshot_sharp"
    SCREENSHOT_ROUNDED = "screenshot_rounded"
    SCREENSHOT_OUTLINED = "screenshot_outlined"
    SCREENSHOT_MONITOR = "screenshot_monitor"
    SCREENSHOT_MONITOR_SHARP = "screenshot_monitor_sharp"
    SCREENSHOT_MONITOR_ROUNDED = "screenshot_monitor_rounded"
    SCREENSHOT_MONITOR_OUTLINED = "screenshot_monitor_outlined"
    SCUBA_DIVING = "scuba_diving"
    SCUBA_DIVING_SHARP = "scuba_diving_sharp"
    SCUBA_DIVING_ROUNDED = "scuba_diving_rounded"
    SCUBA_DIVING_OUTLINED = "scuba_diving_outlined"
    SD = "sd"
    SD_SHARP = "sd_sharp"
    SD_ROUNDED = "sd_rounded"
    SD_OUTLINED = "sd_outlined"
    SD_CARD = "sd_card"
    SD_CARD_SHARP = "sd_card_sharp"
    SD_CARD_ROUNDED = "sd_card_rounded"
    SD_CARD_OUTLINED = "sd_card_outlined"
    SD_CARD_ALERT = "sd_card_alert"
    SD_CARD_ALERT_SHARP = "sd_card_alert_sharp"
    SD_CARD_ALERT_ROUNDED = "sd_card_alert_rounded"
    SD_CARD_ALERT_OUTLINED = "sd_card_alert_outlined"
    SD_STORAGE = "sd_storage"
    SD_STORAGE_SHARP = "sd_storage_sharp"
    SD_STORAGE_ROUNDED = "sd_storage_rounded"
    SD_STORAGE_OUTLINED = "sd_storage_outlined"
    SEARCH = "search"
    SEARCH_SHARP = "search_sharp"
    SEARCH_ROUNDED = "search_rounded"
    SEARCH_OUTLINED = "search_outlined"
    SEARCH_OFF = "search_off"
    SEARCH_OFF_SHARP = "search_off_sharp"
    SEARCH_OFF_ROUNDED = "search_off_rounded"
    SEARCH_OFF_OUTLINED = "search_off_outlined"
    SECURITY = "security"
    SECURITY_SHARP = "security_sharp"
    SECURITY_ROUNDED = "security_rounded"
    SECURITY_OUTLINED = "security_outlined"
    SECURITY_UPDATE = "security_update"
    SECURITY_UPDATE_SHARP = "security_update_sharp"
    SECURITY_UPDATE_ROUNDED = "security_update_rounded"
    SECURITY_UPDATE_OUTLINED = "security_update_outlined"
    SECURITY_UPDATE_GOOD = "security_update_good"
    SECURITY_UPDATE_GOOD_SHARP = "security_update_good_sharp"
    SECURITY_UPDATE_GOOD_ROUNDED = "security_update_good_rounded"
    SECURITY_UPDATE_GOOD_OUTLINED = "security_update_good_outlined"
    SECURITY_UPDATE_WARNING = "security_update_warning"
    SECURITY_UPDATE_WARNING_SHARP = "security_update_warning_sharp"
    SECURITY_UPDATE_WARNING_ROUNDED = "security_update_warning_rounded"
    SECURITY_UPDATE_WARNING_OUTLINED = "security_update_warning_outlined"
    SEGMENT = "segment"
    SEGMENT_SHARP = "segment_sharp"
    SEGMENT_ROUNDED = "segment_rounded"
    SEGMENT_OUTLINED = "segment_outlined"
    SELECT_ALL = "select_all"
    SELECT_ALL_SHARP = "select_all_sharp"
    SELECT_ALL_ROUNDED = "select_all_rounded"
    SELECT_ALL_OUTLINED = "select_all_outlined"
    SELF_IMPROVEMENT = "self_improvement"
    SELF_IMPROVEMENT_SHARP = "self_improvement_sharp"
    SELF_IMPROVEMENT_ROUNDED = "self_improvement_rounded"
    SELF_IMPROVEMENT_OUTLINED = "self_improvement_outlined"
    SELL = "sell"
    SELL_SHARP = "sell_sharp"
    SELL_ROUNDED = "sell_rounded"
    SELL_OUTLINED = "sell_outlined"
    SEND = "send"
    SEND_SHARP = "send_sharp"
    SEND_ROUNDED = "send_rounded"
    SEND_OUTLINED = "send_outlined"
    SEND_AND_ARCHIVE = "send_and_archive"
    SEND_AND_ARCHIVE_SHARP = "send_and_archive_sharp"
    SEND_AND_ARCHIVE_ROUNDED = "send_and_archive_rounded"
    SEND_AND_ARCHIVE_OUTLINED = "send_and_archive_outlined"
    SEND_TIME_EXTENSION = "send_time_extension"
    SEND_TIME_EXTENSION_SHARP = "send_time_extension_sharp"
    SEND_TIME_EXTENSION_ROUNDED = "send_time_extension_rounded"
    SEND_TIME_EXTENSION_OUTLINED = "send_time_extension_outlined"
    SEND_TO_MOBILE = "send_to_mobile"
    SEND_TO_MOBILE_SHARP = "send_to_mobile_sharp"
    SEND_TO_MOBILE_ROUNDED = "send_to_mobile_rounded"
    SEND_TO_MOBILE_OUTLINED = "send_to_mobile_outlined"
    SENSOR_DOOR = "sensor_door"
    SENSOR_DOOR_SHARP = "sensor_door_sharp"
    SENSOR_DOOR_ROUNDED = "sensor_door_rounded"
    SENSOR_DOOR_OUTLINED = "sensor_door_outlined"
    SENSOR_OCCUPIED = "sensor_occupied"
    SENSOR_OCCUPIED_SHARP = "sensor_occupied_sharp"
    SENSOR_OCCUPIED_ROUNDED = "sensor_occupied_rounded"
    SENSOR_OCCUPIED_OUTLINED = "sensor_occupied_outlined"
    SENSOR_WINDOW = "sensor_window"
    SENSOR_WINDOW_SHARP = "sensor_window_sharp"
    SENSOR_WINDOW_ROUNDED = "sensor_window_rounded"
    SENSOR_WINDOW_OUTLINED = "sensor_window_outlined"
    SENSORS = "sensors"
    SENSORS_SHARP = "sensors_sharp"
    SENSORS_ROUNDED = "sensors_rounded"
    SENSORS_OUTLINED = "sensors_outlined"
    SENSORS_OFF = "sensors_off"
    SENSORS_OFF_SHARP = "sensors_off_sharp"
    SENSORS_OFF_ROUNDED = "sensors_off_rounded"
    SENSORS_OFF_OUTLINED = "sensors_off_outlined"
    SENTIMENT_DISSATISFIED = "sentiment_dissatisfied"
    SENTIMENT_DISSATISFIED_SHARP = "sentiment_dissatisfied_sharp"
    SENTIMENT_DISSATISFIED_ROUNDED = "sentiment_dissatisfied_rounded"
    SENTIMENT_DISSATISFIED_OUTLINED = "sentiment_dissatisfied_outlined"
    SENTIMENT_NEUTRAL = "sentiment_neutral"
    SENTIMENT_NEUTRAL_SHARP = "sentiment_neutral_sharp"
    SENTIMENT_NEUTRAL_ROUNDED = "sentiment_neutral_rounded"
    SENTIMENT_NEUTRAL_OUTLINED = "sentiment_neutral_outlined"
    SENTIMENT_SATISFIED = "sentiment_satisfied"
    SENTIMENT_SATISFIED_SHARP = "sentiment_satisfied_sharp"
    SENTIMENT_SATISFIED_ROUNDED = "sentiment_satisfied_rounded"
    SENTIMENT_SATISFIED_OUTLINED = "sentiment_satisfied_outlined"
    SENTIMENT_SATISFIED_ALT = "sentiment_satisfied_alt"
    SENTIMENT_SATISFIED_ALT_SHARP = "sentiment_satisfied_alt_sharp"
    SENTIMENT_SATISFIED_ALT_ROUNDED = "sentiment_satisfied_alt_rounded"
    SENTIMENT_SATISFIED_ALT_OUTLINED = "sentiment_satisfied_alt_outlined"
    SENTIMENT_VERY_DISSATISFIED = "sentiment_very_dissatisfied"
    SENTIMENT_VERY_DISSATISFIED_SHARP = "sentiment_very_dissatisfied_sharp"
    SENTIMENT_VERY_DISSATISFIED_ROUNDED = "sentiment_very_dissatisfied_rounded"
    SENTIMENT_VERY_DISSATISFIED_OUTLINED = "sentiment_very_dissatisfied_outlined"
    SENTIMENT_VERY_SATISFIED = "sentiment_very_satisfied"
    SENTIMENT_VERY_SATISFIED_SHARP = "sentiment_very_satisfied_sharp"
    SENTIMENT_VERY_SATISFIED_ROUNDED = "sentiment_very_satisfied_rounded"
    SENTIMENT_VERY_SATISFIED_OUTLINED = "sentiment_very_satisfied_outlined"
    SET_MEAL = "set_meal"
    SET_MEAL_SHARP = "set_meal_sharp"
    SET_MEAL_ROUNDED = "set_meal_rounded"
    SET_MEAL_OUTLINED = "set_meal_outlined"
    SETTINGS = "settings"
    SETTINGS_SHARP = "settings_sharp"
    SETTINGS_ROUNDED = "settings_rounded"
    SETTINGS_OUTLINED = "settings_outlined"
    SETTINGS_ACCESSIBILITY = "settings_accessibility"
    SETTINGS_ACCESSIBILITY_SHARP = "settings_accessibility_sharp"
    SETTINGS_ACCESSIBILITY_ROUNDED = "settings_accessibility_rounded"
    SETTINGS_ACCESSIBILITY_OUTLINED = "settings_accessibility_outlined"
    SETTINGS_APPLICATIONS = "settings_applications"
    SETTINGS_APPLICATIONS_SHARP = "settings_applications_sharp"
    SETTINGS_APPLICATIONS_ROUNDED = "settings_applications_rounded"
    SETTINGS_APPLICATIONS_OUTLINED = "settings_applications_outlined"
    SETTINGS_BACKUP_RESTORE = "settings_backup_restore"
    SETTINGS_BACKUP_RESTORE_SHARP = "settings_backup_restore_sharp"
    SETTINGS_BACKUP_RESTORE_ROUNDED = "settings_backup_restore_rounded"
    SETTINGS_BACKUP_RESTORE_OUTLINED = "settings_backup_restore_outlined"
    SETTINGS_BLUETOOTH = "settings_bluetooth"
    SETTINGS_BLUETOOTH_SHARP = "settings_bluetooth_sharp"
    SETTINGS_BLUETOOTH_ROUNDED = "settings_bluetooth_rounded"
    SETTINGS_BLUETOOTH_OUTLINED = "settings_bluetooth_outlined"
    SETTINGS_BRIGHTNESS = "settings_brightness"
    SETTINGS_BRIGHTNESS_SHARP = "settings_brightness_sharp"
    SETTINGS_BRIGHTNESS_ROUNDED = "settings_brightness_rounded"
    SETTINGS_BRIGHTNESS_OUTLINED = "settings_brightness_outlined"
    SETTINGS_CELL = "settings_cell"
    SETTINGS_CELL_SHARP = "settings_cell_sharp"
    SETTINGS_CELL_ROUNDED = "settings_cell_rounded"
    SETTINGS_CELL_OUTLINED = "settings_cell_outlined"
    SETTINGS_DISPLAY = "settings_display"
    SETTINGS_DISPLAY_SHARP = "settings_display_sharp"
    SETTINGS_DISPLAY_ROUNDED = "settings_display_rounded"
    SETTINGS_DISPLAY_OUTLINED = "settings_display_outlined"
    SETTINGS_ETHERNET = "settings_ethernet"
    SETTINGS_ETHERNET_SHARP = "settings_ethernet_sharp"
    SETTINGS_ETHERNET_ROUNDED = "settings_ethernet_rounded"
    SETTINGS_ETHERNET_OUTLINED = "settings_ethernet_outlined"
    SETTINGS_INPUT_ANTENNA = "settings_input_antenna"
    SETTINGS_INPUT_ANTENNA_SHARP = "settings_input_antenna_sharp"
    SETTINGS_INPUT_ANTENNA_ROUNDED = "settings_input_antenna_rounded"
    SETTINGS_INPUT_ANTENNA_OUTLINED = "settings_input_antenna_outlined"
    SETTINGS_INPUT_COMPONENT = "settings_input_component"
    SETTINGS_INPUT_COMPONENT_SHARP = "settings_input_component_sharp"
    SETTINGS_INPUT_COMPONENT_ROUNDED = "settings_input_component_rounded"
    SETTINGS_INPUT_COMPONENT_OUTLINED = "settings_input_component_outlined"
    SETTINGS_INPUT_COMPOSITE = "settings_input_composite"
    SETTINGS_INPUT_COMPOSITE_SHARP = "settings_input_composite_sharp"
    SETTINGS_INPUT_COMPOSITE_ROUNDED = "settings_input_composite_rounded"
    SETTINGS_INPUT_COMPOSITE_OUTLINED = "settings_input_composite_outlined"
    SETTINGS_INPUT_HDMI = "settings_input_hdmi"
    SETTINGS_INPUT_HDMI_SHARP = "settings_input_hdmi_sharp"
    SETTINGS_INPUT_HDMI_ROUNDED = "settings_input_hdmi_rounded"
    SETTINGS_INPUT_HDMI_OUTLINED = "settings_input_hdmi_outlined"
    SETTINGS_INPUT_SVIDEO = "settings_input_svideo"
    SETTINGS_INPUT_SVIDEO_SHARP = "settings_input_svideo_sharp"
    SETTINGS_INPUT_SVIDEO_ROUNDED = "settings_input_svideo_rounded"
    SETTINGS_INPUT_SVIDEO_OUTLINED = "settings_input_svideo_outlined"
    SETTINGS_OVERSCAN = "settings_overscan"
    SETTINGS_OVERSCAN_SHARP = "settings_overscan_sharp"
    SETTINGS_OVERSCAN_ROUNDED = "settings_overscan_rounded"
    SETTINGS_OVERSCAN_OUTLINED = "settings_overscan_outlined"
    SETTINGS_PHONE = "settings_phone"
    SETTINGS_PHONE_SHARP = "settings_phone_sharp"
    SETTINGS_PHONE_ROUNDED = "settings_phone_rounded"
    SETTINGS_PHONE_OUTLINED = "settings_phone_outlined"
    SETTINGS_POWER = "settings_power"
    SETTINGS_POWER_SHARP = "settings_power_sharp"
    SETTINGS_POWER_ROUNDED = "settings_power_rounded"
    SETTINGS_POWER_OUTLINED = "settings_power_outlined"
    SETTINGS_REMOTE = "settings_remote"
    SETTINGS_REMOTE_SHARP = "settings_remote_sharp"
    SETTINGS_REMOTE_ROUNDED = "settings_remote_rounded"
    SETTINGS_REMOTE_OUTLINED = "settings_remote_outlined"
    SETTINGS_SUGGEST = "settings_suggest"
    SETTINGS_SUGGEST_SHARP = "settings_suggest_sharp"
    SETTINGS_SUGGEST_ROUNDED = "settings_suggest_rounded"
    SETTINGS_SUGGEST_OUTLINED = "settings_suggest_outlined"
    SETTINGS_SYSTEM_DAYDREAM = "settings_system_daydream"
    SETTINGS_SYSTEM_DAYDREAM_SHARP = "settings_system_daydream_sharp"
    SETTINGS_SYSTEM_DAYDREAM_ROUNDED = "settings_system_daydream_rounded"
    SETTINGS_SYSTEM_DAYDREAM_OUTLINED = "settings_system_daydream_outlined"
    SETTINGS_VOICE = "settings_voice"
    SETTINGS_VOICE_SHARP = "settings_voice_sharp"
    SETTINGS_VOICE_ROUNDED = "settings_voice_rounded"
    SETTINGS_VOICE_OUTLINED = "settings_voice_outlined"
    SEVERE_COLD = "severe_cold"
    SEVERE_COLD_SHARP = "severe_cold_sharp"
    SEVERE_COLD_ROUNDED = "severe_cold_rounded"
    SEVERE_COLD_OUTLINED = "severe_cold_outlined"
    SHAPE_LINE = "shape_line"
    SHAPE_LINE_SHARP = "shape_line_sharp"
    SHAPE_LINE_ROUNDED = "shape_line_rounded"
    SHAPE_LINE_OUTLINED = "shape_line_outlined"
    SHARE = "share"
    SHARE_SHARP = "share_sharp"
    SHARE_ROUNDED = "share_rounded"
    SHARE_OUTLINED = "share_outlined"
    SHARE_ARRIVAL_TIME = "share_arrival_time"
    SHARE_ARRIVAL_TIME_SHARP = "share_arrival_time_sharp"
    SHARE_ARRIVAL_TIME_ROUNDED = "share_arrival_time_rounded"
    SHARE_ARRIVAL_TIME_OUTLINED = "share_arrival_time_outlined"
    SHARE_LOCATION = "share_location"
    SHARE_LOCATION_SHARP = "share_location_sharp"
    SHARE_LOCATION_ROUNDED = "share_location_rounded"
    SHARE_LOCATION_OUTLINED = "share_location_outlined"
    SHELVES = "shelves"
    SHIELD = "shield"
    SHIELD_SHARP = "shield_sharp"
    SHIELD_ROUNDED = "shield_rounded"
    SHIELD_OUTLINED = "shield_outlined"
    SHIELD_MOON = "shield_moon"
    SHIELD_MOON_SHARP = "shield_moon_sharp"
    SHIELD_MOON_ROUNDED = "shield_moon_rounded"
    SHIELD_MOON_OUTLINED = "shield_moon_outlined"
    SHOP = "shop"
    SHOP_SHARP = "shop_sharp"
    SHOP_ROUNDED = "shop_rounded"
    SHOP_OUTLINED = "shop_outlined"
    SHOP_2 = "shop_2"
    SHOP_2_SHARP = "shop_2_sharp"
    SHOP_2_ROUNDED = "shop_2_rounded"
    SHOP_2_OUTLINED = "shop_2_outlined"
    SHOP_TWO = "shop_two"
    SHOP_TWO_SHARP = "shop_two_sharp"
    SHOP_TWO_ROUNDED = "shop_two_rounded"
    SHOP_TWO_OUTLINED = "shop_two_outlined"
    SHOPIFY = "shopify"
    SHOPIFY_SHARP = "shopify_sharp"
    SHOPIFY_ROUNDED = "shopify_rounded"
    SHOPIFY_OUTLINED = "shopify_outlined"
    SHOPPING_BAG = "shopping_bag"
    SHOPPING_BAG_SHARP = "shopping_bag_sharp"
    SHOPPING_BAG_ROUNDED = "shopping_bag_rounded"
    SHOPPING_BAG_OUTLINED = "shopping_bag_outlined"
    SHOPPING_BASKET = "shopping_basket"
    SHOPPING_BASKET_SHARP = "shopping_basket_sharp"
    SHOPPING_BASKET_ROUNDED = "shopping_basket_rounded"
    SHOPPING_BASKET_OUTLINED = "shopping_basket_outlined"
    SHOPPING_CART = "shopping_cart"
    SHOPPING_CART_SHARP = "shopping_cart_sharp"
    SHOPPING_CART_ROUNDED = "shopping_cart_rounded"
    SHOPPING_CART_OUTLINED = "shopping_cart_outlined"
    SHOPPING_CART_CHECKOUT = "shopping_cart_checkout"
    SHOPPING_CART_CHECKOUT_SHARP = "shopping_cart_checkout_sharp"
    SHOPPING_CART_CHECKOUT_ROUNDED = "shopping_cart_checkout_rounded"
    SHOPPING_CART_CHECKOUT_OUTLINED = "shopping_cart_checkout_outlined"
    SHORT_TEXT = "short_text"
    SHORT_TEXT_SHARP = "short_text_sharp"
    SHORT_TEXT_ROUNDED = "short_text_rounded"
    SHORT_TEXT_OUTLINED = "short_text_outlined"
    SHORTCUT = "shortcut"
    SHORTCUT_SHARP = "shortcut_sharp"
    SHORTCUT_ROUNDED = "shortcut_rounded"
    SHORTCUT_OUTLINED = "shortcut_outlined"
    SHOW_CHART = "show_chart"
    SHOW_CHART_SHARP = "show_chart_sharp"
    SHOW_CHART_ROUNDED = "show_chart_rounded"
    SHOW_CHART_OUTLINED = "show_chart_outlined"
    SHOWER = "shower"
    SHOWER_SHARP = "shower_sharp"
    SHOWER_ROUNDED = "shower_rounded"
    SHOWER_OUTLINED = "shower_outlined"
    SHUFFLE = "shuffle"
    SHUFFLE_SHARP = "shuffle_sharp"
    SHUFFLE_ROUNDED = "shuffle_rounded"
    SHUFFLE_OUTLINED = "shuffle_outlined"
    SHUFFLE_ON = "shuffle_on"
    SHUFFLE_ON_SHARP = "shuffle_on_sharp"
    SHUFFLE_ON_ROUNDED = "shuffle_on_rounded"
    SHUFFLE_ON_OUTLINED = "shuffle_on_outlined"
    SHUTTER_SPEED = "shutter_speed"
    SHUTTER_SPEED_SHARP = "shutter_speed_sharp"
    SHUTTER_SPEED_ROUNDED = "shutter_speed_rounded"
    SHUTTER_SPEED_OUTLINED = "shutter_speed_outlined"
    SICK = "sick"
    SICK_SHARP = "sick_sharp"
    SICK_ROUNDED = "sick_rounded"
    SICK_OUTLINED = "sick_outlined"
    SIGN_LANGUAGE = "sign_language"
    SIGN_LANGUAGE_SHARP = "sign_language_sharp"
    SIGN_LANGUAGE_ROUNDED = "sign_language_rounded"
    SIGN_LANGUAGE_OUTLINED = "sign_language_outlined"
    SIGNAL_CELLULAR_0_BAR = "signal_cellular_0_bar"
    SIGNAL_CELLULAR_0_BAR_SHARP = "signal_cellular_0_bar_sharp"
    SIGNAL_CELLULAR_0_BAR_ROUNDED = "signal_cellular_0_bar_rounded"
    SIGNAL_CELLULAR_0_BAR_OUTLINED = "signal_cellular_0_bar_outlined"
    SIGNAL_CELLULAR_4_BAR = "signal_cellular_4_bar"
    SIGNAL_CELLULAR_4_BAR_SHARP = "signal_cellular_4_bar_sharp"
    SIGNAL_CELLULAR_4_BAR_ROUNDED = "signal_cellular_4_bar_rounded"
    SIGNAL_CELLULAR_4_BAR_OUTLINED = "signal_cellular_4_bar_outlined"
    SIGNAL_CELLULAR_ALT = "signal_cellular_alt"
    SIGNAL_CELLULAR_ALT_SHARP = "signal_cellular_alt_sharp"
    SIGNAL_CELLULAR_ALT_ROUNDED = "signal_cellular_alt_rounded"
    SIGNAL_CELLULAR_ALT_OUTLINED = "signal_cellular_alt_outlined"
    SIGNAL_CELLULAR_ALT_1_BAR = "signal_cellular_alt_1_bar"
    SIGNAL_CELLULAR_ALT_1_BAR_SHARP = "signal_cellular_alt_1_bar_sharp"
    SIGNAL_CELLULAR_ALT_1_BAR_ROUNDED = "signal_cellular_alt_1_bar_rounded"
    SIGNAL_CELLULAR_ALT_1_BAR_OUTLINED = "signal_cellular_alt_1_bar_outlined"
    SIGNAL_CELLULAR_ALT_2_BAR = "signal_cellular_alt_2_bar"
    SIGNAL_CELLULAR_ALT_2_BAR_SHARP = "signal_cellular_alt_2_bar_sharp"
    SIGNAL_CELLULAR_ALT_2_BAR_ROUNDED = "signal_cellular_alt_2_bar_rounded"
    SIGNAL_CELLULAR_ALT_2_BAR_OUTLINED = "signal_cellular_alt_2_bar_outlined"
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_0_BAR = (
        "signal_cellular_connected_no_internet_0_bar"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_0_BAR_SHARP = (
        "signal_cellular_connected_no_internet_0_bar_sharp"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_0_BAR_ROUNDED = (
        "signal_cellular_connected_no_internet_0_bar_rounded"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_0_BAR_OUTLINED = (
        "signal_cellular_connected_no_internet_0_bar_outlined"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_4_BAR = (
        "signal_cellular_connected_no_internet_4_bar"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_4_BAR_SHARP = (
        "signal_cellular_connected_no_internet_4_bar_sharp"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_4_BAR_ROUNDED = (
        "signal_cellular_connected_no_internet_4_bar_rounded"
    )
    SIGNAL_CELLULAR_CONNECTED_NO_INTERNET_4_BAR_OUTLINED = (
        "signal_cellular_connected_no_internet_4_bar_outlined"
    )
    SIGNAL_CELLULAR_NO_SIM = "signal_cellular_no_sim"
    SIGNAL_CELLULAR_NO_SIM_SHARP = "signal_cellular_no_sim_sharp"
    SIGNAL_CELLULAR_NO_SIM_ROUNDED = "signal_cellular_no_sim_rounded"
    SIGNAL_CELLULAR_NO_SIM_OUTLINED = "signal_cellular_no_sim_outlined"
    SIGNAL_CELLULAR_NODATA = "signal_cellular_nodata"
    SIGNAL_CELLULAR_NODATA_SHARP = "signal_cellular_nodata_sharp"
    SIGNAL_CELLULAR_NODATA_ROUNDED = "signal_cellular_nodata_rounded"
    SIGNAL_CELLULAR_NODATA_OUTLINED = "signal_cellular_nodata_outlined"
    SIGNAL_CELLULAR_NULL = "signal_cellular_null"
    SIGNAL_CELLULAR_NULL_SHARP = "signal_cellular_null_sharp"
    SIGNAL_CELLULAR_NULL_ROUNDED = "signal_cellular_null_rounded"
    SIGNAL_CELLULAR_NULL_OUTLINED = "signal_cellular_null_outlined"
    SIGNAL_CELLULAR_OFF = "signal_cellular_off"
    SIGNAL_CELLULAR_OFF_SHARP = "signal_cellular_off_sharp"
    SIGNAL_CELLULAR_OFF_ROUNDED = "signal_cellular_off_rounded"
    SIGNAL_CELLULAR_OFF_OUTLINED = "signal_cellular_off_outlined"
    SIGNAL_WIFI_0_BAR = "signal_wifi_0_bar"
    SIGNAL_WIFI_0_BAR_SHARP = "signal_wifi_0_bar_sharp"
    SIGNAL_WIFI_0_BAR_ROUNDED = "signal_wifi_0_bar_rounded"
    SIGNAL_WIFI_0_BAR_OUTLINED = "signal_wifi_0_bar_outlined"
    SIGNAL_WIFI_4_BAR = "signal_wifi_4_bar"
    SIGNAL_WIFI_4_BAR_SHARP = "signal_wifi_4_bar_sharp"
    SIGNAL_WIFI_4_BAR_ROUNDED = "signal_wifi_4_bar_rounded"
    SIGNAL_WIFI_4_BAR_OUTLINED = "signal_wifi_4_bar_outlined"
    SIGNAL_WIFI_4_BAR_LOCK = "signal_wifi_4_bar_lock"
    SIGNAL_WIFI_4_BAR_LOCK_SHARP = "signal_wifi_4_bar_lock_sharp"
    SIGNAL_WIFI_4_BAR_LOCK_ROUNDED = "signal_wifi_4_bar_lock_rounded"
    SIGNAL_WIFI_4_BAR_LOCK_OUTLINED = "signal_wifi_4_bar_lock_outlined"
    SIGNAL_WIFI_BAD = "signal_wifi_bad"
    SIGNAL_WIFI_BAD_SHARP = "signal_wifi_bad_sharp"
    SIGNAL_WIFI_BAD_ROUNDED = "signal_wifi_bad_rounded"
    SIGNAL_WIFI_BAD_OUTLINED = "signal_wifi_bad_outlined"
    SIGNAL_WIFI_CONNECTED_NO_INTERNET_4 = "signal_wifi_connected_no_internet_4"
    SIGNAL_WIFI_CONNECTED_NO_INTERNET_4_SHARP = (
        "signal_wifi_connected_no_internet_4_sharp"
    )
    SIGNAL_WIFI_CONNECTED_NO_INTERNET_4_ROUNDED = (
        "signal_wifi_connected_no_internet_4_rounded"
    )
    SIGNAL_WIFI_CONNECTED_NO_INTERNET_4_OUTLINED = (
        "signal_wifi_connected_no_internet_4_outlined"
    )
    SIGNAL_WIFI_OFF = "signal_wifi_off"
    SIGNAL_WIFI_OFF_SHARP = "signal_wifi_off_sharp"
    SIGNAL_WIFI_OFF_ROUNDED = "signal_wifi_off_rounded"
    SIGNAL_WIFI_OFF_OUTLINED = "signal_wifi_off_outlined"
    SIGNAL_WIFI_STATUSBAR_4_BAR = "signal_wifi_statusbar_4_bar"
    SIGNAL_WIFI_STATUSBAR_4_BAR_SHARP = "signal_wifi_statusbar_4_bar_sharp"
    SIGNAL_WIFI_STATUSBAR_4_BAR_ROUNDED = "signal_wifi_statusbar_4_bar_rounded"
    SIGNAL_WIFI_STATUSBAR_4_BAR_OUTLINED = "signal_wifi_statusbar_4_bar_outlined"
    SIGNAL_WIFI_STATUSBAR_CONNECTED_NO_INTERNET_4 = (
        "signal_wifi_statusbar_connected_no_internet_4"
    )
    SIGNAL_WIFI_STATUSBAR_CONNECTED_NO_INTERNET_4_SHARP = (
        "signal_wifi_statusbar_connected_no_internet_4_sharp"
    )
    SIGNAL_WIFI_STATUSBAR_CONNECTED_NO_INTERNET_4_ROUNDED = (
        "signal_wifi_statusbar_connected_no_internet_4_rounded"
    )
    SIGNAL_WIFI_STATUSBAR_CONNECTED_NO_INTERNET_4_OUTLINED = (
        "signal_wifi_statusbar_connected_no_internet_4_outlined"
    )
    SIGNAL_WIFI_STATUSBAR_NULL = "signal_wifi_statusbar_null"
    SIGNAL_WIFI_STATUSBAR_NULL_SHARP = "signal_wifi_statusbar_null_sharp"
    SIGNAL_WIFI_STATUSBAR_NULL_ROUNDED = "signal_wifi_statusbar_null_rounded"
    SIGNAL_WIFI_STATUSBAR_NULL_OUTLINED = "signal_wifi_statusbar_null_outlined"
    SIGNPOST = "signpost"
    SIGNPOST_SHARP = "signpost_sharp"
    SIGNPOST_ROUNDED = "signpost_rounded"
    SIGNPOST_OUTLINED = "signpost_outlined"
    SIM_CARD = "sim_card"
    SIM_CARD_SHARP = "sim_card_sharp"
    SIM_CARD_ROUNDED = "sim_card_rounded"
    SIM_CARD_OUTLINED = "sim_card_outlined"
    SIM_CARD_ALERT = "sim_card_alert"
    SIM_CARD_ALERT_SHARP = "sim_card_alert_sharp"
    SIM_CARD_ALERT_ROUNDED = "sim_card_alert_rounded"
    SIM_CARD_ALERT_OUTLINED = "sim_card_alert_outlined"
    SIM_CARD_DOWNLOAD = "sim_card_download"
    SIM_CARD_DOWNLOAD_SHARP = "sim_card_download_sharp"
    SIM_CARD_DOWNLOAD_ROUNDED = "sim_card_download_rounded"
    SIM_CARD_DOWNLOAD_OUTLINED = "sim_card_download_outlined"
    SINGLE_BED = "single_bed"
    SINGLE_BED_SHARP = "single_bed_sharp"
    SINGLE_BED_ROUNDED = "single_bed_rounded"
    SINGLE_BED_OUTLINED = "single_bed_outlined"
    SIP = "sip"
    SIP_SHARP = "sip_sharp"
    SIP_ROUNDED = "sip_rounded"
    SIP_OUTLINED = "sip_outlined"
    SKATEBOARDING = "skateboarding"
    SKATEBOARDING_SHARP = "skateboarding_sharp"
    SKATEBOARDING_ROUNDED = "skateboarding_rounded"
    SKATEBOARDING_OUTLINED = "skateboarding_outlined"
    SKIP_NEXT = "skip_next"
    SKIP_NEXT_SHARP = "skip_next_sharp"
    SKIP_NEXT_ROUNDED = "skip_next_rounded"
    SKIP_NEXT_OUTLINED = "skip_next_outlined"
    SKIP_PREVIOUS = "skip_previous"
    SKIP_PREVIOUS_SHARP = "skip_previous_sharp"
    SKIP_PREVIOUS_ROUNDED = "skip_previous_rounded"
    SKIP_PREVIOUS_OUTLINED = "skip_previous_outlined"
    SLEDDING = "sledding"
    SLEDDING_SHARP = "sledding_sharp"
    SLEDDING_ROUNDED = "sledding_rounded"
    SLEDDING_OUTLINED = "sledding_outlined"
    SLIDESHOW = "slideshow"
    SLIDESHOW_SHARP = "slideshow_sharp"
    SLIDESHOW_ROUNDED = "slideshow_rounded"
    SLIDESHOW_OUTLINED = "slideshow_outlined"
    SLOW_MOTION_VIDEO = "slow_motion_video"
    SLOW_MOTION_VIDEO_SHARP = "slow_motion_video_sharp"
    SLOW_MOTION_VIDEO_ROUNDED = "slow_motion_video_rounded"
    SLOW_MOTION_VIDEO_OUTLINED = "slow_motion_video_outlined"
    SMART_BUTTON = "smart_button"
    SMART_BUTTON_SHARP = "smart_button_sharp"
    SMART_BUTTON_ROUNDED = "smart_button_rounded"
    SMART_BUTTON_OUTLINED = "smart_button_outlined"
    SMART_DISPLAY = "smart_display"
    SMART_DISPLAY_SHARP = "smart_display_sharp"
    SMART_DISPLAY_ROUNDED = "smart_display_rounded"
    SMART_DISPLAY_OUTLINED = "smart_display_outlined"
    SMART_SCREEN = "smart_screen"
    SMART_SCREEN_SHARP = "smart_screen_sharp"
    SMART_SCREEN_ROUNDED = "smart_screen_rounded"
    SMART_SCREEN_OUTLINED = "smart_screen_outlined"
    SMART_TOY = "smart_toy"
    SMART_TOY_SHARP = "smart_toy_sharp"
    SMART_TOY_ROUNDED = "smart_toy_rounded"
    SMART_TOY_OUTLINED = "smart_toy_outlined"
    SMARTPHONE = "smartphone"
    SMARTPHONE_SHARP = "smartphone_sharp"
    SMARTPHONE_ROUNDED = "smartphone_rounded"
    SMARTPHONE_OUTLINED = "smartphone_outlined"
    SMOKE_FREE = "smoke_free"
    SMOKE_FREE_SHARP = "smoke_free_sharp"
    SMOKE_FREE_ROUNDED = "smoke_free_rounded"
    SMOKE_FREE_OUTLINED = "smoke_free_outlined"
    SMOKING_ROOMS = "smoking_rooms"
    SMOKING_ROOMS_SHARP = "smoking_rooms_sharp"
    SMOKING_ROOMS_ROUNDED = "smoking_rooms_rounded"
    SMOKING_ROOMS_OUTLINED = "smoking_rooms_outlined"
    SMS = "sms"
    SMS_SHARP = "sms_sharp"
    SMS_ROUNDED = "sms_rounded"
    SMS_OUTLINED = "sms_outlined"
    SMS_FAILED = "sms_failed"
    SMS_FAILED_SHARP = "sms_failed_sharp"
    SMS_FAILED_ROUNDED = "sms_failed_rounded"
    SMS_FAILED_OUTLINED = "sms_failed_outlined"
    SNAPCHAT = "snapchat"
    SNAPCHAT_SHARP = "snapchat_sharp"
    SNAPCHAT_ROUNDED = "snapchat_rounded"
    SNAPCHAT_OUTLINED = "snapchat_outlined"
    SNIPPET_FOLDER = "snippet_folder"
    SNIPPET_FOLDER_SHARP = "snippet_folder_sharp"
    SNIPPET_FOLDER_ROUNDED = "snippet_folder_rounded"
    SNIPPET_FOLDER_OUTLINED = "snippet_folder_outlined"
    SNOOZE = "snooze"
    SNOOZE_SHARP = "snooze_sharp"
    SNOOZE_ROUNDED = "snooze_rounded"
    SNOOZE_OUTLINED = "snooze_outlined"
    SNOWBOARDING = "snowboarding"
    SNOWBOARDING_SHARP = "snowboarding_sharp"
    SNOWBOARDING_ROUNDED = "snowboarding_rounded"
    SNOWBOARDING_OUTLINED = "snowboarding_outlined"
    SNOWING = "snowing"
    SNOWMOBILE = "snowmobile"
    SNOWMOBILE_SHARP = "snowmobile_sharp"
    SNOWMOBILE_ROUNDED = "snowmobile_rounded"
    SNOWMOBILE_OUTLINED = "snowmobile_outlined"
    SNOWSHOEING = "snowshoeing"
    SNOWSHOEING_SHARP = "snowshoeing_sharp"
    SNOWSHOEING_ROUNDED = "snowshoeing_rounded"
    SNOWSHOEING_OUTLINED = "snowshoeing_outlined"
    SOAP = "soap"
    SOAP_SHARP = "soap_sharp"
    SOAP_ROUNDED = "soap_rounded"
    SOAP_OUTLINED = "soap_outlined"
    SOCIAL_DISTANCE = "social_distance"
    SOCIAL_DISTANCE_SHARP = "social_distance_sharp"
    SOCIAL_DISTANCE_ROUNDED = "social_distance_rounded"
    SOCIAL_DISTANCE_OUTLINED = "social_distance_outlined"
    SOLAR_POWER = "solar_power"
    SOLAR_POWER_SHARP = "solar_power_sharp"
    SOLAR_POWER_ROUNDED = "solar_power_rounded"
    SOLAR_POWER_OUTLINED = "solar_power_outlined"
    SORT = "sort"
    SORT_SHARP = "sort_sharp"
    SORT_ROUNDED = "sort_rounded"
    SORT_OUTLINED = "sort_outlined"
    SORT_BY_ALPHA = "sort_by_alpha"
    SORT_BY_ALPHA_SHARP = "sort_by_alpha_sharp"
    SORT_BY_ALPHA_ROUNDED = "sort_by_alpha_rounded"
    SORT_BY_ALPHA_OUTLINED = "sort_by_alpha_outlined"
    SOS = "sos"
    SOS_SHARP = "sos_sharp"
    SOS_ROUNDED = "sos_rounded"
    SOS_OUTLINED = "sos_outlined"
    SOUP_KITCHEN = "soup_kitchen"
    SOUP_KITCHEN_SHARP = "soup_kitchen_sharp"
    SOUP_KITCHEN_ROUNDED = "soup_kitchen_rounded"
    SOUP_KITCHEN_OUTLINED = "soup_kitchen_outlined"
    SOURCE = "source"
    SOURCE_SHARP = "source_sharp"
    SOURCE_ROUNDED = "source_rounded"
    SOURCE_OUTLINED = "source_outlined"
    SOUTH = "south"
    SOUTH_SHARP = "south_sharp"
    SOUTH_ROUNDED = "south_rounded"
    SOUTH_OUTLINED = "south_outlined"
    SOUTH_AMERICA = "south_america"
    SOUTH_AMERICA_SHARP = "south_america_sharp"
    SOUTH_AMERICA_ROUNDED = "south_america_rounded"
    SOUTH_AMERICA_OUTLINED = "south_america_outlined"
    SOUTH_EAST = "south_east"
    SOUTH_EAST_SHARP = "south_east_sharp"
    SOUTH_EAST_ROUNDED = "south_east_rounded"
    SOUTH_EAST_OUTLINED = "south_east_outlined"
    SOUTH_WEST = "south_west"
    SOUTH_WEST_SHARP = "south_west_sharp"
    SOUTH_WEST_ROUNDED = "south_west_rounded"
    SOUTH_WEST_OUTLINED = "south_west_outlined"
    SPA = "spa"
    SPA_SHARP = "spa_sharp"
    SPA_ROUNDED = "spa_rounded"
    SPA_OUTLINED = "spa_outlined"
    SPACE_BAR = "space_bar"
    SPACE_BAR_SHARP = "space_bar_sharp"
    SPACE_BAR_ROUNDED = "space_bar_rounded"
    SPACE_BAR_OUTLINED = "space_bar_outlined"
    SPACE_DASHBOARD = "space_dashboard"
    SPACE_DASHBOARD_SHARP = "space_dashboard_sharp"
    SPACE_DASHBOARD_ROUNDED = "space_dashboard_rounded"
    SPACE_DASHBOARD_OUTLINED = "space_dashboard_outlined"
    SPATIAL_AUDIO = "spatial_audio"
    SPATIAL_AUDIO_SHARP = "spatial_audio_sharp"
    SPATIAL_AUDIO_ROUNDED = "spatial_audio_rounded"
    SPATIAL_AUDIO_OUTLINED = "spatial_audio_outlined"
    SPATIAL_AUDIO_OFF = "spatial_audio_off"
    SPATIAL_AUDIO_OFF_SHARP = "spatial_audio_off_sharp"
    SPATIAL_AUDIO_OFF_ROUNDED = "spatial_audio_off_rounded"
    SPATIAL_AUDIO_OFF_OUTLINED = "spatial_audio_off_outlined"
    SPATIAL_TRACKING = "spatial_tracking"
    SPATIAL_TRACKING_SHARP = "spatial_tracking_sharp"
    SPATIAL_TRACKING_ROUNDED = "spatial_tracking_rounded"
    SPATIAL_TRACKING_OUTLINED = "spatial_tracking_outlined"
    SPEAKER = "speaker"
    SPEAKER_SHARP = "speaker_sharp"
    SPEAKER_ROUNDED = "speaker_rounded"
    SPEAKER_OUTLINED = "speaker_outlined"
    SPEAKER_GROUP = "speaker_group"
    SPEAKER_GROUP_SHARP = "speaker_group_sharp"
    SPEAKER_GROUP_ROUNDED = "speaker_group_rounded"
    SPEAKER_GROUP_OUTLINED = "speaker_group_outlined"
    SPEAKER_NOTES = "speaker_notes"
    SPEAKER_NOTES_SHARP = "speaker_notes_sharp"
    SPEAKER_NOTES_ROUNDED = "speaker_notes_rounded"
    SPEAKER_NOTES_OUTLINED = "speaker_notes_outlined"
    SPEAKER_NOTES_OFF = "speaker_notes_off"
    SPEAKER_NOTES_OFF_SHARP = "speaker_notes_off_sharp"
    SPEAKER_NOTES_OFF_ROUNDED = "speaker_notes_off_rounded"
    SPEAKER_NOTES_OFF_OUTLINED = "speaker_notes_off_outlined"
    SPEAKER_PHONE = "speaker_phone"
    SPEAKER_PHONE_SHARP = "speaker_phone_sharp"
    SPEAKER_PHONE_ROUNDED = "speaker_phone_rounded"
    SPEAKER_PHONE_OUTLINED = "speaker_phone_outlined"
    SPEED = "speed"
    SPEED_SHARP = "speed_sharp"
    SPEED_ROUNDED = "speed_rounded"
    SPEED_OUTLINED = "speed_outlined"
    SPELLCHECK = "spellcheck"
    SPELLCHECK_SHARP = "spellcheck_sharp"
    SPELLCHECK_ROUNDED = "spellcheck_rounded"
    SPELLCHECK_OUTLINED = "spellcheck_outlined"
    SPLITSCREEN = "splitscreen"
    SPLITSCREEN_SHARP = "splitscreen_sharp"
    SPLITSCREEN_ROUNDED = "splitscreen_rounded"
    SPLITSCREEN_OUTLINED = "splitscreen_outlined"
    SPOKE = "spoke"
    SPOKE_SHARP = "spoke_sharp"
    SPOKE_ROUNDED = "spoke_rounded"
    SPOKE_OUTLINED = "spoke_outlined"
    SPORTS = "sports"
    SPORTS_SHARP = "sports_sharp"
    SPORTS_ROUNDED = "sports_rounded"
    SPORTS_OUTLINED = "sports_outlined"
    SPORTS_BAR = "sports_bar"
    SPORTS_BAR_SHARP = "sports_bar_sharp"
    SPORTS_BAR_ROUNDED = "sports_bar_rounded"
    SPORTS_BAR_OUTLINED = "sports_bar_outlined"
    SPORTS_BASEBALL = "sports_baseball"
    SPORTS_BASEBALL_SHARP = "sports_baseball_sharp"
    SPORTS_BASEBALL_ROUNDED = "sports_baseball_rounded"
    SPORTS_BASEBALL_OUTLINED = "sports_baseball_outlined"
    SPORTS_BASKETBALL = "sports_basketball"
    SPORTS_BASKETBALL_SHARP = "sports_basketball_sharp"
    SPORTS_BASKETBALL_ROUNDED = "sports_basketball_rounded"
    SPORTS_BASKETBALL_OUTLINED = "sports_basketball_outlined"
    SPORTS_CRICKET = "sports_cricket"
    SPORTS_CRICKET_SHARP = "sports_cricket_sharp"
    SPORTS_CRICKET_ROUNDED = "sports_cricket_rounded"
    SPORTS_CRICKET_OUTLINED = "sports_cricket_outlined"
    SPORTS_ESPORTS = "sports_esports"
    SPORTS_ESPORTS_SHARP = "sports_esports_sharp"
    SPORTS_ESPORTS_ROUNDED = "sports_esports_rounded"
    SPORTS_ESPORTS_OUTLINED = "sports_esports_outlined"
    SPORTS_FOOTBALL = "sports_football"
    SPORTS_FOOTBALL_SHARP = "sports_football_sharp"
    SPORTS_FOOTBALL_ROUNDED = "sports_football_rounded"
    SPORTS_FOOTBALL_OUTLINED = "sports_football_outlined"
    SPORTS_GOLF = "sports_golf"
    SPORTS_GOLF_SHARP = "sports_golf_sharp"
    SPORTS_GOLF_ROUNDED = "sports_golf_rounded"
    SPORTS_GOLF_OUTLINED = "sports_golf_outlined"
    SPORTS_GYMNASTICS = "sports_gymnastics"
    SPORTS_GYMNASTICS_SHARP = "sports_gymnastics_sharp"
    SPORTS_GYMNASTICS_ROUNDED = "sports_gymnastics_rounded"
    SPORTS_GYMNASTICS_OUTLINED = "sports_gymnastics_outlined"
    SPORTS_HANDBALL = "sports_handball"
    SPORTS_HANDBALL_SHARP = "sports_handball_sharp"
    SPORTS_HANDBALL_ROUNDED = "sports_handball_rounded"
    SPORTS_HANDBALL_OUTLINED = "sports_handball_outlined"
    SPORTS_HOCKEY = "sports_hockey"
    SPORTS_HOCKEY_SHARP = "sports_hockey_sharp"
    SPORTS_HOCKEY_ROUNDED = "sports_hockey_rounded"
    SPORTS_HOCKEY_OUTLINED = "sports_hockey_outlined"
    SPORTS_KABADDI = "sports_kabaddi"
    SPORTS_KABADDI_SHARP = "sports_kabaddi_sharp"
    SPORTS_KABADDI_ROUNDED = "sports_kabaddi_rounded"
    SPORTS_KABADDI_OUTLINED = "sports_kabaddi_outlined"
    SPORTS_MARTIAL_ARTS = "sports_martial_arts"
    SPORTS_MARTIAL_ARTS_SHARP = "sports_martial_arts_sharp"
    SPORTS_MARTIAL_ARTS_ROUNDED = "sports_martial_arts_rounded"
    SPORTS_MARTIAL_ARTS_OUTLINED = "sports_martial_arts_outlined"
    SPORTS_MMA = "sports_mma"
    SPORTS_MMA_SHARP = "sports_mma_sharp"
    SPORTS_MMA_ROUNDED = "sports_mma_rounded"
    SPORTS_MMA_OUTLINED = "sports_mma_outlined"
    SPORTS_MOTORSPORTS = "sports_motorsports"
    SPORTS_MOTORSPORTS_SHARP = "sports_motorsports_sharp"
    SPORTS_MOTORSPORTS_ROUNDED = "sports_motorsports_rounded"
    SPORTS_MOTORSPORTS_OUTLINED = "sports_motorsports_outlined"
    SPORTS_RUGBY = "sports_rugby"
    SPORTS_RUGBY_SHARP = "sports_rugby_sharp"
    SPORTS_RUGBY_ROUNDED = "sports_rugby_rounded"
    SPORTS_RUGBY_OUTLINED = "sports_rugby_outlined"
    SPORTS_SCORE = "sports_score"
    SPORTS_SCORE_SHARP = "sports_score_sharp"
    SPORTS_SCORE_ROUNDED = "sports_score_rounded"
    SPORTS_SCORE_OUTLINED = "sports_score_outlined"
    SPORTS_SOCCER = "sports_soccer"
    SPORTS_SOCCER_SHARP = "sports_soccer_sharp"
    SPORTS_SOCCER_ROUNDED = "sports_soccer_rounded"
    SPORTS_SOCCER_OUTLINED = "sports_soccer_outlined"
    SPORTS_TENNIS = "sports_tennis"
    SPORTS_TENNIS_SHARP = "sports_tennis_sharp"
    SPORTS_TENNIS_ROUNDED = "sports_tennis_rounded"
    SPORTS_TENNIS_OUTLINED = "sports_tennis_outlined"
    SPORTS_VOLLEYBALL = "sports_volleyball"
    SPORTS_VOLLEYBALL_SHARP = "sports_volleyball_sharp"
    SPORTS_VOLLEYBALL_ROUNDED = "sports_volleyball_rounded"
    SPORTS_VOLLEYBALL_OUTLINED = "sports_volleyball_outlined"
    SQUARE = "square"
    SQUARE_SHARP = "square_sharp"
    SQUARE_ROUNDED = "square_rounded"
    SQUARE_OUTLINED = "square_outlined"
    SQUARE_FOOT = "square_foot"
    SQUARE_FOOT_SHARP = "square_foot_sharp"
    SQUARE_FOOT_ROUNDED = "square_foot_rounded"
    SQUARE_FOOT_OUTLINED = "square_foot_outlined"
    SSID_CHART = "ssid_chart"
    SSID_CHART_SHARP = "ssid_chart_sharp"
    SSID_CHART_ROUNDED = "ssid_chart_rounded"
    SSID_CHART_OUTLINED = "ssid_chart_outlined"
    STACKED_BAR_CHART = "stacked_bar_chart"
    STACKED_BAR_CHART_SHARP = "stacked_bar_chart_sharp"
    STACKED_BAR_CHART_ROUNDED = "stacked_bar_chart_rounded"
    STACKED_BAR_CHART_OUTLINED = "stacked_bar_chart_outlined"
    STACKED_LINE_CHART = "stacked_line_chart"
    STACKED_LINE_CHART_SHARP = "stacked_line_chart_sharp"
    STACKED_LINE_CHART_ROUNDED = "stacked_line_chart_rounded"
    STACKED_LINE_CHART_OUTLINED = "stacked_line_chart_outlined"
    STADIUM = "stadium"
    STADIUM_SHARP = "stadium_sharp"
    STADIUM_ROUNDED = "stadium_rounded"
    STADIUM_OUTLINED = "stadium_outlined"
    STAIRS = "stairs"
    STAIRS_SHARP = "stairs_sharp"
    STAIRS_ROUNDED = "stairs_rounded"
    STAIRS_OUTLINED = "stairs_outlined"
    STAR = "star"
    STAR_SHARP = "star_sharp"
    STAR_ROUNDED = "star_rounded"
    STAR_OUTLINED = "star_outlined"
    STAR_BORDER = "star_border"
    STAR_BORDER_SHARP = "star_border_sharp"
    STAR_BORDER_ROUNDED = "star_border_rounded"
    STAR_BORDER_OUTLINED = "star_border_outlined"
    STAR_BORDER_PURPLE500 = "star_border_purple500"
    STAR_BORDER_PURPLE500_SHARP = "star_border_purple500_sharp"
    STAR_BORDER_PURPLE500_ROUNDED = "star_border_purple500_rounded"
    STAR_BORDER_PURPLE500_OUTLINED = "star_border_purple500_outlined"
    STAR_HALF = "star_half"
    STAR_HALF_SHARP = "star_half_sharp"
    STAR_HALF_ROUNDED = "star_half_rounded"
    STAR_HALF_OUTLINED = "star_half_outlined"
    STAR_OUTLINE = "star_outline"
    STAR_OUTLINE_SHARP = "star_outline_sharp"
    STAR_OUTLINE_ROUNDED = "star_outline_rounded"
    STAR_OUTLINE_OUTLINED = "star_outline_outlined"
    STAR_PURPLE500 = "star_purple500"
    STAR_PURPLE500_SHARP = "star_purple500_sharp"
    STAR_PURPLE500_ROUNDED = "star_purple500_rounded"
    STAR_PURPLE500_OUTLINED = "star_purple500_outlined"
    STAR_RATE = "star_rate"
    STAR_RATE_SHARP = "star_rate_sharp"
    STAR_RATE_ROUNDED = "star_rate_rounded"
    STAR_RATE_OUTLINED = "star_rate_outlined"
    STARS = "stars"
    STARS_SHARP = "stars_sharp"
    STARS_ROUNDED = "stars_rounded"
    STARS_OUTLINED = "stars_outlined"
    START = "start"
    START_SHARP = "start_sharp"
    START_ROUNDED = "start_rounded"
    START_OUTLINED = "start_outlined"
    STAY_CURRENT_LANDSCAPE = "stay_current_landscape"
    STAY_CURRENT_LANDSCAPE_SHARP = "stay_current_landscape_sharp"
    STAY_CURRENT_LANDSCAPE_ROUNDED = "stay_current_landscape_rounded"
    STAY_CURRENT_LANDSCAPE_OUTLINED = "stay_current_landscape_outlined"
    STAY_CURRENT_PORTRAIT = "stay_current_portrait"
    STAY_CURRENT_PORTRAIT_SHARP = "stay_current_portrait_sharp"
    STAY_CURRENT_PORTRAIT_ROUNDED = "stay_current_portrait_rounded"
    STAY_CURRENT_PORTRAIT_OUTLINED = "stay_current_portrait_outlined"
    STAY_PRIMARY_LANDSCAPE = "stay_primary_landscape"
    STAY_PRIMARY_LANDSCAPE_SHARP = "stay_primary_landscape_sharp"
    STAY_PRIMARY_LANDSCAPE_ROUNDED = "stay_primary_landscape_rounded"
    STAY_PRIMARY_LANDSCAPE_OUTLINED = "stay_primary_landscape_outlined"
    STAY_PRIMARY_PORTRAIT = "stay_primary_portrait"
    STAY_PRIMARY_PORTRAIT_SHARP = "stay_primary_portrait_sharp"
    STAY_PRIMARY_PORTRAIT_ROUNDED = "stay_primary_portrait_rounded"
    STAY_PRIMARY_PORTRAIT_OUTLINED = "stay_primary_portrait_outlined"
    STICKY_NOTE_2 = "sticky_note_2"
    STICKY_NOTE_2_SHARP = "sticky_note_2_sharp"
    STICKY_NOTE_2_ROUNDED = "sticky_note_2_rounded"
    STICKY_NOTE_2_OUTLINED = "sticky_note_2_outlined"
    STOP = "stop"
    STOP_SHARP = "stop_sharp"
    STOP_ROUNDED = "stop_rounded"
    STOP_OUTLINED = "stop_outlined"
    STOP_CIRCLE = "stop_circle"
    STOP_CIRCLE_SHARP = "stop_circle_sharp"
    STOP_CIRCLE_ROUNDED = "stop_circle_rounded"
    STOP_CIRCLE_OUTLINED = "stop_circle_outlined"
    STOP_SCREEN_SHARE = "stop_screen_share"
    STOP_SCREEN_SHARE_SHARP = "stop_screen_share_sharp"
    STOP_SCREEN_SHARE_ROUNDED = "stop_screen_share_rounded"
    STOP_SCREEN_SHARE_OUTLINED = "stop_screen_share_outlined"
    STORAGE = "storage"
    STORAGE_SHARP = "storage_sharp"
    STORAGE_ROUNDED = "storage_rounded"
    STORAGE_OUTLINED = "storage_outlined"
    STORE = "store"
    STORE_SHARP = "store_sharp"
    STORE_ROUNDED = "store_rounded"
    STORE_OUTLINED = "store_outlined"
    STORE_MALL_DIRECTORY = "store_mall_directory"
    STORE_MALL_DIRECTORY_SHARP = "store_mall_directory_sharp"
    STORE_MALL_DIRECTORY_ROUNDED = "store_mall_directory_rounded"
    STORE_MALL_DIRECTORY_OUTLINED = "store_mall_directory_outlined"
    STOREFRONT = "storefront"
    STOREFRONT_SHARP = "storefront_sharp"
    STOREFRONT_ROUNDED = "storefront_rounded"
    STOREFRONT_OUTLINED = "storefront_outlined"
    STORM = "storm"
    STORM_SHARP = "storm_sharp"
    STORM_ROUNDED = "storm_rounded"
    STORM_OUTLINED = "storm_outlined"
    STRAIGHT = "straight"
    STRAIGHT_SHARP = "straight_sharp"
    STRAIGHT_ROUNDED = "straight_rounded"
    STRAIGHT_OUTLINED = "straight_outlined"
    STRAIGHTEN = "straighten"
    STRAIGHTEN_SHARP = "straighten_sharp"
    STRAIGHTEN_ROUNDED = "straighten_rounded"
    STRAIGHTEN_OUTLINED = "straighten_outlined"
    STREAM = "stream"
    STREAM_SHARP = "stream_sharp"
    STREAM_ROUNDED = "stream_rounded"
    STREAM_OUTLINED = "stream_outlined"
    STREETVIEW = "streetview"
    STREETVIEW_SHARP = "streetview_sharp"
    STREETVIEW_ROUNDED = "streetview_rounded"
    STREETVIEW_OUTLINED = "streetview_outlined"
    STRIKETHROUGH_S = "strikethrough_s"
    STRIKETHROUGH_S_SHARP = "strikethrough_s_sharp"
    STRIKETHROUGH_S_ROUNDED = "strikethrough_s_rounded"
    STRIKETHROUGH_S_OUTLINED = "strikethrough_s_outlined"
    STROLLER = "stroller"
    STROLLER_SHARP = "stroller_sharp"
    STROLLER_ROUNDED = "stroller_rounded"
    STROLLER_OUTLINED = "stroller_outlined"
    STYLE = "style"
    STYLE_SHARP = "style_sharp"
    STYLE_ROUNDED = "style_rounded"
    STYLE_OUTLINED = "style_outlined"
    SUBDIRECTORY_ARROW_LEFT = "subdirectory_arrow_left"
    SUBDIRECTORY_ARROW_LEFT_SHARP = "subdirectory_arrow_left_sharp"
    SUBDIRECTORY_ARROW_LEFT_ROUNDED = "subdirectory_arrow_left_rounded"
    SUBDIRECTORY_ARROW_LEFT_OUTLINED = "subdirectory_arrow_left_outlined"
    SUBDIRECTORY_ARROW_RIGHT = "subdirectory_arrow_right"
    SUBDIRECTORY_ARROW_RIGHT_SHARP = "subdirectory_arrow_right_sharp"
    SUBDIRECTORY_ARROW_RIGHT_ROUNDED = "subdirectory_arrow_right_rounded"
    SUBDIRECTORY_ARROW_RIGHT_OUTLINED = "subdirectory_arrow_right_outlined"
    SUBJECT = "subject"
    SUBJECT_SHARP = "subject_sharp"
    SUBJECT_ROUNDED = "subject_rounded"
    SUBJECT_OUTLINED = "subject_outlined"
    SUBSCRIPT = "subscript"
    SUBSCRIPT_SHARP = "subscript_sharp"
    SUBSCRIPT_ROUNDED = "subscript_rounded"
    SUBSCRIPT_OUTLINED = "subscript_outlined"
    SUBSCRIPTIONS = "subscriptions"
    SUBSCRIPTIONS_SHARP = "subscriptions_sharp"
    SUBSCRIPTIONS_ROUNDED = "subscriptions_rounded"
    SUBSCRIPTIONS_OUTLINED = "subscriptions_outlined"
    SUBTITLES = "subtitles"
    SUBTITLES_SHARP = "subtitles_sharp"
    SUBTITLES_ROUNDED = "subtitles_rounded"
    SUBTITLES_OUTLINED = "subtitles_outlined"
    SUBTITLES_OFF = "subtitles_off"
    SUBTITLES_OFF_SHARP = "subtitles_off_sharp"
    SUBTITLES_OFF_ROUNDED = "subtitles_off_rounded"
    SUBTITLES_OFF_OUTLINED = "subtitles_off_outlined"
    SUBWAY = "subway"
    SUBWAY_SHARP = "subway_sharp"
    SUBWAY_ROUNDED = "subway_rounded"
    SUBWAY_OUTLINED = "subway_outlined"
    SUMMARIZE = "summarize"
    SUMMARIZE_SHARP = "summarize_sharp"
    SUMMARIZE_ROUNDED = "summarize_rounded"
    SUMMARIZE_OUTLINED = "summarize_outlined"
    SUNNY = "sunny"
    SUNNY_SNOWING = "sunny_snowing"
    SUPERSCRIPT = "superscript"
    SUPERSCRIPT_SHARP = "superscript_sharp"
    SUPERSCRIPT_ROUNDED = "superscript_rounded"
    SUPERSCRIPT_OUTLINED = "superscript_outlined"
    SUPERVISED_USER_CIRCLE = "supervised_user_circle"
    SUPERVISED_USER_CIRCLE_SHARP = "supervised_user_circle_sharp"
    SUPERVISED_USER_CIRCLE_ROUNDED = "supervised_user_circle_rounded"
    SUPERVISED_USER_CIRCLE_OUTLINED = "supervised_user_circle_outlined"
    SUPERVISOR_ACCOUNT = "supervisor_account"
    SUPERVISOR_ACCOUNT_SHARP = "supervisor_account_sharp"
    SUPERVISOR_ACCOUNT_ROUNDED = "supervisor_account_rounded"
    SUPERVISOR_ACCOUNT_OUTLINED = "supervisor_account_outlined"
    SUPPORT = "support"
    SUPPORT_SHARP = "support_sharp"
    SUPPORT_ROUNDED = "support_rounded"
    SUPPORT_OUTLINED = "support_outlined"
    SUPPORT_AGENT = "support_agent"
    SUPPORT_AGENT_SHARP = "support_agent_sharp"
    SUPPORT_AGENT_ROUNDED = "support_agent_rounded"
    SUPPORT_AGENT_OUTLINED = "support_agent_outlined"
    SURFING = "surfing"
    SURFING_SHARP = "surfing_sharp"
    SURFING_ROUNDED = "surfing_rounded"
    SURFING_OUTLINED = "surfing_outlined"
    SURROUND_SOUND = "surround_sound"
    SURROUND_SOUND_SHARP = "surround_sound_sharp"
    SURROUND_SOUND_ROUNDED = "surround_sound_rounded"
    SURROUND_SOUND_OUTLINED = "surround_sound_outlined"
    SWAP_CALLS = "swap_calls"
    SWAP_CALLS_SHARP = "swap_calls_sharp"
    SWAP_CALLS_ROUNDED = "swap_calls_rounded"
    SWAP_CALLS_OUTLINED = "swap_calls_outlined"
    SWAP_HORIZ = "swap_horiz"
    SWAP_HORIZ_SHARP = "swap_horiz_sharp"
    SWAP_HORIZ_ROUNDED = "swap_horiz_rounded"
    SWAP_HORIZ_OUTLINED = "swap_horiz_outlined"
    SWAP_HORIZONTAL_CIRCLE = "swap_horizontal_circle"
    SWAP_HORIZONTAL_CIRCLE_SHARP = "swap_horizontal_circle_sharp"
    SWAP_HORIZONTAL_CIRCLE_ROUNDED = "swap_horizontal_circle_rounded"
    SWAP_HORIZONTAL_CIRCLE_OUTLINED = "swap_horizontal_circle_outlined"
    SWAP_VERT = "swap_vert"
    SWAP_VERT_SHARP = "swap_vert_sharp"
    SWAP_VERT_ROUNDED = "swap_vert_rounded"
    SWAP_VERT_OUTLINED = "swap_vert_outlined"
    SWAP_VERT_CIRCLE = "swap_vert_circle"
    SWAP_VERT_CIRCLE_SHARP = "swap_vert_circle_sharp"
    SWAP_VERT_CIRCLE_ROUNDED = "swap_vert_circle_rounded"
    SWAP_VERT_CIRCLE_OUTLINED = "swap_vert_circle_outlined"
    SWAP_VERTICAL_CIRCLE = "swap_vertical_circle"
    SWAP_VERTICAL_CIRCLE_SHARP = "swap_vertical_circle_sharp"
    SWAP_VERTICAL_CIRCLE_ROUNDED = "swap_vertical_circle_rounded"
    SWAP_VERTICAL_CIRCLE_OUTLINED = "swap_vertical_circle_outlined"
    SWIPE = "swipe"
    SWIPE_SHARP = "swipe_sharp"
    SWIPE_ROUNDED = "swipe_rounded"
    SWIPE_OUTLINED = "swipe_outlined"
    SWIPE_DOWN = "swipe_down"
    SWIPE_DOWN_SHARP = "swipe_down_sharp"
    SWIPE_DOWN_ROUNDED = "swipe_down_rounded"
    SWIPE_DOWN_OUTLINED = "swipe_down_outlined"
    SWIPE_DOWN_ALT = "swipe_down_alt"
    SWIPE_DOWN_ALT_SHARP = "swipe_down_alt_sharp"
    SWIPE_DOWN_ALT_ROUNDED = "swipe_down_alt_rounded"
    SWIPE_DOWN_ALT_OUTLINED = "swipe_down_alt_outlined"
    SWIPE_LEFT = "swipe_left"
    SWIPE_LEFT_SHARP = "swipe_left_sharp"
    SWIPE_LEFT_ROUNDED = "swipe_left_rounded"
    SWIPE_LEFT_OUTLINED = "swipe_left_outlined"
    SWIPE_LEFT_ALT = "swipe_left_alt"
    SWIPE_LEFT_ALT_SHARP = "swipe_left_alt_sharp"
    SWIPE_LEFT_ALT_ROUNDED = "swipe_left_alt_rounded"
    SWIPE_LEFT_ALT_OUTLINED = "swipe_left_alt_outlined"
    SWIPE_RIGHT = "swipe_right"
    SWIPE_RIGHT_SHARP = "swipe_right_sharp"
    SWIPE_RIGHT_ROUNDED = "swipe_right_rounded"
    SWIPE_RIGHT_OUTLINED = "swipe_right_outlined"
    SWIPE_RIGHT_ALT = "swipe_right_alt"
    SWIPE_RIGHT_ALT_SHARP = "swipe_right_alt_sharp"
    SWIPE_RIGHT_ALT_ROUNDED = "swipe_right_alt_rounded"
    SWIPE_RIGHT_ALT_OUTLINED = "swipe_right_alt_outlined"
    SWIPE_UP = "swipe_up"
    SWIPE_UP_SHARP = "swipe_up_sharp"
    SWIPE_UP_ROUNDED = "swipe_up_rounded"
    SWIPE_UP_OUTLINED = "swipe_up_outlined"
    SWIPE_UP_ALT = "swipe_up_alt"
    SWIPE_UP_ALT_SHARP = "swipe_up_alt_sharp"
    SWIPE_UP_ALT_ROUNDED = "swipe_up_alt_rounded"
    SWIPE_UP_ALT_OUTLINED = "swipe_up_alt_outlined"
    SWIPE_VERTICAL = "swipe_vertical"
    SWIPE_VERTICAL_SHARP = "swipe_vertical_sharp"
    SWIPE_VERTICAL_ROUNDED = "swipe_vertical_rounded"
    SWIPE_VERTICAL_OUTLINED = "swipe_vertical_outlined"
    SWITCH_ACCESS_SHORTCUT = "switch_access_shortcut"
    SWITCH_ACCESS_SHORTCUT_SHARP = "switch_access_shortcut_sharp"
    SWITCH_ACCESS_SHORTCUT_ROUNDED = "switch_access_shortcut_rounded"
    SWITCH_ACCESS_SHORTCUT_OUTLINED = "switch_access_shortcut_outlined"
    SWITCH_ACCESS_SHORTCUT_ADD = "switch_access_shortcut_add"
    SWITCH_ACCESS_SHORTCUT_ADD_SHARP = "switch_access_shortcut_add_sharp"
    SWITCH_ACCESS_SHORTCUT_ADD_ROUNDED = "switch_access_shortcut_add_rounded"
    SWITCH_ACCESS_SHORTCUT_ADD_OUTLINED = "switch_access_shortcut_add_outlined"
    SWITCH_ACCOUNT = "switch_account"
    SWITCH_ACCOUNT_SHARP = "switch_account_sharp"
    SWITCH_ACCOUNT_ROUNDED = "switch_account_rounded"
    SWITCH_ACCOUNT_OUTLINED = "switch_account_outlined"
    SWITCH_CAMERA = "switch_camera"
    SWITCH_CAMERA_SHARP = "switch_camera_sharp"
    SWITCH_CAMERA_ROUNDED = "switch_camera_rounded"
    SWITCH_CAMERA_OUTLINED = "switch_camera_outlined"
    SWITCH_LEFT = "switch_left"
    SWITCH_LEFT_SHARP = "switch_left_sharp"
    SWITCH_LEFT_ROUNDED = "switch_left_rounded"
    SWITCH_LEFT_OUTLINED = "switch_left_outlined"
    SWITCH_RIGHT = "switch_right"
    SWITCH_RIGHT_SHARP = "switch_right_sharp"
    SWITCH_RIGHT_ROUNDED = "switch_right_rounded"
    SWITCH_RIGHT_OUTLINED = "switch_right_outlined"
    SWITCH_VIDEO = "switch_video"
    SWITCH_VIDEO_SHARP = "switch_video_sharp"
    SWITCH_VIDEO_ROUNDED = "switch_video_rounded"
    SWITCH_VIDEO_OUTLINED = "switch_video_outlined"
    SYNAGOGUE = "synagogue"
    SYNAGOGUE_SHARP = "synagogue_sharp"
    SYNAGOGUE_ROUNDED = "synagogue_rounded"
    SYNAGOGUE_OUTLINED = "synagogue_outlined"
    SYNC = "sync"
    SYNC_SHARP = "sync_sharp"
    SYNC_ROUNDED = "sync_rounded"
    SYNC_OUTLINED = "sync_outlined"
    SYNC_ALT = "sync_alt"
    SYNC_ALT_SHARP = "sync_alt_sharp"
    SYNC_ALT_ROUNDED = "sync_alt_rounded"
    SYNC_ALT_OUTLINED = "sync_alt_outlined"
    SYNC_DISABLED = "sync_disabled"
    SYNC_DISABLED_SHARP = "sync_disabled_sharp"
    SYNC_DISABLED_ROUNDED = "sync_disabled_rounded"
    SYNC_DISABLED_OUTLINED = "sync_disabled_outlined"
    SYNC_LOCK = "sync_lock"
    SYNC_LOCK_SHARP = "sync_lock_sharp"
    SYNC_LOCK_ROUNDED = "sync_lock_rounded"
    SYNC_LOCK_OUTLINED = "sync_lock_outlined"
    SYNC_PROBLEM = "sync_problem"
    SYNC_PROBLEM_SHARP = "sync_problem_sharp"
    SYNC_PROBLEM_ROUNDED = "sync_problem_rounded"
    SYNC_PROBLEM_OUTLINED = "sync_problem_outlined"
    SYSTEM_SECURITY_UPDATE = "system_security_update"
    SYSTEM_SECURITY_UPDATE_SHARP = "system_security_update_sharp"
    SYSTEM_SECURITY_UPDATE_ROUNDED = "system_security_update_rounded"
    SYSTEM_SECURITY_UPDATE_OUTLINED = "system_security_update_outlined"
    SYSTEM_SECURITY_UPDATE_GOOD = "system_security_update_good"
    SYSTEM_SECURITY_UPDATE_GOOD_SHARP = "system_security_update_good_sharp"
    SYSTEM_SECURITY_UPDATE_GOOD_ROUNDED = "system_security_update_good_rounded"
    SYSTEM_SECURITY_UPDATE_GOOD_OUTLINED = "system_security_update_good_outlined"
    SYSTEM_SECURITY_UPDATE_WARNING = "system_security_update_warning"
    SYSTEM_SECURITY_UPDATE_WARNING_SHARP = "system_security_update_warning_sharp"
    SYSTEM_SECURITY_UPDATE_WARNING_ROUNDED = "system_security_update_warning_rounded"
    SYSTEM_SECURITY_UPDATE_WARNING_OUTLINED = "system_security_update_warning_outlined"
    SYSTEM_UPDATE = "system_update"
    SYSTEM_UPDATE_SHARP = "system_update_sharp"
    SYSTEM_UPDATE_ROUNDED = "system_update_rounded"
    SYSTEM_UPDATE_OUTLINED = "system_update_outlined"
    SYSTEM_UPDATE_ALT = "system_update_alt"
    SYSTEM_UPDATE_ALT_SHARP = "system_update_alt_sharp"
    SYSTEM_UPDATE_ALT_ROUNDED = "system_update_alt_rounded"
    SYSTEM_UPDATE_ALT_OUTLINED = "system_update_alt_outlined"
    SYSTEM_UPDATE_TV = "system_update_tv"
    SYSTEM_UPDATE_TV_SHARP = "system_update_tv_sharp"
    SYSTEM_UPDATE_TV_ROUNDED = "system_update_tv_rounded"
    SYSTEM_UPDATE_TV_OUTLINED = "system_update_tv_outlined"
    TAB = "tab"
    TAB_SHARP = "tab_sharp"
    TAB_ROUNDED = "tab_rounded"
    TAB_OUTLINED = "tab_outlined"
    TAB_UNSELECTED = "tab_unselected"
    TAB_UNSELECTED_SHARP = "tab_unselected_sharp"
    TAB_UNSELECTED_ROUNDED = "tab_unselected_rounded"
    TAB_UNSELECTED_OUTLINED = "tab_unselected_outlined"
    TABLE_BAR = "table_bar"
    TABLE_BAR_SHARP = "table_bar_sharp"
    TABLE_BAR_ROUNDED = "table_bar_rounded"
    TABLE_BAR_OUTLINED = "table_bar_outlined"
    TABLE_CHART = "table_chart"
    TABLE_CHART_SHARP = "table_chart_sharp"
    TABLE_CHART_ROUNDED = "table_chart_rounded"
    TABLE_CHART_OUTLINED = "table_chart_outlined"
    TABLE_RESTAURANT = "table_restaurant"
    TABLE_RESTAURANT_SHARP = "table_restaurant_sharp"
    TABLE_RESTAURANT_ROUNDED = "table_restaurant_rounded"
    TABLE_RESTAURANT_OUTLINED = "table_restaurant_outlined"
    TABLE_ROWS = "table_rows"
    TABLE_ROWS_SHARP = "table_rows_sharp"
    TABLE_ROWS_ROUNDED = "table_rows_rounded"
    TABLE_ROWS_OUTLINED = "table_rows_outlined"
    TABLE_VIEW = "table_view"
    TABLE_VIEW_SHARP = "table_view_sharp"
    TABLE_VIEW_ROUNDED = "table_view_rounded"
    TABLE_VIEW_OUTLINED = "table_view_outlined"
    TABLET = "tablet"
    TABLET_SHARP = "tablet_sharp"
    TABLET_ROUNDED = "tablet_rounded"
    TABLET_OUTLINED = "tablet_outlined"
    TABLET_ANDROID = "tablet_android"
    TABLET_ANDROID_SHARP = "tablet_android_sharp"
    TABLET_ANDROID_ROUNDED = "tablet_android_rounded"
    TABLET_ANDROID_OUTLINED = "tablet_android_outlined"
    TABLET_MAC = "tablet_mac"
    TABLET_MAC_SHARP = "tablet_mac_sharp"
    TABLET_MAC_ROUNDED = "tablet_mac_rounded"
    TABLET_MAC_OUTLINED = "tablet_mac_outlined"
    TAG = "tag"
    TAG_SHARP = "tag_sharp"
    TAG_ROUNDED = "tag_rounded"
    TAG_OUTLINED = "tag_outlined"
    TAG_FACES = "tag_faces"
    TAG_FACES_SHARP = "tag_faces_sharp"
    TAG_FACES_ROUNDED = "tag_faces_rounded"
    TAG_FACES_OUTLINED = "tag_faces_outlined"
    TAKEOUT_DINING = "takeout_dining"
    TAKEOUT_DINING_SHARP = "takeout_dining_sharp"
    TAKEOUT_DINING_ROUNDED = "takeout_dining_rounded"
    TAKEOUT_DINING_OUTLINED = "takeout_dining_outlined"
    TAP_AND_PLAY = "tap_and_play"
    TAP_AND_PLAY_SHARP = "tap_and_play_sharp"
    TAP_AND_PLAY_ROUNDED = "tap_and_play_rounded"
    TAP_AND_PLAY_OUTLINED = "tap_and_play_outlined"
    TAPAS = "tapas"
    TAPAS_SHARP = "tapas_sharp"
    TAPAS_ROUNDED = "tapas_rounded"
    TAPAS_OUTLINED = "tapas_outlined"
    TASK = "task"
    TASK_SHARP = "task_sharp"
    TASK_ROUNDED = "task_rounded"
    TASK_OUTLINED = "task_outlined"
    TASK_ALT = "task_alt"
    TASK_ALT_SHARP = "task_alt_sharp"
    TASK_ALT_ROUNDED = "task_alt_rounded"
    TASK_ALT_OUTLINED = "task_alt_outlined"
    TAXI_ALERT = "taxi_alert"
    TAXI_ALERT_SHARP = "taxi_alert_sharp"
    TAXI_ALERT_ROUNDED = "taxi_alert_rounded"
    TAXI_ALERT_OUTLINED = "taxi_alert_outlined"
    TELEGRAM = "telegram"
    TELEGRAM_SHARP = "telegram_sharp"
    TELEGRAM_ROUNDED = "telegram_rounded"
    TELEGRAM_OUTLINED = "telegram_outlined"
    TEMPLE_BUDDHIST = "temple_buddhist"
    TEMPLE_BUDDHIST_SHARP = "temple_buddhist_sharp"
    TEMPLE_BUDDHIST_ROUNDED = "temple_buddhist_rounded"
    TEMPLE_BUDDHIST_OUTLINED = "temple_buddhist_outlined"
    TEMPLE_HINDU = "temple_hindu"
    TEMPLE_HINDU_SHARP = "temple_hindu_sharp"
    TEMPLE_HINDU_ROUNDED = "temple_hindu_rounded"
    TEMPLE_HINDU_OUTLINED = "temple_hindu_outlined"
    TERMINAL = "terminal"
    TERMINAL_SHARP = "terminal_sharp"
    TERMINAL_ROUNDED = "terminal_rounded"
    TERMINAL_OUTLINED = "terminal_outlined"
    TERRAIN = "terrain"
    TERRAIN_SHARP = "terrain_sharp"
    TERRAIN_ROUNDED = "terrain_rounded"
    TERRAIN_OUTLINED = "terrain_outlined"
    TEXT_DECREASE = "text_decrease"
    TEXT_DECREASE_SHARP = "text_decrease_sharp"
    TEXT_DECREASE_ROUNDED = "text_decrease_rounded"
    TEXT_DECREASE_OUTLINED = "text_decrease_outlined"
    TEXT_FIELDS = "text_fields"
    TEXT_FIELDS_SHARP = "text_fields_sharp"
    TEXT_FIELDS_ROUNDED = "text_fields_rounded"
    TEXT_FIELDS_OUTLINED = "text_fields_outlined"
    TEXT_FORMAT = "text_format"
    TEXT_FORMAT_SHARP = "text_format_sharp"
    TEXT_FORMAT_ROUNDED = "text_format_rounded"
    TEXT_FORMAT_OUTLINED = "text_format_outlined"
    TEXT_INCREASE = "text_increase"
    TEXT_INCREASE_SHARP = "text_increase_sharp"
    TEXT_INCREASE_ROUNDED = "text_increase_rounded"
    TEXT_INCREASE_OUTLINED = "text_increase_outlined"
    TEXT_ROTATE_UP = "text_rotate_up"
    TEXT_ROTATE_UP_SHARP = "text_rotate_up_sharp"
    TEXT_ROTATE_UP_ROUNDED = "text_rotate_up_rounded"
    TEXT_ROTATE_UP_OUTLINED = "text_rotate_up_outlined"
    TEXT_ROTATE_VERTICAL = "text_rotate_vertical"
    TEXT_ROTATE_VERTICAL_SHARP = "text_rotate_vertical_sharp"
    TEXT_ROTATE_VERTICAL_ROUNDED = "text_rotate_vertical_rounded"
    TEXT_ROTATE_VERTICAL_OUTLINED = "text_rotate_vertical_outlined"
    TEXT_ROTATION_ANGLEDOWN = "text_rotation_angledown"
    TEXT_ROTATION_ANGLEDOWN_SHARP = "text_rotation_angledown_sharp"
    TEXT_ROTATION_ANGLEDOWN_ROUNDED = "text_rotation_angledown_rounded"
    TEXT_ROTATION_ANGLEDOWN_OUTLINED = "text_rotation_angledown_outlined"
    TEXT_ROTATION_ANGLEUP = "text_rotation_angleup"
    TEXT_ROTATION_ANGLEUP_SHARP = "text_rotation_angleup_sharp"
    TEXT_ROTATION_ANGLEUP_ROUNDED = "text_rotation_angleup_rounded"
    TEXT_ROTATION_ANGLEUP_OUTLINED = "text_rotation_angleup_outlined"
    TEXT_ROTATION_DOWN = "text_rotation_down"
    TEXT_ROTATION_DOWN_SHARP = "text_rotation_down_sharp"
    TEXT_ROTATION_DOWN_ROUNDED = "text_rotation_down_rounded"
    TEXT_ROTATION_DOWN_OUTLINED = "text_rotation_down_outlined"
    TEXT_ROTATION_NONE = "text_rotation_none"
    TEXT_ROTATION_NONE_SHARP = "text_rotation_none_sharp"
    TEXT_ROTATION_NONE_ROUNDED = "text_rotation_none_rounded"
    TEXT_ROTATION_NONE_OUTLINED = "text_rotation_none_outlined"
    TEXT_SNIPPET = "text_snippet"
    TEXT_SNIPPET_SHARP = "text_snippet_sharp"
    TEXT_SNIPPET_ROUNDED = "text_snippet_rounded"
    TEXT_SNIPPET_OUTLINED = "text_snippet_outlined"
    TEXTSMS = "textsms"
    TEXTSMS_SHARP = "textsms_sharp"
    TEXTSMS_ROUNDED = "textsms_rounded"
    TEXTSMS_OUTLINED = "textsms_outlined"
    TEXTURE = "texture"
    TEXTURE_SHARP = "texture_sharp"
    TEXTURE_ROUNDED = "texture_rounded"
    TEXTURE_OUTLINED = "texture_outlined"
    THEATER_COMEDY = "theater_comedy"
    THEATER_COMEDY_SHARP = "theater_comedy_sharp"
    THEATER_COMEDY_ROUNDED = "theater_comedy_rounded"
    THEATER_COMEDY_OUTLINED = "theater_comedy_outlined"
    THEATERS = "theaters"
    THEATERS_SHARP = "theaters_sharp"
    THEATERS_ROUNDED = "theaters_rounded"
    THEATERS_OUTLINED = "theaters_outlined"
    THERMOSTAT = "thermostat"
    THERMOSTAT_SHARP = "thermostat_sharp"
    THERMOSTAT_ROUNDED = "thermostat_rounded"
    THERMOSTAT_OUTLINED = "thermostat_outlined"
    THERMOSTAT_AUTO = "thermostat_auto"
    THERMOSTAT_AUTO_SHARP = "thermostat_auto_sharp"
    THERMOSTAT_AUTO_ROUNDED = "thermostat_auto_rounded"
    THERMOSTAT_AUTO_OUTLINED = "thermostat_auto_outlined"
    THUMB_DOWN = "thumb_down"
    THUMB_DOWN_SHARP = "thumb_down_sharp"
    THUMB_DOWN_ROUNDED = "thumb_down_rounded"
    THUMB_DOWN_OUTLINED = "thumb_down_outlined"
    THUMB_DOWN_ALT = "thumb_down_alt"
    THUMB_DOWN_ALT_SHARP = "thumb_down_alt_sharp"
    THUMB_DOWN_ALT_ROUNDED = "thumb_down_alt_rounded"
    THUMB_DOWN_ALT_OUTLINED = "thumb_down_alt_outlined"
    THUMB_DOWN_OFF_ALT = "thumb_down_off_alt"
    THUMB_DOWN_OFF_ALT_SHARP = "thumb_down_off_alt_sharp"
    THUMB_DOWN_OFF_ALT_ROUNDED = "thumb_down_off_alt_rounded"
    THUMB_DOWN_OFF_ALT_OUTLINED = "thumb_down_off_alt_outlined"
    THUMB_UP = "thumb_up"
    THUMB_UP_SHARP = "thumb_up_sharp"
    THUMB_UP_ROUNDED = "thumb_up_rounded"
    THUMB_UP_OUTLINED = "thumb_up_outlined"
    THUMB_UP_ALT = "thumb_up_alt"
    THUMB_UP_ALT_SHARP = "thumb_up_alt_sharp"
    THUMB_UP_ALT_ROUNDED = "thumb_up_alt_rounded"
    THUMB_UP_ALT_OUTLINED = "thumb_up_alt_outlined"
    THUMB_UP_OFF_ALT = "thumb_up_off_alt"
    THUMB_UP_OFF_ALT_SHARP = "thumb_up_off_alt_sharp"
    THUMB_UP_OFF_ALT_ROUNDED = "thumb_up_off_alt_rounded"
    THUMB_UP_OFF_ALT_OUTLINED = "thumb_up_off_alt_outlined"
    THUMBS_UP_DOWN = "thumbs_up_down"
    THUMBS_UP_DOWN_SHARP = "thumbs_up_down_sharp"
    THUMBS_UP_DOWN_ROUNDED = "thumbs_up_down_rounded"
    THUMBS_UP_DOWN_OUTLINED = "thumbs_up_down_outlined"
    THUNDERSTORM = "thunderstorm"
    THUNDERSTORM_SHARP = "thunderstorm_sharp"
    THUNDERSTORM_ROUNDED = "thunderstorm_rounded"
    THUNDERSTORM_OUTLINED = "thunderstorm_outlined"
    TIKTOK = "tiktok"
    TIKTOK_SHARP = "tiktok_sharp"
    TIKTOK_ROUNDED = "tiktok_rounded"
    TIKTOK_OUTLINED = "tiktok_outlined"
    TIME_TO_LEAVE = "time_to_leave"
    TIME_TO_LEAVE_SHARP = "time_to_leave_sharp"
    TIME_TO_LEAVE_ROUNDED = "time_to_leave_rounded"
    TIME_TO_LEAVE_OUTLINED = "time_to_leave_outlined"
    TIMELAPSE = "timelapse"
    TIMELAPSE_SHARP = "timelapse_sharp"
    TIMELAPSE_ROUNDED = "timelapse_rounded"
    TIMELAPSE_OUTLINED = "timelapse_outlined"
    TIMELINE = "timeline"
    TIMELINE_SHARP = "timeline_sharp"
    TIMELINE_ROUNDED = "timeline_rounded"
    TIMELINE_OUTLINED = "timeline_outlined"
    TIMER = "timer"
    TIMER_SHARP = "timer_sharp"
    TIMER_ROUNDED = "timer_rounded"
    TIMER_OUTLINED = "timer_outlined"
    TIMER_10 = "timer_10"
    TIMER_10_SHARP = "timer_10_sharp"
    TIMER_10_ROUNDED = "timer_10_rounded"
    TIMER_10_OUTLINED = "timer_10_outlined"
    TIMER_10_SELECT = "timer_10_select"
    TIMER_10_SELECT_SHARP = "timer_10_select_sharp"
    TIMER_10_SELECT_ROUNDED = "timer_10_select_rounded"
    TIMER_10_SELECT_OUTLINED = "timer_10_select_outlined"
    TIMER_3 = "timer_3"
    TIMER_3_SHARP = "timer_3_sharp"
    TIMER_3_ROUNDED = "timer_3_rounded"
    TIMER_3_OUTLINED = "timer_3_outlined"
    TIMER_3_SELECT = "timer_3_select"
    TIMER_3_SELECT_SHARP = "timer_3_select_sharp"
    TIMER_3_SELECT_ROUNDED = "timer_3_select_rounded"
    TIMER_3_SELECT_OUTLINED = "timer_3_select_outlined"
    TIMER_OFF = "timer_off"
    TIMER_OFF_SHARP = "timer_off_sharp"
    TIMER_OFF_ROUNDED = "timer_off_rounded"
    TIMER_OFF_OUTLINED = "timer_off_outlined"
    TIPS_AND_UPDATES = "tips_and_updates"
    TIPS_AND_UPDATES_SHARP = "tips_and_updates_sharp"
    TIPS_AND_UPDATES_ROUNDED = "tips_and_updates_rounded"
    TIPS_AND_UPDATES_OUTLINED = "tips_and_updates_outlined"
    TIRE_REPAIR = "tire_repair"
    TIRE_REPAIR_SHARP = "tire_repair_sharp"
    TIRE_REPAIR_ROUNDED = "tire_repair_rounded"
    TIRE_REPAIR_OUTLINED = "tire_repair_outlined"
    TITLE = "title"
    TITLE_SHARP = "title_sharp"
    TITLE_ROUNDED = "title_rounded"
    TITLE_OUTLINED = "title_outlined"
    TOC = "toc"
    TOC_SHARP = "toc_sharp"
    TOC_ROUNDED = "toc_rounded"
    TOC_OUTLINED = "toc_outlined"
    TODAY = "today"
    TODAY_SHARP = "today_sharp"
    TODAY_ROUNDED = "today_rounded"
    TODAY_OUTLINED = "today_outlined"
    TOGGLE_OFF = "toggle_off"
    TOGGLE_OFF_SHARP = "toggle_off_sharp"
    TOGGLE_OFF_ROUNDED = "toggle_off_rounded"
    TOGGLE_OFF_OUTLINED = "toggle_off_outlined"
    TOGGLE_ON = "toggle_on"
    TOGGLE_ON_SHARP = "toggle_on_sharp"
    TOGGLE_ON_ROUNDED = "toggle_on_rounded"
    TOGGLE_ON_OUTLINED = "toggle_on_outlined"
    TOKEN = "token"
    TOKEN_SHARP = "token_sharp"
    TOKEN_ROUNDED = "token_rounded"
    TOKEN_OUTLINED = "token_outlined"
    TOLL = "toll"
    TOLL_SHARP = "toll_sharp"
    TOLL_ROUNDED = "toll_rounded"
    TOLL_OUTLINED = "toll_outlined"
    TONALITY = "tonality"
    TONALITY_SHARP = "tonality_sharp"
    TONALITY_ROUNDED = "tonality_rounded"
    TONALITY_OUTLINED = "tonality_outlined"
    TOPIC = "topic"
    TOPIC_SHARP = "topic_sharp"
    TOPIC_ROUNDED = "topic_rounded"
    TOPIC_OUTLINED = "topic_outlined"
    TORNADO = "tornado"
    TORNADO_SHARP = "tornado_sharp"
    TORNADO_ROUNDED = "tornado_rounded"
    TORNADO_OUTLINED = "tornado_outlined"
    TOUCH_APP = "touch_app"
    TOUCH_APP_SHARP = "touch_app_sharp"
    TOUCH_APP_ROUNDED = "touch_app_rounded"
    TOUCH_APP_OUTLINED = "touch_app_outlined"
    TOUR = "tour"
    TOUR_SHARP = "tour_sharp"
    TOUR_ROUNDED = "tour_rounded"
    TOUR_OUTLINED = "tour_outlined"
    TOYS = "toys"
    TOYS_SHARP = "toys_sharp"
    TOYS_ROUNDED = "toys_rounded"
    TOYS_OUTLINED = "toys_outlined"
    TRACK_CHANGES = "track_changes"
    TRACK_CHANGES_SHARP = "track_changes_sharp"
    TRACK_CHANGES_ROUNDED = "track_changes_rounded"
    TRACK_CHANGES_OUTLINED = "track_changes_outlined"
    TRAFFIC = "traffic"
    TRAFFIC_SHARP = "traffic_sharp"
    TRAFFIC_ROUNDED = "traffic_rounded"
    TRAFFIC_OUTLINED = "traffic_outlined"
    TRAIN = "train"
    TRAIN_SHARP = "train_sharp"
    TRAIN_ROUNDED = "train_rounded"
    TRAIN_OUTLINED = "train_outlined"
    TRAM = "tram"
    TRAM_SHARP = "tram_sharp"
    TRAM_ROUNDED = "tram_rounded"
    TRAM_OUTLINED = "tram_outlined"
    TRANSCRIBE = "transcribe"
    TRANSCRIBE_SHARP = "transcribe_sharp"
    TRANSCRIBE_ROUNDED = "transcribe_rounded"
    TRANSCRIBE_OUTLINED = "transcribe_outlined"
    TRANSFER_WITHIN_A_STATION = "transfer_within_a_station"
    TRANSFER_WITHIN_A_STATION_SHARP = "transfer_within_a_station_sharp"
    TRANSFER_WITHIN_A_STATION_ROUNDED = "transfer_within_a_station_rounded"
    TRANSFER_WITHIN_A_STATION_OUTLINED = "transfer_within_a_station_outlined"
    TRANSFORM = "transform"
    TRANSFORM_SHARP = "transform_sharp"
    TRANSFORM_ROUNDED = "transform_rounded"
    TRANSFORM_OUTLINED = "transform_outlined"
    TRANSGENDER = "transgender"
    TRANSGENDER_SHARP = "transgender_sharp"
    TRANSGENDER_ROUNDED = "transgender_rounded"
    TRANSGENDER_OUTLINED = "transgender_outlined"
    TRANSIT_ENTEREXIT = "transit_enterexit"
    TRANSIT_ENTEREXIT_SHARP = "transit_enterexit_sharp"
    TRANSIT_ENTEREXIT_ROUNDED = "transit_enterexit_rounded"
    TRANSIT_ENTEREXIT_OUTLINED = "transit_enterexit_outlined"
    TRANSLATE = "translate"
    TRANSLATE_SHARP = "translate_sharp"
    TRANSLATE_ROUNDED = "translate_rounded"
    TRANSLATE_OUTLINED = "translate_outlined"
    TRAVEL_EXPLORE = "travel_explore"
    TRAVEL_EXPLORE_SHARP = "travel_explore_sharp"
    TRAVEL_EXPLORE_ROUNDED = "travel_explore_rounded"
    TRAVEL_EXPLORE_OUTLINED = "travel_explore_outlined"
    TRENDING_DOWN = "trending_down"
    TRENDING_DOWN_SHARP = "trending_down_sharp"
    TRENDING_DOWN_ROUNDED = "trending_down_rounded"
    TRENDING_DOWN_OUTLINED = "trending_down_outlined"
    TRENDING_FLAT = "trending_flat"
    TRENDING_FLAT_SHARP = "trending_flat_sharp"
    TRENDING_FLAT_ROUNDED = "trending_flat_rounded"
    TRENDING_FLAT_OUTLINED = "trending_flat_outlined"
    TRENDING_NEUTRAL = "trending_neutral"
    TRENDING_NEUTRAL_SHARP = "trending_neutral_sharp"
    TRENDING_NEUTRAL_ROUNDED = "trending_neutral_rounded"
    TRENDING_NEUTRAL_OUTLINED = "trending_neutral_outlined"
    TRENDING_UP = "trending_up"
    TRENDING_UP_SHARP = "trending_up_sharp"
    TRENDING_UP_ROUNDED = "trending_up_rounded"
    TRENDING_UP_OUTLINED = "trending_up_outlined"
    TRIP_ORIGIN = "trip_origin"
    TRIP_ORIGIN_SHARP = "trip_origin_sharp"
    TRIP_ORIGIN_ROUNDED = "trip_origin_rounded"
    TRIP_ORIGIN_OUTLINED = "trip_origin_outlined"
    TROLLEY = "trolley"
    TROUBLESHOOT = "troubleshoot"
    TROUBLESHOOT_SHARP = "troubleshoot_sharp"
    TROUBLESHOOT_ROUNDED = "troubleshoot_rounded"
    TROUBLESHOOT_OUTLINED = "troubleshoot_outlined"
    TRY_SMS_STAR = "try_sms_star"
    TRY_SMS_STAR_SHARP = "try_sms_star_sharp"
    TRY_SMS_STAR_ROUNDED = "try_sms_star_rounded"
    TRY_SMS_STAR_OUTLINED = "try_sms_star_outlined"
    TSUNAMI = "tsunami"
    TSUNAMI_SHARP = "tsunami_sharp"
    TSUNAMI_ROUNDED = "tsunami_rounded"
    TSUNAMI_OUTLINED = "tsunami_outlined"
    TTY = "tty"
    TTY_SHARP = "tty_sharp"
    TTY_ROUNDED = "tty_rounded"
    TTY_OUTLINED = "tty_outlined"
    TUNE = "tune"
    TUNE_SHARP = "tune_sharp"
    TUNE_ROUNDED = "tune_rounded"
    TUNE_OUTLINED = "tune_outlined"
    TUNGSTEN = "tungsten"
    TUNGSTEN_SHARP = "tungsten_sharp"
    TUNGSTEN_ROUNDED = "tungsten_rounded"
    TUNGSTEN_OUTLINED = "tungsten_outlined"
    TURN_LEFT = "turn_left"
    TURN_LEFT_SHARP = "turn_left_sharp"
    TURN_LEFT_ROUNDED = "turn_left_rounded"
    TURN_LEFT_OUTLINED = "turn_left_outlined"
    TURN_RIGHT = "turn_right"
    TURN_RIGHT_SHARP = "turn_right_sharp"
    TURN_RIGHT_ROUNDED = "turn_right_rounded"
    TURN_RIGHT_OUTLINED = "turn_right_outlined"
    TURN_SHARP_LEFT = "turn_sharp_left"
    TURN_SHARP_LEFT_SHARP = "turn_sharp_left_sharp"
    TURN_SHARP_LEFT_ROUNDED = "turn_sharp_left_rounded"
    TURN_SHARP_LEFT_OUTLINED = "turn_sharp_left_outlined"
    TURN_SHARP_RIGHT = "turn_sharp_right"
    TURN_SHARP_RIGHT_SHARP = "turn_sharp_right_sharp"
    TURN_SHARP_RIGHT_ROUNDED = "turn_sharp_right_rounded"
    TURN_SHARP_RIGHT_OUTLINED = "turn_sharp_right_outlined"
    TURN_SLIGHT_LEFT = "turn_slight_left"
    TURN_SLIGHT_LEFT_SHARP = "turn_slight_left_sharp"
    TURN_SLIGHT_LEFT_ROUNDED = "turn_slight_left_rounded"
    TURN_SLIGHT_LEFT_OUTLINED = "turn_slight_left_outlined"
    TURN_SLIGHT_RIGHT = "turn_slight_right"
    TURN_SLIGHT_RIGHT_SHARP = "turn_slight_right_sharp"
    TURN_SLIGHT_RIGHT_ROUNDED = "turn_slight_right_rounded"
    TURN_SLIGHT_RIGHT_OUTLINED = "turn_slight_right_outlined"
    TURNED_IN = "turned_in"
    TURNED_IN_SHARP = "turned_in_sharp"
    TURNED_IN_ROUNDED = "turned_in_rounded"
    TURNED_IN_OUTLINED = "turned_in_outlined"
    TURNED_IN_NOT = "turned_in_not"
    TURNED_IN_NOT_SHARP = "turned_in_not_sharp"
    TURNED_IN_NOT_ROUNDED = "turned_in_not_rounded"
    TURNED_IN_NOT_OUTLINED = "turned_in_not_outlined"
    TV = "tv"
    TV_SHARP = "tv_sharp"
    TV_ROUNDED = "tv_rounded"
    TV_OUTLINED = "tv_outlined"
    TV_OFF = "tv_off"
    TV_OFF_SHARP = "tv_off_sharp"
    TV_OFF_ROUNDED = "tv_off_rounded"
    TV_OFF_OUTLINED = "tv_off_outlined"
    TWO_WHEELER = "two_wheeler"
    TWO_WHEELER_SHARP = "two_wheeler_sharp"
    TWO_WHEELER_ROUNDED = "two_wheeler_rounded"
    TWO_WHEELER_OUTLINED = "two_wheeler_outlined"
    TYPE_SPECIMEN = "type_specimen"
    TYPE_SPECIMEN_SHARP = "type_specimen_sharp"
    TYPE_SPECIMEN_ROUNDED = "type_specimen_rounded"
    TYPE_SPECIMEN_OUTLINED = "type_specimen_outlined"
    U_TURN_LEFT = "u_turn_left"
    U_TURN_LEFT_SHARP = "u_turn_left_sharp"
    U_TURN_LEFT_ROUNDED = "u_turn_left_rounded"
    U_TURN_LEFT_OUTLINED = "u_turn_left_outlined"
    U_TURN_RIGHT = "u_turn_right"
    U_TURN_RIGHT_SHARP = "u_turn_right_sharp"
    U_TURN_RIGHT_ROUNDED = "u_turn_right_rounded"
    U_TURN_RIGHT_OUTLINED = "u_turn_right_outlined"
    UMBRELLA = "umbrella"
    UMBRELLA_SHARP = "umbrella_sharp"
    UMBRELLA_ROUNDED = "umbrella_rounded"
    UMBRELLA_OUTLINED = "umbrella_outlined"
    UNARCHIVE = "unarchive"
    UNARCHIVE_SHARP = "unarchive_sharp"
    UNARCHIVE_ROUNDED = "unarchive_rounded"
    UNARCHIVE_OUTLINED = "unarchive_outlined"
    UNDO = "undo"
    UNDO_SHARP = "undo_sharp"
    UNDO_ROUNDED = "undo_rounded"
    UNDO_OUTLINED = "undo_outlined"
    UNFOLD_LESS = "unfold_less"
    UNFOLD_LESS_SHARP = "unfold_less_sharp"
    UNFOLD_LESS_ROUNDED = "unfold_less_rounded"
    UNFOLD_LESS_OUTLINED = "unfold_less_outlined"
    UNFOLD_LESS_DOUBLE = "unfold_less_double"
    UNFOLD_LESS_DOUBLE_SHARP = "unfold_less_double_sharp"
    UNFOLD_LESS_DOUBLE_ROUNDED = "unfold_less_double_rounded"
    UNFOLD_LESS_DOUBLE_OUTLINED = "unfold_less_double_outlined"
    UNFOLD_MORE = "unfold_more"
    UNFOLD_MORE_SHARP = "unfold_more_sharp"
    UNFOLD_MORE_ROUNDED = "unfold_more_rounded"
    UNFOLD_MORE_OUTLINED = "unfold_more_outlined"
    UNFOLD_MORE_DOUBLE = "unfold_more_double"
    UNFOLD_MORE_DOUBLE_SHARP = "unfold_more_double_sharp"
    UNFOLD_MORE_DOUBLE_ROUNDED = "unfold_more_double_rounded"
    UNFOLD_MORE_DOUBLE_OUTLINED = "unfold_more_double_outlined"
    UNPUBLISHED = "unpublished"
    UNPUBLISHED_SHARP = "unpublished_sharp"
    UNPUBLISHED_ROUNDED = "unpublished_rounded"
    UNPUBLISHED_OUTLINED = "unpublished_outlined"
    UNSUBSCRIBE = "unsubscribe"
    UNSUBSCRIBE_SHARP = "unsubscribe_sharp"
    UNSUBSCRIBE_ROUNDED = "unsubscribe_rounded"
    UNSUBSCRIBE_OUTLINED = "unsubscribe_outlined"
    UPCOMING = "upcoming"
    UPCOMING_SHARP = "upcoming_sharp"
    UPCOMING_ROUNDED = "upcoming_rounded"
    UPCOMING_OUTLINED = "upcoming_outlined"
    UPDATE = "update"
    UPDATE_SHARP = "update_sharp"
    UPDATE_ROUNDED = "update_rounded"
    UPDATE_OUTLINED = "update_outlined"
    UPDATE_DISABLED = "update_disabled"
    UPDATE_DISABLED_SHARP = "update_disabled_sharp"
    UPDATE_DISABLED_ROUNDED = "update_disabled_rounded"
    UPDATE_DISABLED_OUTLINED = "update_disabled_outlined"
    UPGRADE = "upgrade"
    UPGRADE_SHARP = "upgrade_sharp"
    UPGRADE_ROUNDED = "upgrade_rounded"
    UPGRADE_OUTLINED = "upgrade_outlined"
    UPLOAD = "upload"
    UPLOAD_SHARP = "upload_sharp"
    UPLOAD_ROUNDED = "upload_rounded"
    UPLOAD_OUTLINED = "upload_outlined"
    UPLOAD_FILE = "upload_file"
    UPLOAD_FILE_SHARP = "upload_file_sharp"
    UPLOAD_FILE_ROUNDED = "upload_file_rounded"
    UPLOAD_FILE_OUTLINED = "upload_file_outlined"
    USB = "usb"
    USB_SHARP = "usb_sharp"
    USB_ROUNDED = "usb_rounded"
    USB_OUTLINED = "usb_outlined"
    USB_OFF = "usb_off"
    USB_OFF_SHARP = "usb_off_sharp"
    USB_OFF_ROUNDED = "usb_off_rounded"
    USB_OFF_OUTLINED = "usb_off_outlined"
    VACCINES = "vaccines"
    VACCINES_SHARP = "vaccines_sharp"
    VACCINES_ROUNDED = "vaccines_rounded"
    VACCINES_OUTLINED = "vaccines_outlined"
    VAPE_FREE = "vape_free"
    VAPE_FREE_SHARP = "vape_free_sharp"
    VAPE_FREE_ROUNDED = "vape_free_rounded"
    VAPE_FREE_OUTLINED = "vape_free_outlined"
    VAPING_ROOMS = "vaping_rooms"
    VAPING_ROOMS_SHARP = "vaping_rooms_sharp"
    VAPING_ROOMS_ROUNDED = "vaping_rooms_rounded"
    VAPING_ROOMS_OUTLINED = "vaping_rooms_outlined"
    VERIFIED = "verified"
    VERIFIED_SHARP = "verified_sharp"
    VERIFIED_ROUNDED = "verified_rounded"
    VERIFIED_OUTLINED = "verified_outlined"
    VERIFIED_USER = "verified_user"
    VERIFIED_USER_SHARP = "verified_user_sharp"
    VERIFIED_USER_ROUNDED = "verified_user_rounded"
    VERIFIED_USER_OUTLINED = "verified_user_outlined"
    VERTICAL_ALIGN_BOTTOM = "vertical_align_bottom"
    VERTICAL_ALIGN_BOTTOM_SHARP = "vertical_align_bottom_sharp"
    VERTICAL_ALIGN_BOTTOM_ROUNDED = "vertical_align_bottom_rounded"
    VERTICAL_ALIGN_BOTTOM_OUTLINED = "vertical_align_bottom_outlined"
    VERTICAL_ALIGN_CENTER = "vertical_align_center"
    VERTICAL_ALIGN_CENTER_SHARP = "vertical_align_center_sharp"
    VERTICAL_ALIGN_CENTER_ROUNDED = "vertical_align_center_rounded"
    VERTICAL_ALIGN_CENTER_OUTLINED = "vertical_align_center_outlined"
    VERTICAL_ALIGN_TOP = "vertical_align_top"
    VERTICAL_ALIGN_TOP_SHARP = "vertical_align_top_sharp"
    VERTICAL_ALIGN_TOP_ROUNDED = "vertical_align_top_rounded"
    VERTICAL_ALIGN_TOP_OUTLINED = "vertical_align_top_outlined"
    VERTICAL_DISTRIBUTE = "vertical_distribute"
    VERTICAL_DISTRIBUTE_SHARP = "vertical_distribute_sharp"
    VERTICAL_DISTRIBUTE_ROUNDED = "vertical_distribute_rounded"
    VERTICAL_DISTRIBUTE_OUTLINED = "vertical_distribute_outlined"
    VERTICAL_SHADES = "vertical_shades"
    VERTICAL_SHADES_SHARP = "vertical_shades_sharp"
    VERTICAL_SHADES_ROUNDED = "vertical_shades_rounded"
    VERTICAL_SHADES_OUTLINED = "vertical_shades_outlined"
    VERTICAL_SHADES_CLOSED = "vertical_shades_closed"
    VERTICAL_SHADES_CLOSED_SHARP = "vertical_shades_closed_sharp"
    VERTICAL_SHADES_CLOSED_ROUNDED = "vertical_shades_closed_rounded"
    VERTICAL_SHADES_CLOSED_OUTLINED = "vertical_shades_closed_outlined"
    VERTICAL_SPLIT = "vertical_split"
    VERTICAL_SPLIT_SHARP = "vertical_split_sharp"
    VERTICAL_SPLIT_ROUNDED = "vertical_split_rounded"
    VERTICAL_SPLIT_OUTLINED = "vertical_split_outlined"
    VIBRATION = "vibration"
    VIBRATION_SHARP = "vibration_sharp"
    VIBRATION_ROUNDED = "vibration_rounded"
    VIBRATION_OUTLINED = "vibration_outlined"
    VIDEO_CALL = "video_call"
    VIDEO_CALL_SHARP = "video_call_sharp"
    VIDEO_CALL_ROUNDED = "video_call_rounded"
    VIDEO_CALL_OUTLINED = "video_call_outlined"
    VIDEO_CAMERA_BACK = "video_camera_back"
    VIDEO_CAMERA_BACK_SHARP = "video_camera_back_sharp"
    VIDEO_CAMERA_BACK_ROUNDED = "video_camera_back_rounded"
    VIDEO_CAMERA_BACK_OUTLINED = "video_camera_back_outlined"
    VIDEO_CAMERA_FRONT = "video_camera_front"
    VIDEO_CAMERA_FRONT_SHARP = "video_camera_front_sharp"
    VIDEO_CAMERA_FRONT_ROUNDED = "video_camera_front_rounded"
    VIDEO_CAMERA_FRONT_OUTLINED = "video_camera_front_outlined"
    VIDEO_CHAT = "video_chat"
    VIDEO_CHAT_SHARP = "video_chat_sharp"
    VIDEO_CHAT_ROUNDED = "video_chat_rounded"
    VIDEO_CHAT_OUTLINED = "video_chat_outlined"
    VIDEO_COLLECTION = "video_collection"
    VIDEO_COLLECTION_SHARP = "video_collection_sharp"
    VIDEO_COLLECTION_ROUNDED = "video_collection_rounded"
    VIDEO_COLLECTION_OUTLINED = "video_collection_outlined"
    VIDEO_FILE = "video_file"
    VIDEO_FILE_SHARP = "video_file_sharp"
    VIDEO_FILE_ROUNDED = "video_file_rounded"
    VIDEO_FILE_OUTLINED = "video_file_outlined"
    VIDEO_LABEL = "video_label"
    VIDEO_LABEL_SHARP = "video_label_sharp"
    VIDEO_LABEL_ROUNDED = "video_label_rounded"
    VIDEO_LABEL_OUTLINED = "video_label_outlined"
    VIDEO_LIBRARY = "video_library"
    VIDEO_LIBRARY_SHARP = "video_library_sharp"
    VIDEO_LIBRARY_ROUNDED = "video_library_rounded"
    VIDEO_LIBRARY_OUTLINED = "video_library_outlined"
    VIDEO_SETTINGS = "video_settings"
    VIDEO_SETTINGS_SHARP = "video_settings_sharp"
    VIDEO_SETTINGS_ROUNDED = "video_settings_rounded"
    VIDEO_SETTINGS_OUTLINED = "video_settings_outlined"
    VIDEO_STABLE = "video_stable"
    VIDEO_STABLE_SHARP = "video_stable_sharp"
    VIDEO_STABLE_ROUNDED = "video_stable_rounded"
    VIDEO_STABLE_OUTLINED = "video_stable_outlined"
    VIDEOCAM = "videocam"
    VIDEOCAM_SHARP = "videocam_sharp"
    VIDEOCAM_ROUNDED = "videocam_rounded"
    VIDEOCAM_OUTLINED = "videocam_outlined"
    VIDEOCAM_OFF = "videocam_off"
    VIDEOCAM_OFF_SHARP = "videocam_off_sharp"
    VIDEOCAM_OFF_ROUNDED = "videocam_off_rounded"
    VIDEOCAM_OFF_OUTLINED = "videocam_off_outlined"
    VIDEOGAME_ASSET = "videogame_asset"
    VIDEOGAME_ASSET_SHARP = "videogame_asset_sharp"
    VIDEOGAME_ASSET_ROUNDED = "videogame_asset_rounded"
    VIDEOGAME_ASSET_OUTLINED = "videogame_asset_outlined"
    VIDEOGAME_ASSET_OFF = "videogame_asset_off"
    VIDEOGAME_ASSET_OFF_SHARP = "videogame_asset_off_sharp"
    VIDEOGAME_ASSET_OFF_ROUNDED = "videogame_asset_off_rounded"
    VIDEOGAME_ASSET_OFF_OUTLINED = "videogame_asset_off_outlined"
    VIEW_AGENDA = "view_agenda"
    VIEW_AGENDA_SHARP = "view_agenda_sharp"
    VIEW_AGENDA_ROUNDED = "view_agenda_rounded"
    VIEW_AGENDA_OUTLINED = "view_agenda_outlined"
    VIEW_ARRAY = "view_array"
    VIEW_ARRAY_SHARP = "view_array_sharp"
    VIEW_ARRAY_ROUNDED = "view_array_rounded"
    VIEW_ARRAY_OUTLINED = "view_array_outlined"
    VIEW_CAROUSEL = "view_carousel"
    VIEW_CAROUSEL_SHARP = "view_carousel_sharp"
    VIEW_CAROUSEL_ROUNDED = "view_carousel_rounded"
    VIEW_CAROUSEL_OUTLINED = "view_carousel_outlined"
    VIEW_COLUMN = "view_column"
    VIEW_COLUMN_SHARP = "view_column_sharp"
    VIEW_COLUMN_ROUNDED = "view_column_rounded"
    VIEW_COLUMN_OUTLINED = "view_column_outlined"
    VIEW_COMFORTABLE = "view_comfortable"
    VIEW_COMFORTABLE_SHARP = "view_comfortable_sharp"
    VIEW_COMFORTABLE_ROUNDED = "view_comfortable_rounded"
    VIEW_COMFORTABLE_OUTLINED = "view_comfortable_outlined"
    VIEW_COMFY = "view_comfy"
    VIEW_COMFY_SHARP = "view_comfy_sharp"
    VIEW_COMFY_ROUNDED = "view_comfy_rounded"
    VIEW_COMFY_OUTLINED = "view_comfy_outlined"
    VIEW_COMFY_ALT = "view_comfy_alt"
    VIEW_COMFY_ALT_SHARP = "view_comfy_alt_sharp"
    VIEW_COMFY_ALT_ROUNDED = "view_comfy_alt_rounded"
    VIEW_COMFY_ALT_OUTLINED = "view_comfy_alt_outlined"
    VIEW_COMPACT = "view_compact"
    VIEW_COMPACT_SHARP = "view_compact_sharp"
    VIEW_COMPACT_ROUNDED = "view_compact_rounded"
    VIEW_COMPACT_OUTLINED = "view_compact_outlined"
    VIEW_COMPACT_ALT = "view_compact_alt"
    VIEW_COMPACT_ALT_SHARP = "view_compact_alt_sharp"
    VIEW_COMPACT_ALT_ROUNDED = "view_compact_alt_rounded"
    VIEW_COMPACT_ALT_OUTLINED = "view_compact_alt_outlined"
    VIEW_COZY = "view_cozy"
    VIEW_COZY_SHARP = "view_cozy_sharp"
    VIEW_COZY_ROUNDED = "view_cozy_rounded"
    VIEW_COZY_OUTLINED = "view_cozy_outlined"
    VIEW_DAY = "view_day"
    VIEW_DAY_SHARP = "view_day_sharp"
    VIEW_DAY_ROUNDED = "view_day_rounded"
    VIEW_DAY_OUTLINED = "view_day_outlined"
    VIEW_HEADLINE = "view_headline"
    VIEW_HEADLINE_SHARP = "view_headline_sharp"
    VIEW_HEADLINE_ROUNDED = "view_headline_rounded"
    VIEW_HEADLINE_OUTLINED = "view_headline_outlined"
    VIEW_IN_AR = "view_in_ar"
    VIEW_IN_AR_SHARP = "view_in_ar_sharp"
    VIEW_IN_AR_ROUNDED = "view_in_ar_rounded"
    VIEW_IN_AR_OUTLINED = "view_in_ar_outlined"
    VIEW_KANBAN = "view_kanban"
    VIEW_KANBAN_SHARP = "view_kanban_sharp"
    VIEW_KANBAN_ROUNDED = "view_kanban_rounded"
    VIEW_KANBAN_OUTLINED = "view_kanban_outlined"
    VIEW_LIST = "view_list"
    VIEW_LIST_SHARP = "view_list_sharp"
    VIEW_LIST_ROUNDED = "view_list_rounded"
    VIEW_LIST_OUTLINED = "view_list_outlined"
    VIEW_MODULE = "view_module"
    VIEW_MODULE_SHARP = "view_module_sharp"
    VIEW_MODULE_ROUNDED = "view_module_rounded"
    VIEW_MODULE_OUTLINED = "view_module_outlined"
    VIEW_QUILT = "view_quilt"
    VIEW_QUILT_SHARP = "view_quilt_sharp"
    VIEW_QUILT_ROUNDED = "view_quilt_rounded"
    VIEW_QUILT_OUTLINED = "view_quilt_outlined"
    VIEW_SIDEBAR = "view_sidebar"
    VIEW_SIDEBAR_SHARP = "view_sidebar_sharp"
    VIEW_SIDEBAR_ROUNDED = "view_sidebar_rounded"
    VIEW_SIDEBAR_OUTLINED = "view_sidebar_outlined"
    VIEW_STREAM = "view_stream"
    VIEW_STREAM_SHARP = "view_stream_sharp"
    VIEW_STREAM_ROUNDED = "view_stream_rounded"
    VIEW_STREAM_OUTLINED = "view_stream_outlined"
    VIEW_TIMELINE = "view_timeline"
    VIEW_TIMELINE_SHARP = "view_timeline_sharp"
    VIEW_TIMELINE_ROUNDED = "view_timeline_rounded"
    VIEW_TIMELINE_OUTLINED = "view_timeline_outlined"
    VIEW_WEEK = "view_week"
    VIEW_WEEK_SHARP = "view_week_sharp"
    VIEW_WEEK_ROUNDED = "view_week_rounded"
    VIEW_WEEK_OUTLINED = "view_week_outlined"
    VIGNETTE = "vignette"
    VIGNETTE_SHARP = "vignette_sharp"
    VIGNETTE_ROUNDED = "vignette_rounded"
    VIGNETTE_OUTLINED = "vignette_outlined"
    VILLA = "villa"
    VILLA_SHARP = "villa_sharp"
    VILLA_ROUNDED = "villa_rounded"
    VILLA_OUTLINED = "villa_outlined"
    VISIBILITY = "visibility"
    VISIBILITY_SHARP = "visibility_sharp"
    VISIBILITY_ROUNDED = "visibility_rounded"
    VISIBILITY_OUTLINED = "visibility_outlined"
    VISIBILITY_OFF = "visibility_off"
    VISIBILITY_OFF_SHARP = "visibility_off_sharp"
    VISIBILITY_OFF_ROUNDED = "visibility_off_rounded"
    VISIBILITY_OFF_OUTLINED = "visibility_off_outlined"
    VOICE_CHAT = "voice_chat"
    VOICE_CHAT_SHARP = "voice_chat_sharp"
    VOICE_CHAT_ROUNDED = "voice_chat_rounded"
    VOICE_CHAT_OUTLINED = "voice_chat_outlined"
    VOICE_OVER_OFF = "voice_over_off"
    VOICE_OVER_OFF_SHARP = "voice_over_off_sharp"
    VOICE_OVER_OFF_ROUNDED = "voice_over_off_rounded"
    VOICE_OVER_OFF_OUTLINED = "voice_over_off_outlined"
    VOICEMAIL = "voicemail"
    VOICEMAIL_SHARP = "voicemail_sharp"
    VOICEMAIL_ROUNDED = "voicemail_rounded"
    VOICEMAIL_OUTLINED = "voicemail_outlined"
    VOLCANO = "volcano"
    VOLCANO_SHARP = "volcano_sharp"
    VOLCANO_ROUNDED = "volcano_rounded"
    VOLCANO_OUTLINED = "volcano_outlined"
    VOLUME_DOWN = "volume_down"
    VOLUME_DOWN_SHARP = "volume_down_sharp"
    VOLUME_DOWN_ROUNDED = "volume_down_rounded"
    VOLUME_DOWN_OUTLINED = "volume_down_outlined"
    VOLUME_DOWN_ALT = "volume_down_alt"
    VOLUME_MUTE = "volume_mute"
    VOLUME_MUTE_SHARP = "volume_mute_sharp"
    VOLUME_MUTE_ROUNDED = "volume_mute_rounded"
    VOLUME_MUTE_OUTLINED = "volume_mute_outlined"
    VOLUME_OFF = "volume_off"
    VOLUME_OFF_SHARP = "volume_off_sharp"
    VOLUME_OFF_ROUNDED = "volume_off_rounded"
    VOLUME_OFF_OUTLINED = "volume_off_outlined"
    VOLUME_UP = "volume_up"
    VOLUME_UP_SHARP = "volume_up_sharp"
    VOLUME_UP_ROUNDED = "volume_up_rounded"
    VOLUME_UP_OUTLINED = "volume_up_outlined"
    VOLUNTEER_ACTIVISM = "volunteer_activism"
    VOLUNTEER_ACTIVISM_SHARP = "volunteer_activism_sharp"
    VOLUNTEER_ACTIVISM_ROUNDED = "volunteer_activism_rounded"
    VOLUNTEER_ACTIVISM_OUTLINED = "volunteer_activism_outlined"
    VPN_KEY = "vpn_key"
    VPN_KEY_SHARP = "vpn_key_sharp"
    VPN_KEY_ROUNDED = "vpn_key_rounded"
    VPN_KEY_OUTLINED = "vpn_key_outlined"
    VPN_KEY_OFF = "vpn_key_off"
    VPN_KEY_OFF_SHARP = "vpn_key_off_sharp"
    VPN_KEY_OFF_ROUNDED = "vpn_key_off_rounded"
    VPN_KEY_OFF_OUTLINED = "vpn_key_off_outlined"
    VPN_LOCK = "vpn_lock"
    VPN_LOCK_SHARP = "vpn_lock_sharp"
    VPN_LOCK_ROUNDED = "vpn_lock_rounded"
    VPN_LOCK_OUTLINED = "vpn_lock_outlined"
    VRPANO = "vrpano"
    VRPANO_SHARP = "vrpano_sharp"
    VRPANO_ROUNDED = "vrpano_rounded"
    VRPANO_OUTLINED = "vrpano_outlined"
    WALLET = "wallet"
    WALLET_SHARP = "wallet_sharp"
    WALLET_ROUNDED = "wallet_rounded"
    WALLET_OUTLINED = "wallet_outlined"
    WALLET_GIFTCARD = "wallet_giftcard"
    WALLET_GIFTCARD_SHARP = "wallet_giftcard_sharp"
    WALLET_GIFTCARD_ROUNDED = "wallet_giftcard_rounded"
    WALLET_GIFTCARD_OUTLINED = "wallet_giftcard_outlined"
    WALLET_MEMBERSHIP = "wallet_membership"
    WALLET_MEMBERSHIP_SHARP = "wallet_membership_sharp"
    WALLET_MEMBERSHIP_ROUNDED = "wallet_membership_rounded"
    WALLET_MEMBERSHIP_OUTLINED = "wallet_membership_outlined"
    WALLET_TRAVEL = "wallet_travel"
    WALLET_TRAVEL_SHARP = "wallet_travel_sharp"
    WALLET_TRAVEL_ROUNDED = "wallet_travel_rounded"
    WALLET_TRAVEL_OUTLINED = "wallet_travel_outlined"
    WALLPAPER = "wallpaper"
    WALLPAPER_SHARP = "wallpaper_sharp"
    WALLPAPER_ROUNDED = "wallpaper_rounded"
    WALLPAPER_OUTLINED = "wallpaper_outlined"
    WAREHOUSE = "warehouse"
    WAREHOUSE_SHARP = "warehouse_sharp"
    WAREHOUSE_ROUNDED = "warehouse_rounded"
    WAREHOUSE_OUTLINED = "warehouse_outlined"
    WARNING = "warning"
    WARNING_SHARP = "warning_sharp"
    WARNING_ROUNDED = "warning_rounded"
    WARNING_OUTLINED = "warning_outlined"
    WARNING_AMBER = "warning_amber"
    WARNING_AMBER_SHARP = "warning_amber_sharp"
    WARNING_AMBER_ROUNDED = "warning_amber_rounded"
    WARNING_AMBER_OUTLINED = "warning_amber_outlined"
    WASH = "wash"
    WASH_SHARP = "wash_sharp"
    WASH_ROUNDED = "wash_rounded"
    WASH_OUTLINED = "wash_outlined"
    WATCH = "watch"
    WATCH_SHARP = "watch_sharp"
    WATCH_ROUNDED = "watch_rounded"
    WATCH_OUTLINED = "watch_outlined"
    WATCH_LATER = "watch_later"
    WATCH_LATER_SHARP = "watch_later_sharp"
    WATCH_LATER_ROUNDED = "watch_later_rounded"
    WATCH_LATER_OUTLINED = "watch_later_outlined"
    WATCH_OFF = "watch_off"
    WATCH_OFF_SHARP = "watch_off_sharp"
    WATCH_OFF_ROUNDED = "watch_off_rounded"
    WATCH_OFF_OUTLINED = "watch_off_outlined"
    WATER = "water"
    WATER_SHARP = "water_sharp"
    WATER_ROUNDED = "water_rounded"
    WATER_OUTLINED = "water_outlined"
    WATER_DAMAGE = "water_damage"
    WATER_DAMAGE_SHARP = "water_damage_sharp"
    WATER_DAMAGE_ROUNDED = "water_damage_rounded"
    WATER_DAMAGE_OUTLINED = "water_damage_outlined"
    WATER_DROP = "water_drop"
    WATER_DROP_SHARP = "water_drop_sharp"
    WATER_DROP_ROUNDED = "water_drop_rounded"
    WATER_DROP_OUTLINED = "water_drop_outlined"
    WATERFALL_CHART = "waterfall_chart"
    WATERFALL_CHART_SHARP = "waterfall_chart_sharp"
    WATERFALL_CHART_ROUNDED = "waterfall_chart_rounded"
    WATERFALL_CHART_OUTLINED = "waterfall_chart_outlined"
    WAVES = "waves"
    WAVES_SHARP = "waves_sharp"
    WAVES_ROUNDED = "waves_rounded"
    WAVES_OUTLINED = "waves_outlined"
    WAVING_HAND = "waving_hand"
    WAVING_HAND_SHARP = "waving_hand_sharp"
    WAVING_HAND_ROUNDED = "waving_hand_rounded"
    WAVING_HAND_OUTLINED = "waving_hand_outlined"
    WB_AUTO = "wb_auto"
    WB_AUTO_SHARP = "wb_auto_sharp"
    WB_AUTO_ROUNDED = "wb_auto_rounded"
    WB_AUTO_OUTLINED = "wb_auto_outlined"
    WB_CLOUDY = "wb_cloudy"
    WB_CLOUDY_SHARP = "wb_cloudy_sharp"
    WB_CLOUDY_ROUNDED = "wb_cloudy_rounded"
    WB_CLOUDY_OUTLINED = "wb_cloudy_outlined"
    WB_INCANDESCENT = "wb_incandescent"
    WB_INCANDESCENT_SHARP = "wb_incandescent_sharp"
    WB_INCANDESCENT_ROUNDED = "wb_incandescent_rounded"
    WB_INCANDESCENT_OUTLINED = "wb_incandescent_outlined"
    WB_IRIDESCENT = "wb_iridescent"
    WB_IRIDESCENT_SHARP = "wb_iridescent_sharp"
    WB_IRIDESCENT_ROUNDED = "wb_iridescent_rounded"
    WB_IRIDESCENT_OUTLINED = "wb_iridescent_outlined"
    WB_SHADE = "wb_shade"
    WB_SHADE_SHARP = "wb_shade_sharp"
    WB_SHADE_ROUNDED = "wb_shade_rounded"
    WB_SHADE_OUTLINED = "wb_shade_outlined"
    WB_SUNNY = "wb_sunny"
    WB_SUNNY_SHARP = "wb_sunny_sharp"
    WB_SUNNY_ROUNDED = "wb_sunny_rounded"
    WB_SUNNY_OUTLINED = "wb_sunny_outlined"
    WB_TWIGHLIGHT = "wb_twighlight"
    WB_TWILIGHT = "wb_twilight"
    WB_TWILIGHT_SHARP = "wb_twilight_sharp"
    WB_TWILIGHT_ROUNDED = "wb_twilight_rounded"
    WB_TWILIGHT_OUTLINED = "wb_twilight_outlined"
    WC = "wc"
    WC_SHARP = "wc_sharp"
    WC_ROUNDED = "wc_rounded"
    WC_OUTLINED = "wc_outlined"
    WEB = "web"
    WEB_SHARP = "web_sharp"
    WEB_ROUNDED = "web_rounded"
    WEB_OUTLINED = "web_outlined"
    WEB_ASSET = "web_asset"
    WEB_ASSET_SHARP = "web_asset_sharp"
    WEB_ASSET_ROUNDED = "web_asset_rounded"
    WEB_ASSET_OUTLINED = "web_asset_outlined"
    WEB_ASSET_OFF = "web_asset_off"
    WEB_ASSET_OFF_SHARP = "web_asset_off_sharp"
    WEB_ASSET_OFF_ROUNDED = "web_asset_off_rounded"
    WEB_ASSET_OFF_OUTLINED = "web_asset_off_outlined"
    WEB_STORIES = "web_stories"
    WEB_STORIES_SHARP = "web_stories_sharp"
    WEB_STORIES_ROUNDED = "web_stories_rounded"
    WEB_STORIES_OUTLINED = "web_stories_outlined"
    WEBHOOK = "webhook"
    WEBHOOK_SHARP = "webhook_sharp"
    WEBHOOK_ROUNDED = "webhook_rounded"
    WEBHOOK_OUTLINED = "webhook_outlined"
    WECHAT = "wechat"
    WECHAT_SHARP = "wechat_sharp"
    WECHAT_ROUNDED = "wechat_rounded"
    WECHAT_OUTLINED = "wechat_outlined"
    WEEKEND = "weekend"
    WEEKEND_SHARP = "weekend_sharp"
    WEEKEND_ROUNDED = "weekend_rounded"
    WEEKEND_OUTLINED = "weekend_outlined"
    WEST = "west"
    WEST_SHARP = "west_sharp"
    WEST_ROUNDED = "west_rounded"
    WEST_OUTLINED = "west_outlined"
    WHATSHOT = "whatshot"
    WHATSHOT_SHARP = "whatshot_sharp"
    WHATSHOT_ROUNDED = "whatshot_rounded"
    WHATSHOT_OUTLINED = "whatshot_outlined"
    WHEELCHAIR_PICKUP = "wheelchair_pickup"
    WHEELCHAIR_PICKUP_SHARP = "wheelchair_pickup_sharp"
    WHEELCHAIR_PICKUP_ROUNDED = "wheelchair_pickup_rounded"
    WHEELCHAIR_PICKUP_OUTLINED = "wheelchair_pickup_outlined"
    WHERE_TO_VOTE = "where_to_vote"
    WHERE_TO_VOTE_SHARP = "where_to_vote_sharp"
    WHERE_TO_VOTE_ROUNDED = "where_to_vote_rounded"
    WHERE_TO_VOTE_OUTLINED = "where_to_vote_outlined"
    WIDGETS = "widgets"
    WIDGETS_SHARP = "widgets_sharp"
    WIDGETS_ROUNDED = "widgets_rounded"
    WIDGETS_OUTLINED = "widgets_outlined"
    WIDTH_FULL = "width_full"
    WIDTH_FULL_SHARP = "width_full_sharp"
    WIDTH_FULL_ROUNDED = "width_full_rounded"
    WIDTH_FULL_OUTLINED = "width_full_outlined"
    WIDTH_NORMAL = "width_normal"
    WIDTH_NORMAL_SHARP = "width_normal_sharp"
    WIDTH_NORMAL_ROUNDED = "width_normal_rounded"
    WIDTH_NORMAL_OUTLINED = "width_normal_outlined"
    WIDTH_WIDE = "width_wide"
    WIDTH_WIDE_SHARP = "width_wide_sharp"
    WIDTH_WIDE_ROUNDED = "width_wide_rounded"
    WIDTH_WIDE_OUTLINED = "width_wide_outlined"
    WIFI = "wifi"
    WIFI_SHARP = "wifi_sharp"
    WIFI_ROUNDED = "wifi_rounded"
    WIFI_OUTLINED = "wifi_outlined"
    WIFI_1_BAR = "wifi_1_bar"
    WIFI_1_BAR_SHARP = "wifi_1_bar_sharp"
    WIFI_1_BAR_ROUNDED = "wifi_1_bar_rounded"
    WIFI_1_BAR_OUTLINED = "wifi_1_bar_outlined"
    WIFI_2_BAR = "wifi_2_bar"
    WIFI_2_BAR_SHARP = "wifi_2_bar_sharp"
    WIFI_2_BAR_ROUNDED = "wifi_2_bar_rounded"
    WIFI_2_BAR_OUTLINED = "wifi_2_bar_outlined"
    WIFI_CALLING = "wifi_calling"
    WIFI_CALLING_SHARP = "wifi_calling_sharp"
    WIFI_CALLING_ROUNDED = "wifi_calling_rounded"
    WIFI_CALLING_OUTLINED = "wifi_calling_outlined"
    WIFI_CALLING_3 = "wifi_calling_3"
    WIFI_CALLING_3_SHARP = "wifi_calling_3_sharp"
    WIFI_CALLING_3_ROUNDED = "wifi_calling_3_rounded"
    WIFI_CALLING_3_OUTLINED = "wifi_calling_3_outlined"
    WIFI_CHANNEL = "wifi_channel"
    WIFI_CHANNEL_SHARP = "wifi_channel_sharp"
    WIFI_CHANNEL_ROUNDED = "wifi_channel_rounded"
    WIFI_CHANNEL_OUTLINED = "wifi_channel_outlined"
    WIFI_FIND = "wifi_find"
    WIFI_FIND_SHARP = "wifi_find_sharp"
    WIFI_FIND_ROUNDED = "wifi_find_rounded"
    WIFI_FIND_OUTLINED = "wifi_find_outlined"
    WIFI_LOCK = "wifi_lock"
    WIFI_LOCK_SHARP = "wifi_lock_sharp"
    WIFI_LOCK_ROUNDED = "wifi_lock_rounded"
    WIFI_LOCK_OUTLINED = "wifi_lock_outlined"
    WIFI_OFF = "wifi_off"
    WIFI_OFF_SHARP = "wifi_off_sharp"
    WIFI_OFF_ROUNDED = "wifi_off_rounded"
    WIFI_OFF_OUTLINED = "wifi_off_outlined"
    WIFI_PASSWORD = "wifi_password"
    WIFI_PASSWORD_SHARP = "wifi_password_sharp"
    WIFI_PASSWORD_ROUNDED = "wifi_password_rounded"
    WIFI_PASSWORD_OUTLINED = "wifi_password_outlined"
    WIFI_PROTECTED_SETUP = "wifi_protected_setup"
    WIFI_PROTECTED_SETUP_SHARP = "wifi_protected_setup_sharp"
    WIFI_PROTECTED_SETUP_ROUNDED = "wifi_protected_setup_rounded"
    WIFI_PROTECTED_SETUP_OUTLINED = "wifi_protected_setup_outlined"
    WIFI_TETHERING = "wifi_tethering"
    WIFI_TETHERING_SHARP = "wifi_tethering_sharp"
    WIFI_TETHERING_ROUNDED = "wifi_tethering_rounded"
    WIFI_TETHERING_OUTLINED = "wifi_tethering_outlined"
    WIFI_TETHERING_ERROR = "wifi_tethering_error"
    WIFI_TETHERING_ERROR_SHARP = "wifi_tethering_error_sharp"
    WIFI_TETHERING_ERROR_ROUNDED = "wifi_tethering_error_rounded"
    WIFI_TETHERING_ERROR_OUTLINED = "wifi_tethering_error_outlined"
    WIFI_TETHERING_ERROR_ROUNDED_SHARP = "wifi_tethering_error_rounded_sharp"
    WIFI_TETHERING_ERROR_ROUNDED_ROUNDED = "wifi_tethering_error_rounded_rounded"
    WIFI_TETHERING_ERROR_ROUNDED_OUTLINED = "wifi_tethering_error_rounded_outlined"
    WIFI_TETHERING_OFF = "wifi_tethering_off"
    WIFI_TETHERING_OFF_SHARP = "wifi_tethering_off_sharp"
    WIFI_TETHERING_OFF_ROUNDED = "wifi_tethering_off_rounded"
    WIFI_TETHERING_OFF_OUTLINED = "wifi_tethering_off_outlined"
    WIND_POWER = "wind_power"
    WIND_POWER_SHARP = "wind_power_sharp"
    WIND_POWER_ROUNDED = "wind_power_rounded"
    WIND_POWER_OUTLINED = "wind_power_outlined"
    WINDOW = "window"
    WINDOW_SHARP = "window_sharp"
    WINDOW_ROUNDED = "window_rounded"
    WINDOW_OUTLINED = "window_outlined"
    WINE_BAR = "wine_bar"
    WINE_BAR_SHARP = "wine_bar_sharp"
    WINE_BAR_ROUNDED = "wine_bar_rounded"
    WINE_BAR_OUTLINED = "wine_bar_outlined"
    WOMAN = "woman"
    WOMAN_SHARP = "woman_sharp"
    WOMAN_ROUNDED = "woman_rounded"
    WOMAN_OUTLINED = "woman_outlined"
    WOMAN_2 = "woman_2"
    WOMAN_2_SHARP = "woman_2_sharp"
    WOMAN_2_ROUNDED = "woman_2_rounded"
    WOMAN_2_OUTLINED = "woman_2_outlined"
    WOO_COMMERCE = "woo_commerce"
    WOO_COMMERCE_SHARP = "woo_commerce_sharp"
    WOO_COMMERCE_ROUNDED = "woo_commerce_rounded"
    WOO_COMMERCE_OUTLINED = "woo_commerce_outlined"
    WORDPRESS = "wordpress"
    WORDPRESS_SHARP = "wordpress_sharp"
    WORDPRESS_ROUNDED = "wordpress_rounded"
    WORDPRESS_OUTLINED = "wordpress_outlined"
    WORK = "work"
    WORK_SHARP = "work_sharp"
    WORK_ROUNDED = "work_rounded"
    WORK_OUTLINED = "work_outlined"
    WORK_HISTORY = "work_history"
    WORK_HISTORY_SHARP = "work_history_sharp"
    WORK_HISTORY_ROUNDED = "work_history_rounded"
    WORK_HISTORY_OUTLINED = "work_history_outlined"
    WORK_OFF = "work_off"
    WORK_OFF_SHARP = "work_off_sharp"
    WORK_OFF_ROUNDED = "work_off_rounded"
    WORK_OFF_OUTLINED = "work_off_outlined"
    WORK_OUTLINE = "work_outline"
    WORK_OUTLINE_SHARP = "work_outline_sharp"
    WORK_OUTLINE_ROUNDED = "work_outline_rounded"
    WORK_OUTLINE_OUTLINED = "work_outline_outlined"
    WORKSPACE_PREMIUM = "workspace_premium"
    WORKSPACE_PREMIUM_SHARP = "workspace_premium_sharp"
    WORKSPACE_PREMIUM_ROUNDED = "workspace_premium_rounded"
    WORKSPACE_PREMIUM_OUTLINED = "workspace_premium_outlined"
    WORKSPACES = "workspaces"
    WORKSPACES_SHARP = "workspaces_sharp"
    WORKSPACES_ROUNDED = "workspaces_rounded"
    WORKSPACES_OUTLINED = "workspaces_outlined"
    WORKSPACES_FILLED = "workspaces_filled"
    WORKSPACES_OUTLINE = "workspaces_outline"
    WRAP_TEXT = "wrap_text"
    WRAP_TEXT_SHARP = "wrap_text_sharp"
    WRAP_TEXT_ROUNDED = "wrap_text_rounded"
    WRAP_TEXT_OUTLINED = "wrap_text_outlined"
    WRONG_LOCATION = "wrong_location"
    WRONG_LOCATION_SHARP = "wrong_location_sharp"
    WRONG_LOCATION_ROUNDED = "wrong_location_rounded"
    WRONG_LOCATION_OUTLINED = "wrong_location_outlined"
    WYSIWYG = "wysiwyg"
    WYSIWYG_SHARP = "wysiwyg_sharp"
    WYSIWYG_ROUNDED = "wysiwyg_rounded"
    WYSIWYG_OUTLINED = "wysiwyg_outlined"
    YARD = "yard"
    YARD_SHARP = "yard_sharp"
    YARD_ROUNDED = "yard_rounded"
    YARD_OUTLINED = "yard_outlined"
    YOUTUBE_SEARCHED_FOR = "youtube_searched_for"
    YOUTUBE_SEARCHED_FOR_SHARP = "youtube_searched_for_sharp"
    YOUTUBE_SEARCHED_FOR_ROUNDED = "youtube_searched_for_rounded"
    YOUTUBE_SEARCHED_FOR_OUTLINED = "youtube_searched_for_outlined"
    ZOOM_IN = "zoom_in"
    ZOOM_IN_SHARP = "zoom_in_sharp"
    ZOOM_IN_ROUNDED = "zoom_in_rounded"
    ZOOM_IN_OUTLINED = "zoom_in_outlined"
    ZOOM_IN_MAP = "zoom_in_map"
    ZOOM_IN_MAP_SHARP = "zoom_in_map_sharp"
    ZOOM_IN_MAP_ROUNDED = "zoom_in_map_rounded"
    ZOOM_IN_MAP_OUTLINED = "zoom_in_map_outlined"
    ZOOM_OUT = "zoom_out"
    ZOOM_OUT_SHARP = "zoom_out_sharp"
    ZOOM_OUT_ROUNDED = "zoom_out_rounded"
    ZOOM_OUT_OUTLINED = "zoom_out_outlined"
    ZOOM_OUT_MAP = "zoom_out_map"
    ZOOM_OUT_MAP_SHARP = "zoom_out_map_sharp"
    ZOOM_OUT_MAP_ROUNDED = "zoom_out_map_rounded"
    ZOOM_OUT_MAP_OUTLINED = "zoom_out_map_outlined"
